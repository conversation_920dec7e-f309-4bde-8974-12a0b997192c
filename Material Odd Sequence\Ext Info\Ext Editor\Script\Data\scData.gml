function data_stg_get_star_desc(stg_data, star_num) {
	// stg_data = struct stage details
	
	var result = "";
	
	switch (stg_data.detail[$ string($"star{star_num}")].type) {
		case stage_star_survive:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang("Bertahan hidup", "Survive");
			break;
		case stage_star_intime:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Selesai dalam {stg_data.detail[$ string($"star{star_num}")].val / 60} menit"), string($"Cleared within {stg_data.detail[$ string($"star{star_num}")].val / 60} minutes"));
			break;
		case stage_star_overtime:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Selesai setelah {stg_data.detail[$ string($"star{star_num}")].val / 60} menit"), string($"Cleared after {stg_data.detail[$ string($"star{star_num}")].val / 60} minutes"));
			break;
		case stage_star_noloss:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(((stg_data.detail[$ string($"star{star_num}")].val == 0) ? "Tidak ada rekan tim yang dikalahkan" : string($"Tidak kehilangan lebih dari {stg_data.detail[$ string($"star{star_num}")].val} rekan tim")), 
									((stg_data.detail[$ string($"star{star_num}")].val == 0) ? "No teammates were defeated" : string($"Not losing more than {stg_data.detail[$ string($"star{star_num}")].val} teammates")));
			break;
		case stage_star_nodmg:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(((stg_data.detail[$ string($"star{star_num}")].val == 0) ? "Tidak menerima kerusakan HP dari musuh" : ("Tidak menerima kerusakan HP lebih dari " + formatted_number(stg_data.detail[$ string($"star{star_num}")].val))), 
									((stg_data.detail[$ string($"star{star_num}")].val == 0) ? "Take no HP damage from enemies" : ("Not getting more than " + formatted_number(stg_data.detail[$ string($"star{star_num}")].val) + " HP damage")));
			break;
		case stage_star_noskill:
			var skill_title = ["", switch_lang("Serangan Dasar", "Basic Attacks"), switch_lang("Skill Turunan", "Derivative Skills"), switch_lang("Gerakan Spesial", "Special Moves"), switch_lang("Skill Ultimate", "Ultimate Skills"), "", "", switch_lang("Skill Ekstra", "Extra Skills")];
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Tidak menggunakan {skill_title[stg_data.detail[$ string($"star{star_num}")].val]} apapun"), string($"Not using any {skill_title[stg_data.detail[$ string($"star{star_num}")].val]}"));
			break;
		case stage_star_botcntless:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(((stg_data.detail[$ string($"star{star_num}")].val == 1) ? "Hanya menggunakan 1 kelas Bot" : string($"Tidak menggunakan lebih dari {stg_data.detail[$ string($"star{star_num}")].val} kelas Bot")), 
									((stg_data.detail[$ string($"star{star_num}")].val == 1) ? "Only used 1 Bots class" : string($"Not using more than {stg_data.detail[$ string($"star{star_num}")].val} Bots classes")));
			break;
		case stage_star_botcntmore:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(((stg_data.detail[$ string($"star{star_num}")].val == 1) ? "Menggunakan setidaknya 1 kelas Bot" : string($"Menggunakan setidaknya {stg_data.detail[$ string($"star{star_num}")].val} kelas Bot")), 
									((stg_data.detail[$ string($"star{star_num}")].val == 1) ? "Have used at least 1 Bots class" : string($"Use at least {stg_data.detail[$ string($"star{star_num}")].val} Bots classes")));
			break;
		case stage_star_bothp:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Rata-rata HP rekan tim {stg_data.detail[$ string($"star{star_num}")].val}% atau lebih tinggi"), string($"Average HP of teammates is {stg_data.detail[$ string($"star{star_num}")].val}% or higher"));
			break;
		case stage_star_botclass:
			var class_arr = ["", switch_lang(string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]} berada di dalam tim"), string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]} is in the team")), 
									switch_lang(string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]} dan {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]} berada di dalam tim"), string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]} and {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]} are in the team")), 
									switch_lang(string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]}, {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]}, dan {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]} berada di dalam tim"), string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]}, {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]}, and {class_name[stg_data.detail[$ string($"star{star_num}")].val[2]]} are in the team"))];
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : class_arr[array_length(stg_data.detail[$ string($"star{star_num}")].val)];
			break;
		case stage_star_botnoclass:
			var class_arr = ["", switch_lang(string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]} tidak berada di dalam tim"), string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]} isn't in the team")), 
									switch_lang(string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]} dan {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]} tidak berada di dalam tim"), string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]} and {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]} aren't in the team")), 
									switch_lang(string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]}, {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]}, dan {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]} tidak berada di dalam tim"), string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]}, {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]}, and {class_name[stg_data.detail[$ string($"star{star_num}")].val[2]]} aren't in the team"))];
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : class_arr[array_length(stg_data.detail[$ string($"star{star_num}")].val)];
			break;
		case stage_star_botonlyclass:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Selesai hanya dengan kelas {stg_data.detail[$ string($"star{star_num}")].val}"), string($"Cleared only by {stg_data.detail[$ string($"star{star_num}")].val} class"));
			break;
		case stage_star_bottype:
			var title_arr = ["", switch_lang("Serba Bisa", "All-Rounder"), switch_lang("Penyerang", "Attacker"), switch_lang("Penjaga", "Defender"), switch_lang("Penyembuh", "Healer"), switch_lang("Pendukung", "Supporter"), switch_lang("Spesialis", "Specialist")];
			var class_arr = ["", switch_lang(string($"Bot dengan peran {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]} berada di dalam tim"), string($"Bots with {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]} role is in the team")), 
									switch_lang(string($"Bot dengan peran {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]} dan {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]} berada di dalam tim"), string($"Bots with {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]} and {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]} role are in the team")), 
									switch_lang(string($"Bot dengan peran {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]}, {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]}, dan {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]} berada di dalam tim"), string($"Bots with {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]}, {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]}, and {title_arr[stg_data.detail[$ string($"star{star_num}")].val[2]]} role are in the team"))];
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : class_arr[array_length(stg_data.detail[$ string($"star{star_num}")].val)];
			break;
		case stage_star_botnotype:
			var title_arr = ["", switch_lang("Serba Bisa", "All-Rounder"), switch_lang("Penyerang", "Attacker"), switch_lang("Penjaga", "Defender"), switch_lang("Penyembuh", "Healer"), switch_lang("Pendukung", "Supporter"), switch_lang("Spesialis", "Specialist")];
			var class_arr = ["", switch_lang(string($"Bot dengan peran {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]} tidak berada di dalam tim"), string($"Bots with {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]} role isn't in the team")), 
									switch_lang(string($"Bot dengan peran {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]} dan {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]} tidak berada di dalam tim"), string($"Bots with {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]} and {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]} role aren't in the team")), 
									switch_lang(string($"Bot dengan peran {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]}, {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]}, dan {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]} tidak berada di dalam tim"), string($"Bots with {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]}, {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]}, and {title_arr[stg_data.detail[$ string($"star{star_num}")].val[2]]} role aren't in the team"))];
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : class_arr[array_length(stg_data.detail[$ string($"star{star_num}")].val)];
			break;
		case stage_star_botonlytype:
			var title_arr = ["", switch_lang("Serba Bisa", "All-Rounder"), switch_lang("Penyerang", "Attacker"), switch_lang("Penjaga", "Defender"), switch_lang("Penyembuh", "Healer"), switch_lang("Pendukung", "Supporter"), switch_lang("Spesialis", "Specialist")];
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Selesai hanya dengan Bot yang berperan {title_arr[stg_data.detail[$ string($"star{star_num}")].val]}"), string($"Cleared only by Bots with {title_arr[stg_data.detail[$ string($"star{star_num}")].val]} role"));
			break;
		case stage_star_enemydefeat:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Selesai setelah mengalahkan {stg_data.detail[$ string($"star{star_num}")].val} musuh"), string($"Cleared after defeating {stg_data.detail[$ string($"star{star_num}")].val} enemies"));
			break;
		case stage_star_enemytarget:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Selesai setelah mengalahkan target"), string($"Cleared after defeating the target"));
			break;
		case stage_star_protect:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Lindungi target dari serangan musuh"), string($"Protect the target from enemy attacks"));
			break;
		case stage_star_destroy:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Hancurkan semua target"), string($"Destroy all targets"));
			break;
		case stage_star_retrieve:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Ambil semua target"), string($"Retrieve all targets"));
			break;
		case stage_star_capture:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Ambil target dan membawanya ke lokasi tujuan"), string($"Pick up the target and take it to the destination"));
			break;
		case stage_star_occupy:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Ambil alih dan pertahankan lokasi tujuan"), string($"Take over and defend the destination"));
			break;
		case stage_star_escort:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Kawal dan lindungi target sampai ke lokasi tujuan"), string($"Escort and protect the target to its destination"));
			break;
		case stage_star_rescue:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Selamatkan target dan membawanya ke lokasi tujuan"), string($"Rescue the target and escort it to the destination"));
			break;
	}
	
	return result;
}

function data_to_hash(struct_data) {
	// struct_data = struct to be hashed
	var str_names = struct_get_names(struct_data);
	
	for (var i = 0; i < array_length(str_names); i++) {
		if (str_names[i] != "index") {
			struct_remove(struct_data, str_names[i]);
			continue;
		}

		if (is_struct(struct_data[$ str_names[i]])) {
			data_to_hash(struct_data[$ str_names[i]]);
		} 

		struct_set_from_hash(struct_data, variable_get_hash(str_names[i]), struct_data[$ str_names[i]]);
		struct_remove(struct_data, str_names[i]);
	}
}

function bots_stats() constructor {
	hp_scale : {num: 1, type: "percent", val: 100},
	atk_scale : {num: 2, type: "percent", val: 100},
	def_scale : {num: 3, type: "percent", val: 100},
	hp_flat : {num: 1, type: "flat", val: 0},
	atk_flat : {num: 2, type: "flat", val: 0},
	def_flat : {num: 3, type: "flat", val: 0},
	agility : {num: 4, val: 100},
	dmg_output : {num: 5, val: 100},
	ignore_interruption : {num: 6, val: 0},
	cd_reduction : {num: 7, val: 0},
	crit_buildup : {num: 8, val: 0},
	crit_damage : {num: 9, val: 50},
	crit_protection : {num: 10, val: 0},
	healing_output : {num: 11, val: 100},
	atk_spd : {num: 12, val: 100},
	melee_do : {num: 13, val: 0},
	ranged_do : {num: 14, val: 0},
	aoe_dmg_scale : {num: 15, val: 20},
	physical_dmg_bonus : {num: 16, val: 0},
	physical_do : {num: 17, val: 0},
	dmg_reduction : {num: 18, val: 0},
	acidity_bonus : {num: 19, val: 0},
	acid_do : {num: 20, val: 0},
	tc : {num: 21, val: 0},
	def_penetration : {num: 22, val: 0},
	ap : {num: 23, val: 0},
	ab : {num: 24, val: 0},
	armor_burst : {num: 25, val: 50},
	armor_str : {num: 26, val: 100},
	dmg_res : {num: 27, val: 40},
	cc_power : {num: 28, val: 100},
	cc_res : {num: 29, val: 0},
	buff_power : {num: 30, val: 100},
	debuff_res : {num: 31, val: 0},
	charge_spd : {num: 32, val: 100},
	batk_db : {num: 33, val: 0},
	batk_do : {num: 34, val: 0},
	batk_power : {num: 35, val: 0},
	batk_eff : {num: 36, val: 0},
	derv_db : {num: 37, val: 0},
	derv_do : {num: 38, val: 0},
	derv_power : {num: 39, val: 0},
	derv_eff : {num: 40, val: 0},
	spmv_db : {num: 41, val: 0},
	spmv_do : {num: 42, val: 0},
	spmv_power : {num: 43, val: 0},
	spmv_eff : {num: 44, val: 0},
	ulti_db : {num: 45, val: 0},
	ulti_do : {num: 46, val: 0},
	ulti_power : {num: 47, val: 0},
	ulti_eff : {num: 48, val: 0},
	intg_db : {num: 49, val: 0},
	intg_do : {num: 50, val: 0},
	intg_power : {num: 51, val: 0},
	intg_eff : {num: 52, val: 0},
	accuracy : {num: 53, val: 0},
	recoil_reduction : {num: 54, val: 0},
	ammo_cap : {num: 55, val: 0},
	mags_cap : {num: 56, val: 0},
	reload_spd : {num: 57, val: 0}
}

function bots_stats_method() constructor {
	static get_val = function(key) {
		return struct_get_from_hash(self, variable_get_hash(key));
	};
	static set_val = function(key, val) {
		struct_set_from_hash(self, variable_get_hash(key), val);
	};
}

function data_bots_stats_tohash(stats_struct) {
	// stats_struct = stats to be hashed
	var str_names = struct_get_names(stats_struct);
	var new_stats = new bots_stats_method();
	
	var old_key_num = [
		stats_list.ignore_interruption, stats_list.cd_reduction, stats_list.crit_buildup, stats_list.crit_damage, stats_list.crit_protection, 
		stats_list.healing_output, stats_list.aoe_dmg_scale, stats_list.physical_dmg_bonus, stats_list.physical_do, stats_list.dmg_reduction, 
		stats_list.acidity_bonus, stats_list.acid_do, stats_list.tc, stats_list.def_penetration, stats_list.ap, stats_list.ab, stats_list.recoil_reduction
	];
	var new_key = [
		"ignore_int", "cd_rdc", "crit_bld", "crit_dmg", "crit_prt", "heal_otp", 
		"aoe_dmg", "physical_db", "physical_do", "physical_rdc", "acid_db", 
		"acid_do", "acid_rdc", "def_pen", "armor_pen", "armor_dmg", "recoil_rdc"
	];

	for (var i = 0; i < array_length(str_names); i++) {
		struct_set_from_hash(new_stats, ((array_contain(old_key_num, stats_struct[$ str_names[i]].num)) ? variable_get_hash(new_key[array_get_index(old_key_num, stats_struct[$ str_names[i]].num)]) : variable_get_hash(str_names[i])), stats_struct[$ str_names[i]].val);
	}

	stats_struct = new_stats;
	delete stats_struct;
}

function Vec2(_x = 0, _y = 0) constructor {
	x = x;
	y = y;

	static add = function(_x = 0, _y = 0) {
		x += _x;
		y += _y;
	};

	static sub = function(_x = 0, _y = 0) {
		x -= _x;
		y -= _y;
	};

	static mul = function(_x = 1, _y = 1) {
		x *= _x;
		y *= _y;
	};

	static divide = function(_x = 1, _y = 1) {
		x /= _x;
		y /= _y;
	};
}

function Vec3(_x = 0, _y = 0, _z = 0) : Vec2(_x, _y) constructor {
	z = _z;

	static add = function(_x = 0, _y = 0, _z = 0) {
		x += _x;
		y += _y;
		z += _z;
	};

	static sub = function(_x = 0, _y = 0, _z = 0) {
		x -= _x;
		y -= _y;
		z -= _z;
	};

	static mul = function(_x = 1, _y = 1, _z = 1) {
		x *= _x;
		y *= _y;
		z *= _z;
	};

	static divide = function(_x = 1, _y = 1, _z = 1) {
		x /= _x;
		y /= _y;
		z /= _z;
	};
}

function Vec4(_x = 0, _y = 0, _w = 0, _h = 0) : Vec2(_x, _y) constructor {
	w = _w;
	h = _h;

	static add = function(_x = 0, _y = 0, _w = 0, _h = 0) {
		x += _x;
		y += _y;
		w += _w;
		h += _h;
	};

	static sub = function(_x = 0, _y = 0, _w = 0, _h = 0) {
		x -= _x;
		y -= _y;
		w += _w;
		h += _h;
	};

	static mul = function(_x = 1, _y = 1, _w = 1, _h = 1) {
		x *= _x;
		y *= _y;
		w *= _w;
		h *= _h;
	};

	static divide = function(_x = 1, _y = 1, _w = 1, _h = 1) {
		x /= _x;
		y /= _y;
		w /= _w;
		h /= _h;
	};
}
