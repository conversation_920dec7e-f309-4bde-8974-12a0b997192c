1. Warrior
  Base Max HP = 1000 + (level*100) + (floor(level/5)*6)*(level*10)
    Base Max HP (Level 1)  = 1100
    Base Max HP (Level 50) = 36000
  Base ATK = 90 + (level*5) + (floor(level/5)*5)*level
    Base ATK (Level 1) = 95
    Base ATK (Level 50) = 2840 (+2500 = 5340)
  Base DEF = 130 + (level*9) + (floor(level/5)*6)*level
    Base DEF (Level 1) = 139
    Base DEF (Level 50) = 3580
  Base Melee Damage Output = floor(level/10)*3
    Base Melee Damage Output (Level 1) = 0%
    Base Melee Damage Output (Level 50) = 15%
  Total CP (Level 50) = 12000 + 17800 + 10229 = 40029
  Main IMG-Bot Bonus = 10% Melee Damage Output

2. Archer
  Base Max HP = 800 + (level*100) + (floor(level/5)*4,3)*(level*10)
    Base Max HP (Level 1)  = 900
    Base Max HP (Level 50) = 27300
  Base ATK = 110 + (level*4) + (floor(level/5)*3)*level
    Base ATK (Level 1) = 114
    Base ATK (Level 50) = 1810 (+5000 = 6810)
  Base DEF = 90 + (level*6) + (floor(level/5)*5)*level
    Base DEF (Level 1) = 96
    Base DEF (Level 50) = 2890
  Base Ranged Damage Output = floor(level/10)*3
    Base Ranged Damage Output (Level 1) = 0%
    Base Ranged Damage Output (Level 50) = 15%
  Total CP (Level 50) = 9100 + 22700 + 8257 = 40057
  Main IMG-Bot Bonus = 10% Ranged Damage Output

3. Medic
  Base Max HP = 900 + (level*90) + (floor(level/5)*6,8)*(level*10)
    Base Max HP (Level 1)  = 990
    Base Max HP (Level 50) = 39400
  Base ATK = 70 + (level*5) + (floor(level/5)*2,82)*level
    Base ATK (Level 1) = 75
    Base ATK (Level 50) = 1730 (+3400 = 5130)
  Base DEF = 120 + (level*8) + (floor(level/5)*5,8)*level
    Base DEF (Level 1) = 128
    Base DEF (Level 50) = 3420
  Base Healing Output = floor(level/10)*5
    Base Healing Output (Level 1) = 0%
    Base Healing Output (Level 50) = 25%
  Total CP (Level 50) = 13133 + 17100 + 9771 = 40004
  Main IMG-Bot Bonus = 5% Healing Output & 5% Damage Reduction

Engineer
  Base Max HP = 850 + (level*85) + (floor(level/5,3)*4,5)*(level*10)
    Base Max HP (Level 1)  = 935
    Base Max HP (Level 50) = 31600
  Base ATK = 80 + (level*6) + (floor(level/5)*4,5)*level
    Base ATK (Level 1) = 86
    Base ATK (Level 50) = 2630 (+2600 = 5230)
  Base DEF = 130 + (level*6) + (floor(level/5)*7,6)*level
    Base DEF (Level 1) = 136
    Base DEF (Level 50) = 4230
  Base Teflon Coating = floor(level/10)*4
    Base Teflon Coating (Level 1) = 0%
    Base Teflon Coating (Level 50) = 20%
  Total CP (Level 50) = 10533 + 17433 + 12086 = 40,052

Defender
  Base Max HP = 1200 + (level*110) + (floor(level/5)*7)*(level*10)
    Base Max HP (Level 1)  = 1310
    Base Max HP (Level 50) = 41700
  Base ATK = 60 + (level*4) + (floor(level/5)*4)*level
    Base ATK (Level 1) = 64
    Base ATK (Level 50) = 2260 (+2000 = 4260)
  Base DEF = 150 + (level*6) + (floor(level/5)*7,5)*level
    Base DEF (Level 1) = 156
    Base DEF (Level 50) = 4200
  Base Damage Reduction = floor(level/10)*4
    Base Damage Reduction (Level 1) = 0%
    Base Damage Reduction (Level 50) = 20%
  Total CP (Level 50) = 13900 + 14200 + 12000 = 40100

Berserker
  Base Max HP = 900 + (level*90) + (floor(level/5)*6)*(level*10)
    Base Max HP (Level 1)  = 990
    Base Max HP (Level 50) = 35400
  Base ATK = 120 + (level*7) + (floor(level/5)*5,5)*level
    Base ATK (Level 1) = 127
    Base ATK (Level 50) = 3220 (+3000 = 6220)
  Base DEF = 130 + (level*7) + (floor(level/5)*4,3)*level
    Base DEF (Level 1) = 137
    Base DEF (Level 50) = 2630
  Base Ignore Interruption = floor(level/10)*4
    Base Ignore Interruption (Level 1) = 0%
    Base Ignore Interruption (Level 50) = 20%
  Total CP (Level 50) = 11800 + 20733 + 7514 = 40047

Lancer
  Base Max HP = 900 + (level*90) + (floor(level/5)*5,5)*(level*10)
    Base Max HP (Level 1)  = 990
    Base Max HP (Level 50) = 32900
  Base ATK = 90 + (level*5) + (floor(level/5)*5)*level
    Base ATK (Level 1) = 95
    Base ATK (Level 50) = 2840 (+3000 = 5840)
  Base DEF = 130 + (level*7) + (floor(level/5)*5,8)*level
    Base DEF (Level 1) = 136
    Base DEF (Level 50) = 3380
  Base Physical Damage Bonus = floor(level/10)*10
    Base Physical Damage Bonus (Level 1) = 0%
    Base Physical Damage Bonus (Level 50) = 50%
  Total CP (Level 50) = 10967 + 19467 + 9657 = 40091

Gunslinger
  Base Max HP = 800 + (level*65) + (floor(level/5)*6)*(level*10)
    Base Max HP (Level 1)  = 865
    Base Max HP (Level 50) = 34100
  Base ATK = 110 + (level*5) + (floor(level/5)*5,5)*level
    Base ATK (Level 1) = 115
    Base ATK (Level 50) = 3110 (+3400 = 6510)
  Base DEF = 100 + (level*7) + (floor(level/5)*4)*level
    Base DEF (Level 1) = 107
    Base DEF (Level 50) = 2450
  Base Critical Buildup = floor(level/10)*3
    Base Critical Buildup (Level 1) = 0%
    Base Critical Buildup (Level 50) = 15%
  Total CP (Level 50) = 11367 + 21700 + 7000 = 40,067

Guardian
  Base Max HP = 1100 + (level*100) + (floor(level/5)*6,5)*(level*10)
    Base Max HP (Level 1)  = 1200
    Base Max HP (Level 50) = 38600
  Base ATK = 80 + (level*5) + (floor(level/5)*3,7)*level
    Base ATK (Level 1) = 85
    Base ATK (Level 50) = 2180 (+2500 = 4680)
  Base DEF = 140 + (level*6) + (floor(level/5)*7,2)*level
    Base DEF (Level 1) = 146
    Base DEF (Level 50) = 4040
  Base Super Armor Strength = floor(level/10)*6
    Base Super Armor Strength (Level 1) = 0%
    Base Super Armor Strength (Level 50) = 30%
  Total CP (Level 50) = 12867 + 15600 + 11542 = 40009

Vanguard
  Base Max HP = 900 + (level*90) + (floor(level/5)*5,2)*(level*10)
    Base Max HP (Level 1)  = 990
    Base Max HP (Level 50) = 31400
  Base ATK = 100 + (level*5) + (floor(level/5)*5,6)*level
    Base ATK (Level 1) = 105
    Base ATK (Level 50) = 3150 (+2500 = 5650)
  Base DEF = 130 + (level*6) + (floor(level/5)*6,7)*level
    Base DEF (Level 1) = 115
    Base DEF (Level 50) = 3780
  Base Agility = floor(level/10)*10
    Base Agility (Level 1) = 0%
    Base Agility (Level 50) = 50%
  Total CP (Level 50) = 10467 + 18833 + 10800 = 40100

Marksman
  Base Max HP = 800 + (level*80) + (floor(level/5)*4,7)*(level*10)
    Base Max HP (Level 1)  = 880
    Base Max HP (Level 50) = 28300
  Base ATK = 110 + (level*7) + (floor(level/5)*5)*level
    Base ATK (Level 1) = 117
    Base ATK (Level 50) = 2960 (+3500 = 6460)
  Base DEF = 90 + (level*7) + (floor(level/5)*5,5)*level
    Base DEF (Level 1) = 97
    Base DEF (Level 50) = 3190
  Base Recoil Reduction = floor(level/10)*4
    Base Recoil Reduction (Level 1) = 0%
    Base Recoil Reduction (Level 50) = 20%
  Total CP (Level 50) = 9433 + 21533 + 9114 = 40080

Rearguard
  Base Max HP = 800 + (level*80) + (floor(level/5)*5)*(level*10)
    Base Max HP (Level 1)  = 880
    Base Max HP (Level 50) = 29800
  Base ATK = 100 + (level*4,8) + (floor(level/5)*3,6)*level
    Base ATK (Level 1) = 105
    Base ATK (Level 50) = 2140 (+4000 = 6510)
  Base DEF = 90 + (level*7) + (floor(level/5)*5,9)*level
    Base DEF (Level 1) = 97
    Base DEF (Level 50) = 3390
  Base Attack Speed = floor(level/10)*3
    Base Attack Speed (Level 1) = 0%
    Base Attack Speed (Level 50) = 15%
  Total CP (Level 50) = 9933 + 20467 + 9686 = 40086

Recon
  Base Max HP = 500 + (level*60) + (floor(level/5)*4,5)*(level*10)
    Base Max HP (Level 1)  = 560
    Base Max HP (Level 50) = 26000
  Base ATK = 130 + (level*5) + (floor(level/5)*3,3)*level
    Base ATK (Level 1) = 135
    Base ATK (Level 50) = 2030 (+5600 = 7630)
  Base DEF = 70 + (level*5) + (floor(level/5)*3,6)*level
    Base DEF (Level 1) = 75
    Base DEF (Level 50) = 2120
  Base Armor Penetration = floor(level/10)*7
    Base Armor Penetration (Level 1) = 0%
    Base Armor Penetration (Level 50) = 35%
  Total CP (Level 50) = 8667 + 25433 + 6057 = 40157

Assassin
  Base Max HP = 900 + (level*80) + (floor(level/5)*5)*(level*10)
    Base Max HP (Level 1)  = 980
    Base Max HP (Level 50) = 29900
  Base ATK = 120 + (level*7) + (floor(level/5)*5,4)*level
    Base ATK (Level 1) = 127
    Base ATK (Level 50) = 3170 (+3000 = 6170)
  Base DEF = 90 + (level*5) + (floor(level/5)*6)*level
    Base DEF (Level 1) = 95
    Base DEF (Level 50) = 3340
  Base Critical Damage = floor(level/10)*15
    Base Critical Damage (Level 1) = 0%
    Base Critical Damage (Level 50) = 75%
  Total CP (Level 50) =  9967 + 20567 + 9543 = 40077

Alchemist
  Base Max HP = 800 + (level*90) + (floor(level/5)*4,5)*(level*10)
    Base Max HP (Level 1)  = 890
    Base Max HP (Level 50) = 27800
  Base ATK = 110 + (level*8) + (floor(level/5)*2,4)*level
    Base ATK (Level 1) = 118
    Base ATK (Level 50) = 1700 (+4600 = 6300)
  Base DEF = 90 + (level*7) + (floor(level/5)*6)*level
    Base DEF (Level 1) = 97
    Base DEF (Level 50) = 3440
  Base Acidity Bonus = floor(level/10)*10
    Base Acidity Bonus (Level 1) = 0%
    Base Acidity Bonus (Level 50) = 50%
  Total CP (Level 50) = 9267 + 21000 + 9829 = 40096

Fencer
  Base Max HP = 1000 + (level*90) + (floor(level/5)*5,5)*(level*10)
    Base Max HP (Level 1)  = 1090
    Base Max HP (Level 50) = 33000
  Base ATK = 90 + (level*4) + (floor(level/5)*5)*level
    Base ATK (Level 1) = 95
    Base ATK (Level 50) = 2790 (+2600 = 5390)
  Base DEF = 130 + (level*10) + (floor(level/5)*6,5)*level
    Base DEF (Level 1) = 140
    Base DEF (Level 50) = 3880
  Base Cooldown Reduction = floor(level/10)*2
    Base Cooldown Reduction (Level 1) = 0%
    Base Cooldown Reduction (Level 50) = 10%
  Total CP (Level 50) = 11000 + 17967 + 11086 = 40053

Enforcer
  Base Max HP = 1000 + (level*90) + (floor(level/5)*5)*(level*10)
    Base Max HP (Level 1)  = 1090
    Base Max HP (Level 50) = 30500
  Base ATK = 80 + (level*5) + (floor(level/5)*4,5)*level
    Base ATK (Level 1) = 85
    Base ATK (Level 50) = 2580 (+3000 = 5580)
  Base DEF = 90 + (level*7) + (floor(level/5)*7)*level
    Base DEF (Level 1) = 97
    Base DEF (Level 50) = 3940
  Base Charge Speed = floor(level/10)*4
    Base Charge Speed (Level 1) = 0%
    Base Charge Speed (Level 50) = 20%
  Total CP (Level 50) = 10167 + 18600 + 11257 = 40024

Eliminator
  Base Max HP = 1000 + (level*90) + (floor(level/5)*5)*(level*10)
    Base Max HP (Level 1)  = 1090
    Base Max HP (Level 50) = 30500
  Base ATK = 120 + (level*8) + (floor(level/5)*6)*level
    Base ATK (Level 1) = 128
    Base ATK (Level 50) = 3520 (+3500 = 7020)
  Base DEF = 90 + (level*4) + (floor(level/5)*4)*level
    Base DEF (Level 1) = 94
    Base DEF (Level 50) = 2290
  Base Fatal Buildup = floor(level/10)*2
    Base Fatal Buildup (Level 1) = 0%
    Base Fatal Buildup (Level 50) = 10%
  Total CP (Level 50) = 10167 + 23400 + 6543 = 40110
