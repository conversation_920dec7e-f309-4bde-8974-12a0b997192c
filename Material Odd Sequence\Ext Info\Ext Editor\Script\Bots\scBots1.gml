function bots_get_all_stats(bots_stat) {
	var filtered_stats = {};
	var str_names = struct_get_names(bots_stat);
	
	for (var i = 0; i < array_length(str_names); i++) {
		if (str_names[i] != "main_stats" && str_names[i] != "cp") {
			filtered_stats[$ str_names[i]] = bots_stat[$ str_names[i]];
		}
	}
	
	return filtered_stats;
}

function bots_get_stats_val(bots_stat, find_num, ext = "real", filtered = false) {
	// filtered = stat_val - 100
	var result = 0;
	var filtered_stats = bots_get_all_stats(bots_stat);
	var str_names = struct_get_names(filtered_stats);
	
	if (find_num > 3) {
		for (var i = 0; i < array_length(str_names); i++) {
			if (filtered_stats[$ str_names[i]].num == find_num) {
				result = filtered_stats[$ str_names[i]].val;
				if (filtered) {
					if (result >= 100) {
						switch (filtered_stats[$ str_names[i]].num) {
							case 4:
							case 5:
							case 11:
							case 12:
							case 26:
							case 28:
							case 30:
							case 32:
								result -= 100;
								break;
						}
					}
				}
				break;
			}
		}
	} else {
		switch (ext) {
			case "real":
				switch (find_num) {
					case 1: result = filtered_stats.hp.val; break;
					case 2: result = filtered_stats.atk.val; break;
					case 3: result = filtered_stats.def.val; break;
				}
				break;
			case "flat":
				switch (find_num) {
					case 1: result = filtered_stats.hp_flat.val; break;
					case 2: result = filtered_stats.atk_flat.val; break;
					case 3: result = filtered_stats.def_flat.val; break;
				}
				break;
			case "scale":
				switch (find_num) {
					case 1: result = filtered_stats.hp_scale.val; break;
					case 2: result = filtered_stats.atk_scale.val; break;
					case 3: result = filtered_stats.def_scale.val; break;
				}
				break;
		}
	}
	
	return result;
}

function bots_get_team(team_type) {
	var result = {class: [], init: 0};
	var key = "";
	
	switch (team_type) {
		case bots_team_lastused: key = "last"; break;
	}
	
	file_text_decrypt("general.osq");
	for (var i = 0; i < 3; i++) {
		array_push(result.class, ini_read_real("team", string($"{key}{i+1}"), 0));
	}
	result.init = ini_read_real("team", string($"{key}_init"), 0);
	file_text_encrypt("general.osq");
	
	return result;
}

function bots_get_info(db_bots, class_num) {
	var result = {};
	
	var db_wp = db_create_weapon(db_bots, class_num);
	var db_eq = db_create_equipment(db_bots, class_num, 1);
	var db_skill = db_create_skill_list(db_bots, class_num, 2);
	
	var bots_data = db_get_row(db_bots, db_type_bots, class_num);
	var str_name = ["weapon", "core", "bearing", "crust", "batk_set", "alt_batk_set", "air_batk_set",
					"derv", "spmv", "spmv_mod", "ulti"];
	for (var i = 0; i < array_length(str_name); i++) {
		switch (i) {
			case 0:
				result[$ str_name[i]] = db_get_value(db_wp, 3, bots_data.weapon);
				break;
			case 1:
			case 2:
			case 3:
				if (i > 1) {
					db_eq = db_refresh(db_eq, db_type_equipment, db_bots, class_num, i);
				}
				result[$ str_name[i]] = db_get_value(db_eq, 5, bots_data[$ str_name[i]]);
				break;
			case 4:
				result[$ str_name[i]] = string($"Preset {bots_data.batk_set}");
				break;
			case 5:
				var db_batk = db_create_skill_list(db_bots, class_num, 1);
				var str_data = db_get_batk_seq(db_bots, db_batk, class_num, 2, bots_data.alt_batk_set);
				
				result[$ str_name[i]] = (array_length(struct_get_names(str_data)) > 0) 
										? ((str_data.num1.type > 0) ? string($"Preset {bots_data.alt_batk_set}") : "-")
										: "-";
				
				delete str_data;
				ds_grid_destroy(db_batk);
				break;
			case 6:
				var db_batk = db_create_skill_list(db_bots, class_num, 1);
				var str_data = db_get_batk_seq(db_bots, db_batk, class_num, 3, bots_data.air_batk_set);
				
				result[$ str_name[i]] = (array_length(struct_get_names(str_data)) > 0) 
										? ((str_data.num1.type > 0) ? string($"Preset {bots_data.air_batk_set}") : "-")
										: "-";
				
				delete str_data;
				ds_grid_destroy(db_batk);
				break;
			default:
				if (i == 7) {
					result[$ str_name[i]] = (result[$ "alt_batk_set"] == "-") ? db_get_value(db_skill, 3, bots_data[$ str_name[i]]) : "-";
				} else {
					if (i != 9) {
						var sk_type = [3, 3, 4];
						db_skill = db_refresh(db_skill, db_type_sklist, db_bots, class_num, sk_type[i-8]);
					}
					result[$ str_name[i]] = db_get_value(db_skill, 3, bots_data[$ str_name[i]]);
				}
		}
	}
	
	delete bots_data;
	ds_grid_destroy(db_wp);
	ds_grid_destroy(db_eq);
	ds_grid_destroy(db_skill);
	
	return result;
}

function bots_set_skill_index(skill_data, class_num) {
	switch (skill_data.sk_type) {
		case bots_skill_batk:		
			switch (class_num) {
				case bots_class_warrior:		
					switch (skill_data.type) {
						case 1: 				// Slash
							skill_data.index = {
								dmg: 0,
								max_chain: 7,
								target_cnt: 8, 
								dmg_excess: 9,
								cd: 10
							};
							break;
						case 2: 				// Blow
							skill_data.index = {
								dmg: 1,
								buff_eff: 2,
								buff_dur: 0,
								cc_eff: 3, 
								cd: 4
							};
							skill_data.buff = stats_list.dmg_output;
							skill_data.cc = stats_ailment.cc_knockback;
							break;
						case 3: 				// Thrust
							skill_data.index = {
								dmg: 1,
								buff_eff: 2,
								buff_dur: 0,
								cd: 3
							};
							skill_data.buff = stats_list.ap;
							break;
						case 4: 				// Double Slash
							skill_data.index = {
								dmg: 0,
								max_chain: 4,
								target_cnt: 5, 
								dmg_excess: 6,
								cd: 7
							};
							break;
						case 5: 				// Twin Thrust
							skill_data.index = {
								dmg: 1,
								buff_eff: 2,
								buff_dur: 0,
								cd: 3
							};
							skill_data.buff = stats_list.ap;
							break;
						case 6: 				// Uppercut
							skill_data.index = {
								dmg: 1,
								cc_eff: 2, 
								cd: 3
							};
							skill_data.cc = stats_ailment.cc_airborne;
							break;
						case 7: 				// Spinning Slash
							skill_data.index = {
								dmg: 1,
								target_cnt: 2, 
								dmg_excess: 3,
								cd: 4
							};
							break;
						case 8: 				// Slam
							skill_data.index = {
								dmg: 1,
								cc_eff: 2, 
								cd: 3
							};
							skill_data.target_cnt = 0;
							skill_data.cc = stats_ailment.cc_knockback;
							break;
						case 9: 				// Charged Slash
							skill_data.index = {
								dmg: [1, 2, 3],
								cc_eff: 5, 
								target_cnt: 7, 
								dmg_excess: 8,
								cd: 9
							};
							skill_data.cc = stats_ailment.cc_knockback;
							skill_data.max_phase = 0;
							if (skill_data.talent.m10) {
								array_push(skill_data.index.dmg, 4);
							}
							break;
						case 10: 				// Smash
							skill_data.index = {
								dmg: 0,
								max_chain: 4,
								cc_eff: 5, 
								cd: 6
							};
							skill_data.cc = stats_ailment.cc_knockdown;
							break;
						case 11: 				// Stab
							skill_data.index = {
								dmg: 1,
								buff_eff: [2, 3],
								buff_dur: [-1, 0],
								cd: 4
							};
							skill_data.buff = [stats_list.batk_db, stats_list.ap];
							break;
					}
				break;

				case bots_class_archer:		
					switch (skill_data.type) {
						case 1: 				// Ordinary Shot
							skill_data.index = {
								dmg: 0,
								max_chain: 7,
								cd: 8
							};
							skill_data.spd = 40;
							skill_data.recoil = 10;
							break;
						case 2: 				// Heavy Shot
							skill_data.index = {
								dmg: 1,
								buff_eff: 2,
								buff_dur: 0, 
								cd: 3
							};
							skill_data.buff = stats_list.dmg_output;
							skill_data.cc = stats_ailment.cc_flinch;
							skill_data.spd = 35;
							skill_data.recoil = 25;
							break;
						case 3: 				// Multi Shot
							skill_data.index = {
								dmg: 1,
								cnt: 2,
								cd: 3
							};
							skill_data.spd = 40;
							skill_data.recoil = 15;
							break;
						case 4: 				// Double Shot
							skill_data.index = {
								dmg: 1,
								cd: 2
							};
							skill_data.spd = 40;
							skill_data.recoil = 10;
							break;
						case 5: 				// Parallel Shot
							skill_data.index = {
								dmg: 1,
								cnt: 2,
								cd: 3
							};
							skill_data.spd = 40;
							skill_data.recoil = 25;
							skill_data.angle = 12;
							break;
						case 6: 				// Acidic Shot
							skill_data.dmg_type = skill_dmg_type.acid;
							skill_data.index = {
								dmg: 0,
								max_chain: 7,
								cd: 8
							};
							skill_data.spd = 40;
							skill_data.recoil = 10;
							break;
						case 7: 				// Piercing Shot
							skill_data.index = {
								dmg: 0,
								max_chain: 7,
								target_cnt: 8,
								dmg_excess: 9,
								cd: 10
							};
							skill_data.spd = 40;
							skill_data.recoil = 15;
							break;
						case 8: 				// Charged Shot
							skill_data.index = {
								dmg: [1, 2, 3],
								cc_eff: 5, 
								target_cnt: 7, 
								dmg_excess: 8,
								cd: 9
							};
							skill_data.cc = stats_ailment.cc_knockback;
							skill_data.max_phase = 0;
							if (skill_data.talent.m11) {
								array_push(skill_data.index.dmg, 4);
							}
							skill_data.spd = 60;
							skill_data.recoil = 50;
							break;
						case 9: 				// Explosive Shot
							skill_data.index = {
								dmg: 1,
								radius: 2,
								cc_eff: 3,
								cd: 4
							};
							skill_data.target_cnt = 0;
							skill_data.cc = stats_ailment.cc_knockback;
							skill_data.spd = 35;
							skill_data.recoil = 35;
							break;
					}
				break;

				case bots_class_medic:		
					switch (skill_data.type) {
						case 1: 				// Slash
							skill_data.index = {
								dmg: 0,
								max_chain: 6,
								target_cnt: 7, 
								dmg_excess: 8,
								cd: 9
							};
							break;
						case 2: 				// Blow
							skill_data.index = {
								dmg: 1,
								buff_eff: 2,
								buff_dur: 0,
								cc_eff: 3, 
								cd: 4
							};
							skill_data.buff = stats_list.dmg_output;
							skill_data.cc = stats_ailment.cc_knockback;
							break;
						case 3: 				// Thrust
							skill_data.index = {
								dmg: 1,
								buff_eff: 2,
								buff_dur: 0,
								cd: 3
							};
							skill_data.buff = stats_list.ap;
							break;
						case 4: 				// Double Slash
							skill_data.index = {
								dmg: 0,
								max_chain: 6,
								target_cnt: 7, 
								dmg_excess: 8,
								cd: 9
							};
							skill_data.dmg_type = skill_dmg_type.acid;
							break;
						case 5: 				// Uppercut
							skill_data.index = {
								dmg: 1,
								cc_eff: 2, 
								cd: 3
							};
							skill_data.cc = stats_ailment.cc_airborne;
							break;
						case 6: 				// Spinning Slash
							skill_data.index = {
								dmg: [1, 2],
								buff_eff: 3,
								buff_dur: 0,
								target_cnt: 4, 
								dmg_excess: 5,
								cd: 6
							};
							skill_data.buff = stats_list.atk_spd;
							skill_data.chain = 1 + (skill_data.talent.m8);
							break;
						case 7: 				// Stab
							skill_data.index = {
								dmg: 0,
								max_chain: 4,
								buff_eff: 5,
								buff_dur: 0,
								cd: 6
							};
							skill_data.buff = stats_list.ap;
							break;
						case 8: 				// Drag Out
							skill_data.index = {
								dmg: 0,
								max_chain: 3,
								buff_eff: 4,
								buff_dur: 0,
								cd: 5
							};
							skill_data.buff = stats_list.ap;
							break;
					}
				break;
			}
		break;
		
		case bots_skill_derv:		
			switch (class_num) {
				case bots_class_warrior:		
					skill_data.multiplier = ""; 	
					skill_data.dmg_type = skill_dmg_type.none;
					switch (skill_data.type) {
						case 1: 				// Self Repair
							skill_data.cd_type = skill_cd_type.none;
							skill_data.target = skill_target_type.own;
							skill_data.index = {
								heal: 1,
								interval: 2,
								delay: 3
							};
							break;
						case 2: 				// Redirect
							skill_data.index = {
								buff_eff: 1,
								buff_dur: 2,
								cd: 3
							};
							skill_data.buff = stats_list.atk_spd;
							break;
					}
				break;

				case bots_class_archer:		
					skill_data.cd_type = skill_cd_type.none;
					switch (skill_data.type) {
						case 1: 				// Follow-up Shot
							skill_data.dmg_type = skill_dmg_type.none;
							skill_data.index = {
								dmg: 1,
								cc_eff: 2
							};
							skill_data.spd = 40;
							skill_data.recoil = 10;
							break;
						case 2: 				// Twister Shot
							skill_data.index = {
								dmg: [1, 2],
								cnt: 3,
								interval: 4
							};
							skill_data.spd = 40;
							skill_data.recoil = 15;
							break;
					}
				break;

				case bots_class_medic:		
					switch (skill_data.type) {
						case 1: 				// Dart/Serum Throw
							skill_data.dmg_type = skill_dmg_type.none;	
							skill_data.index = {
								dmg: 1,
								stats_ovr: 2,
								max_stack: 3,
								cd: 4,
								device_type: 5,
							};
							skill_data.stats_ovr_type = stats_list.accuracy;
							break;
					}
				break;

			}
		break;

		case bots_skill_spmv:
			switch (class_num) {
				case bots_class_warrior:		
					switch (skill_data.type) {
						case 1: 				// Determination
							skill_data.target = skill_target_type.own;
							skill_data.multiplier = ""; 	
							skill_data.dmg_type = skill_dmg_type.none;
							skill_data.index = {
								buff_eff: [1, 2],
								buff_dur: [3, 3],
								cd: 4
							};
							skill_data.buff = [stats_list.atk_scale, stats_list.def_scale];
							break;
						case 2: 				// Sidecut
							skill_data.index = {
								dmg: 1,
								max_stack: 2,
								buff_eff: 3,
								buff_dur: 4,
								target_cnt: 5,
								dmg_excess: 6,
								cd: 7
							};
							skill_data.buff = stats_list.atk_spd;
							break;
						case 3: 				// Parry
							if (!sign(skill_data.upgraded)) {
								skill_data.index = {
									buff_eff: [1, 3],
									buff_dur: [0, -1],
									dur: 2,
									cd: 4
								};
							} else {
								skill_data.index = {
									buff_eff: [1, 5],
									buff_dur: [0, -1],
									dur: 2,
									dmg: 3,
									cc_eff: 4,
									target_cnt: 6,
									dmg_excess: 7,
									cd: 8
								};
								skill_data.cc = stats_ailment.cc_knockback;
							}
							skill_data.buff = [stats_list.dmg_reduction, stats_list.dmg_reduction];
							break;
					}
				break;

				case bots_class_archer:		
					switch (skill_data.type) {
						case 1: 				// Flick Shot
							skill_data.index = {
								dmg: 1,
								move_hor: 2,
								cd: 3
							};
							skill_data.dash = 1;
							skill_data.spd = 40;
							skill_data.recoil = 15;
							break;
						case 2: 				// Spike/Electric Shot
							if (!sign(skill_data.upgraded)) {
								skill_data.index = {
									dmg: 1,
									cnt: 2,
									debuff_dur: 3,
									debuff_eff: 4,
									buff_eff: 5,
									buff_dur: 6,
									cd: 7
								};
								skill_data.debuff = stats_ailment.debuff_slow;
								skill_data.recoil = 25;
								skill_data.angle = 12;
							} else {
								skill_data.target = skill_target_type.all_bots;
								skill_data.index = {
									dmg: 1,
									radius: 2,
									cc_dur: 3,
									buff_eff: 4,
									buff_dur: 5,
									target_cnt: 6,
									dmg_excess: 7,
									cd: 8
								};
								skill_data.cc = stats_ailment.cc_stun;
								skill_data.recoil = 35;
							}
							skill_data.buff = stats_list.atk_scale;
							skill_data.jump = 1;
							skill_data.dash = 1;
							skill_data.move_hor = 1.5;
							skill_data.move_hor_delay = 0.5;
							skill_data.spd = 40;
							
							break;
						case 3: 				// Half-Moon Shot
							skill_data.cd_type = skill_cd_type.usage_batk;
							skill_data.index = {
								dmg: 1,
								jump: 2,
								buff_eff: 3,
								buff_dur: 4,
								cd: 5
							};
							skill_data.buff = stats_list.atk_spd;
							skill_data.dash = 1;
							skill_data.move_hor = 2;
							skill_data.no_reset = 1;
							skill_data.spd = 40;
							skill_data.recoil = 15;
							skill_data.angle = 12;
							break;
						case 4: 				// Supercharged Shot
							skill_data.index = {
								dmg: [1, 2, 3],
								cc_eff: 7, 
								target_cnt: 9, 
								dmg_excess: 10,
								cd: 11
							};
							skill_data.cc = stats_ailment.cc_knockback;
							skill_data.max_phase = 0;
							if (skill_data.talent.m11) {
								array_push(skill_data.index.dmg, 5, 6);
							}
							skill_data.spd = 70;
							skill_data.recoil = 70;
							break;
					}
				break;

				case bots_class_medic:		
					skill_data.dmg_type = skill_dmg_type.none;
					switch (skill_data.type) {
						case 1: 				// Acid Vial
							skill_data.multiplier = "";	
							skill_data.cd_type = skill_cd_type.dyn_timeout;	
							skill_data.target = skill_target_type.own;	
							skill_data.index = {
								buff_eff: [1, 1],
								buff_dur: [2, 2],
								dur: [2, 2],
								cd: 3,
								device_type: 4
							};
							skill_data.buff = [stats_mod.infuse_acid, stats_list.acidity_bonus];
							break;
						case 2: 				// Healing Potion, buat struct potion
							skill_data.multiplier = "hp";	
							skill_data.target = skill_target_type.own;	
							skill_data.index = {
								regen: 1,
								dur: 2,
								interval: 3,
								cd: 4,
								device_type: 5
							};
							break;
						case 3: 				// Diminisher Knife
							skill_data.index = {
								dmg: 1,
								debuff_amp: 2,
								debuff_dur: -1,
								stats_ovr: 3,
								cd: 4,
								device_type: 5
							};
							skill_data.stats_ovr_type = stats_list.accuracy;
							break;
					}
				break;

			}
		break;

		case bots_skill_ulti:
			switch (class_num) {
				case bots_class_warrior:		
					switch (skill_data.type) {
						case 1:					// Whirlwind Cut
							skill_data.index = {
								dmg: [1, 2],
								cnt: 3,
								cc_eff: 4,
								target_cnt: 5,
								dmg_excess: 6,
								cd: 7
							};
							skill_data.cc = stats_ailment.cc_knockback;
						break;
						case 2:					// Devastating Ambush
							skill_data.index = {
								dmg: [1, 2, 3],
								cc_eff: 4,
								buff_eff: [5, 6],
								buff_dur: [-1, -1],
								cd: 8
							};
							skill_data.cc = [stats_ailment.cc_airborne, stats_ailment.cc_flinch];
							skill_data.buff = [stats_list.ap, stats_list.physical_do];
							skill_data.threshold = 300;
						break;
						case 3:					// Daunting Shockwaves
							skill_data.cd_type = skill_cd_type.dyn_timeout;	
							skill_data.dmg_type = skill_dmg_type.none;
							skill_data.index = {
								dmg: 1,
								debuff_eff: 2,
								debuff_dur: 3,
								buff_eff: [4, 5],
								buff_dur: [0, 0],
								dur: 6,
								cd: 7
							};
							skill_data.buff = [stats_list.crit_buildup, stats_list.crit_damage];
							skill_data.debuff = stats_ailment.debuff_impair;
						break;
					}
				break;

				case bots_class_archer:		
					switch (skill_data.type) {
						case 1:					// Cloudburst Volley
							skill_data.index = {
								dmg: 1,
								cnt: 2,
								cd: 3
							};
							skill_data.spd = 50;
							skill_data.recoil = 30;
						break;
						case 2:					// Harpoon Breakout
							skill_data.index = {
								dmg: [1, 2],
								max_stack: 3,
								buff_eff: [4],
								buff_dur: [-1],
								debuff_eff: 5,
								debuff_dur: 0,
								cd: 6
							};
							skill_data.buff = [stats_list.ab];
							skill_data.debuff = stats_ailment.debuff_slow;
							skill_data.debuff_target = skill_target_type.own;	
							skill_data.spd = 40;
							skill_data.recoil = 35;					
						break;
						case 3:					// Pinpoint Shooting
							skill_data.cd_type = skill_cd_type.dyn_timeout;	
							skill_data.index = {
								dmg: 1,
								cc_eff: 2,
								buff_eff: [3, 4],
								buff_dur: [0, 0],
								debuff_eff: 5,
								debuff_dur: 0,
								dur: 6,
								cd: 7
							};
							skill_data.buff = [stats_list.atk_scale, stats_list.crit_damage];
							skill_data.cc = stats_ailment.cc_knockback;
							skill_data.debuff = stats_ailment.debuff_impair;
							skill_data.debuff_target = skill_target_type.own;
						break;
					}
				break;

				case bots_class_medic:		
					switch (skill_data.type) {
						case 1:					// Medical Kit
							skill_data.multiplier = "hp";
							skill_data.dmg_type = skill_dmg_type.none;
							skill_data.target = skill_target_type.own;
							skill_data.index = {
								heal: 1,
								buff_eff: [2, 3],
								buff_dur: [4, 4],
								cnt: 5,
								cd: 6,
								device_type: 7
							};
							skill_data.buff = [stats_list.physical_do, stats_list.acid_do];
							skill_data.total = 5;
						break;
						case 2:					// Corrosive Concentration 
							skill_data.dmg_type = skill_dmg_type.acid;
							if (!skill_data.talent.s14) {
								skill_data.index = {
									dmg: [1],
									dur: 2,
									interval: 3,
									buff_eff: 4,
									buff_dur: 5,
									target_cnt: 6,
									dmg_excess: 7,
									cd: 8,
									device_type: 9
								};
							} else {
								skill_data.index = {
									dmg: [1, 2],
									dur: 3,
									interval: 4,
									buff_eff: 5,
									buff_dur: 6,
									target_cnt: 7,
									dmg_excess: 8,
									cd: 9,
									device_type: 10
								};
							}
							skill_data.radius = 1;
							skill_data.buff = stats_list.tc;
						break;
						case 3:					// CBC Booster, buat struct booster
							skill_data.multiplier = "hp";
							skill_data.dmg_type = skill_dmg_type.none;
							skill_data.target = skill_target_type.own;
							skill_data.index = {
								regen: 1,
								interval: 2,
								buff_amp: 3,
								buff_dur: 4,
								cnt: 5,
								cd: 6,
								device_type: 7
							};
							skill_data.total = 7;
						break;
					}
				break;
			}
		break;

		case bots_skill_intg:
			if (skill_data.type == 1) {
				switch (class_num) {
					case bots_class_warrior:	
						skill_data.multiplier = "";
						skill_data.cd_type = skill_cd_type.none;
						skill_data.dmg_type = skill_dmg_type.none;
						skill_data.target = skill_target_type.none;
						skill_data.index = {
							debuff_eff: [1],
							debuff_dur: [0],
							buff_eff: [2, 3],
							buff_dur: [0, -1]
						};
						skill_data.debuff = [stats_ailment.debuff_slow];
						skill_data.buff = [stats_list.agility, stats_list.def_scale];
						break;

					case bots_class_archer:	
						skill_data.multiplier = "";
						skill_data.cd_type = skill_cd_type.auto_timeout;
						skill_data.dmg_type = skill_dmg_type.none;
						skill_data.index = {
							buff_eff: 1,
							buff_dur: 0,
							max_stack: 2,
							cd: 3
						};
						skill_data.buff = stats_list.atk;
						break;

					case bots_class_medic:	
						skill_data.multiplier = "hp";
						skill_data.dmg_type = skill_dmg_type.none;
						skill_data.target = skill_target_type.ally;
						skill_data.index = {
							heal: 1,
							device_type: 3,
							cd: 4
						};
						// skill_data.buff_dur ambil struct potion
						break;
				}
			} else {
				skill_data.dmg_type = skill_dmg_type.none;
				switch (skill_data.type) {
					case 2:					// Grave Hit Module
						skill_data.multiplier = "";
						skill_data.cd_type = skill_cd_type.none;
						skill_data.index = {
							buildup: 1,
							dmg: 2
						};
					break;
					case 3:					// Superbody Module
						skill_data.multiplier = "-hp_lost";
						skill_data.cd_type = skill_cd_type.none;
						skill_data.index = {
							armor: 1,
							fade_dur: 3
						};
						skill_data.threshold = 40;
					break;
					case 4:					// Sequence Breaker Module
						skill_data.multiplier = "hp";
						skill_data.index = {
							cc_eff: 1,
							armor: 2,
							fade_dur: 4,
							cd: 5
						};
						skill_data.buff = [stats_list.ignore_interruption];
						skill_data.buff_eff = [100];
						skill_data.buff_dur = [5];
						skill_data.cc = stats_ailment.cc_knockback;
						skill_data.threshold = 5;
					break;
					case 5:					// Substitute Care Module
						skill_data.multiplier = "hp";
						skill_data.target = skill_target_type.ally;
						skill_data.index = {
							heal: 1,
							cd: 3
						};
					break;
					case 6:					// Fatalism Prevention Module
						skill_data.multiplier = "hp";
						skill_data.index = {
							regen: 1,
							interval: 3, 
							dur: 4,
							cd: 5
						};
					break;
				}
			}
		break;

		case bots_skill_uniq:
			skill_data.cd_type = skill_cd_type.none;
			skill_data.dmg_type = skill_dmg_type.none;
			
			switch (class_num) {
				case bots_class_warrior:	
					skill_data.multiplier = "hp";
					skill_data.index = {
						max_point: 1,
						buff_eff: 2,
						buff_dur: 5,
						armor: 3,
						heal: 4,
						fade_dur: 6
					};
					skill_data.max_point = 200;
					skill_data.threshold = 200;
					skill_data.buff = stats_list.atk;
				break;

				case bots_class_archer:	
					skill_data.multiplier = "";
					skill_data.index = {
						max_stack: 1
					};
					skill_data.threshold = 5;
				break;

				case bots_class_medic:	
					skill_data.multiplier = "hp";
					skill_data.index = {
						max_point: 1,
						regen: 2,
						interval: 3,
						buff_eff: [4, 5],
						buff_dur: [6, 6]
					};
					skill_data.max_point = 300;
					skill_data.buff = [stats_list.dmg_reduction, stats_list.tc];
				break;
			}
		break;

		case bots_skill_ext:
			switch (class_num) {
				case bots_class_warrior:		
					switch (skill_data.type) {
						case 1:					// Wavecleaver
							skill_data.cd_type = skill_cd_type.none; 
							skill_data.index = {
								dmg: 1,
								interval: 2,
								cc_eff: 3,
								target_cnt: 4, 
								dmg_excess: 5,
								max_dur: 6
							};
							skill_data.cc = stats_ailment.cc_knockback;
						break;
						case 2:					// Doom Drop
							skill_data.multiplier = "-hp_enemy";
							skill_data.dmg_type = skill_dmg_type.absolute;
							skill_data.cd_type = skill_cd_type.none; 
							skill_data.index = {
								dmg: 1,
								max_dur: 2
							};
							skill_data.cc = stats_ailment.cc_flinch;
						break;
						case 3:					// Breaker Stance
							skill_data.index = {
								dmg: 1,
								cc_eff: 2,
								target_cnt: 3, 
								dmg_excess: 4,
								max_dur: 5,
								cd: 6
							};
							skill_data.cc = stats_ailment.cc_knockback;
						break;
						case 4:					// Fang Breaker
							skill_data.cd_type = skill_cd_type.none; 
							skill_data.index = {
								dmg: [1, 2],
								cc_eff: 3
							};
							skill_data.cc = stats_ailment.cc_airborne;
						break;
						case 5:					// Starfall Strike
							skill_data.index = {
								dmg: [1, 2],
								cc_eff: 3,
								max_dur: 4,
								cd: 5
							};
							skill_data.cnt = 2;
							skill_data.cc = stats_ailment.cc_knockback;
						break;
					}
				break;

				case bots_class_archer:		
					switch (skill_data.type) {
						case 1:					// Twin Talon Shot
							skill_data.cd_type = skill_cd_type.none; 
							skill_data.index = {
								dmg: 1,
								max_dur: 2
							};
							skill_data.total = 2;
							skill_data.cc = stats_ailment.cc_flinch;
							skill_data.spd = 40;
							skill_data.recoil = 35;	
						break;
						case 2:					// Soulsnare Shot
							skill_data.multiplier = "-hp_enemy";
							skill_data.dmg_type = skill_dmg_type.absolute;
							skill_data.cd_type = skill_cd_type.none; 
							skill_data.index = {
								dmg: 1,
								max_dur: 2
							};
							skill_data.cc = stats_ailment.cc_knockdown;
							skill_data.spd = 60;
							skill_data.recoil = 20;	
						break;
						case 3:					// Cyclone Shot 
							skill_data.index = {
								dmg: [1, 2],
								cnt: 3,
								interval: 4,
								max_dur: 5,
								cd: 6
							};
							skill_data.total = 2;
							skill_data.spd = 40;
							skill_data.recoil = 25;	
						break;
						case 4:					// Skyfall Barrage
							skill_data.cd_type = skill_cd_type.none; 
							skill_data.index = {
								dmg: 1,
								cnt: 2
							};
							skill_data.spd = 50;
							skill_data.recoil = 25;	
						break;
						case 5:					// Predator's Trilogy
							skill_data.index = {
								dmg: 1,
								cnt: 2,
								cd: 3
							};
							skill_data.total = 3;
							skill_data.spd = 40;
							skill_data.recoil = 10;	
						break;
					}
				break;

				case bots_class_medic:		
					switch (skill_data.type) {
						case 1:					// Stasis Flask/Bolt
							skill_data.cd_type = skill_cd_type.none; 
							skill_data.dmg_type = skill_dmg_type.acid;
							if (!sign(skill_data.upgraded)) {
								skill_data.index = {
									debuff_eff: 1,
									debuff_dur: 2,
									max_dur: 3
								};
							} else {
								skill_data.index = {
									dmg: 1,
									interval: 2,
									debuff_eff: 3,
									debuff_dur: 4,
									max_dur: 5
								};
							}
							skill_data.debuff = stats_ailment.debuff_slow;
						break;
						case 2:					// Abyss Flask
							skill_data.multiplier = "-hp_enemy";
							skill_data.dmg_type = skill_dmg_type.absolute;
							skill_data.cd_type = skill_cd_type.none; 
							skill_data.index = {
								dmg: 1,
								max_dur: 2
							};
						break;
						case 3:					// Life Bloom
							skill_data.multiplier = "hp";
							skill_data.target = skill_target_type.ally;
							skill_data.index = {
								heal: 1,
								max_dur: 2,
								cd: 3
							};
						break;
						case 4:					// Revital Burst
							skill_data.multiplier = "hp";
							skill_data.target = skill_target_type.ally;	
							skill_data.cd_type = skill_cd_type.none; 
							skill_data.index = {
								regen: 1,
								interval: 2,
								max_dur: 3
							};
						break;
						case 5:					// Caustic Drip
							skill_data.dmg_type = skill_dmg_type.acid;
							skill_data.index = {
								dmg: 1,
								interval: 2,
								dur: 3,
								target_cnt: 4,
								dmg_excess: 5,
								max_dur: 6,
								cd: 7
							};
						break;
					}
				break;
			}
		break;
	}
}

/*	// Legacy
function bots_calc_skill_attr(db_skill_dtl, skill_data, to_add, bots_stat, val_str, val_opt, current_index) {
	// to_add = struct_get_names(skill_data.index)
	// current_index = skill_data.index[$ to_add[i]][j]
	var result = 0;

	if (db_get_value(db_skill_dtl, 3, current_index) == 1 && !is_string(val_str.init)) {
		var val_mod = db_get_value(db_skill_dtl, 8, current_index);
		var val_mod_names = ["scale_num", "flat_num", "amp_num"];
		
		result = (val_str.init + 
				 (val_str.low * (skill_data.level - 1)) + 
				 (val_str.mid * floor(skill_data.level / 5) * (skill_data.level - 4)) + 
				 (val_str.high * floor(skill_data.level / 7) * (skill_data.level - 7)));
		
		if (!val_opt.fixed) {
			for (var k = 0; k < array_length(val_mod_names); k++) {
				if (val_mod[$ val_mod_names[k]] != 0) {
					if (!is_array(val_mod[$ val_mod_names[k]])) {
						val_mod[$ val_mod_names[k]] = [val_mod[$ val_mod_names[k]]];
					}
				
					var add_val = 0;
					for (var l = 0; l < array_length(val_mod[$ val_mod_names[k]]); l++) {
						add_val += bots_get_stats_val(bots_stat, val_mod[$ val_mod_names[k]][l], "", true);
					}
					switch (val_mod_names[k]) {
						case "scale_num":
						case "amp_num":
							result *= (!val_mod.inv) ? ((100 + add_val) / 100) : ((100 - add_val) / 100);
							break;
						case "flat_num":
							result += add_val;
							break;
					}
				}
			}
		}
		
		if (val_opt.rounded) {
			result = round(result);
		}

		delete val_mod;
	} else {
		result = (is_struct(val_str)) ? val_str.init : val_str;
	}

	return result;
}

function bots_set_skill_attr(db_skill_dtl, db_stats, skill_data, bots_stat, class_num) {
	skill_data.multiplier = "atk";
	skill_data.dmg_type = skill_dmg_type.physical;
	skill_data.cd_type = skill_cd_type.timeout;
	skill_data.target = skill_target_type.enemy;
	
	bots_set_skill_index(skill_data, class_num);

	var to_add = ["int_power", "ignint_bonus"];
	var add_i = [-4, -3];
	for (var i = 0; i < array_length(to_add); i++) {
		if (to_add[i] == "ignint_bonus") {
			skill_data.ignint_bonus = !db_get_value(db_skill_dtl, 7, ds_grid_height(db_skill_dtl) + add_i[i])
									  ? db_get_value(db_skill_dtl, 5, ds_grid_height(db_skill_dtl) + add_i[i])
									  : 0;
			skill_data.ignint_subs = db_get_value(db_skill_dtl, 7, ds_grid_height(db_skill_dtl) + add_i[i])
									 ? db_get_value(db_skill_dtl, 5, ds_grid_height(db_skill_dtl) + add_i[i])
									 : 0;
		} else {
			skill_data[$ to_add[i]] = db_get_value(db_skill_dtl, 5, ds_grid_height(db_skill_dtl) + add_i[i]);
		}
	}

	to_add = struct_get_names(skill_data.index);
	for (var i = 0; i < array_length(to_add); i++) {
		if (to_add[i] == "max_chain") {
			continue;
		}
		
		var to_array = 0;
		var index_add = 1;
		if (!is_array(skill_data.index[$ to_add[i]])) {
			skill_data.index[$ to_add[i]] = [skill_data.index[$ to_add[i]]];
			to_array = 1;
		}
		skill_data[$ to_add[i]] = [];

		for (var j = 0; j < array_length(skill_data.index[$ to_add[i]]); j++) {		
			switch (to_add[i]) {
				case "dmg":
					if (skill_data.index.dmg[j] == 0) {
						skill_data.index.dmg[j] = skill_data.chain_num + 1;
					}
					if (struct_exists(skill_data.index, "max_chain")) {
						skill_data.index.dmg[j] = clamp(skill_data.index.dmg[j], 0, skill_data.index.max_chain);
					}
					break;
				case "buff_dur":
				case "debuff_dur":
					if (skill_data.index[$ to_add[i]][j] == 0) {
						array_push(skill_data[$ to_add[i]], 0);
						continue;
					}
					break;
			}
			
			skill_data.level = clamp(skill_data.level, 1, skill_data.eff_level);
			var val_str = db_get_value(db_skill_dtl, 5, skill_data.index[$ to_add[i]][j]);	
			var val_attr = db_get_value(db_skill_dtl, 6, skill_data.index[$ to_add[i]][j]);	
			var val_opt = db_get_value(db_skill_dtl, 7, skill_data.index[$ to_add[i]][j]);
			var val_cnt = 0;
			
			switch (to_add[i]) {
				case "dmg":
					for (var k = skill_data.index.dmg[j]; k > 0; k--) {
						if (db_get_value(db_skill_dtl, 6, k).unlocked) {		// Chain Unlocked
							break;
						}
						skill_data.index.dmg[j]--;
					}

					if (string_pos("*", val_attr.affix)) {
						skill_data.cnt = to_real(string_copy(val_attr.affix, 1, string_pos("*", val_attr.affix) - 1));
					}

					if (struct_exists(skill_data, "max_phase")) {
						if (j >= 1) {
							if (db_get_value(db_skill_dtl, 6, j).unlocked) {
								skill_data.max_phase++;
							}
						}
					}
					break;
			}
	
			// Extra Value from Stats 
			val_cnt = bots_calc_skill_attr(db_skill_dtl, skill_data, to_add, bots_stat, val_str, val_opt, skill_data.index[$ to_add[i]][j]);
			
			switch (to_add[i]) {
				case "stats_ovr":
					index_add = 0;
					if (!is_struct(skill_data[$ to_add[i]])) {
						skill_data[$ to_add[i]] = {};
					}
					if (!is_array(skill_data.stats_ovr_type)) {
						skill_data.stats_ovr_type = [skill_data.stats_ovr_type];
					}
					
					struct_set(skill_data.stats_ovr, db_get_value(db_stats, 5, skill_data.stats_ovr_type[j]), val_cnt);
					break;
			}
			
			switch (skill_data.sk_type) {
				case bots_skill_derv:
					switch (class_num) {
						case 3:				// Medic
							switch (skill_data.type) {
								case 1:					// Dart/Serum Throw
									skill_data.dmg_type = skill_data.adj.num1;
								break;
							}
						break;
					}
					
					break;
			}
			
			if (index_add) {
				if (to_array) {
					skill_data[$ to_add[i]] = val_cnt;
				} else {
					array_push(skill_data[$ to_add[i]], val_cnt);
				}
			}
	
			if (is_struct(val_str)) {
				delete val_str;
			}
			if (is_struct(val_attr)) {
				delete val_attr;
			}
			if (is_struct(val_opt)) {
				delete val_opt;
			}
		}
	}
}

function bots_get_batk(db_bots, db_batk, db_stats, bots_stat, class_num, batkseq_type = 1) {
	var bots_data = db_get_row(db_bots, db_type_bots, class_num);
	var seq_data = db_get_batk_seq(db_bots, db_batk, class_num, batkseq_type, bots_data.batk_set);

	for (var i = 0; i < array_length(struct_get_names(seq_data)); i++) {
		if (seq_data[$ string($"num{i+1}")].type == 0) {	// Empty
			for (var j = array_length(struct_get_names(seq_data)) - 1; j >= i; j--) {
				struct_remove(seq_data, string($"num{j+1}"));
			}
			break;
		} else {											// Non-Empty BATK
			var db_skdtl = db_create_skill_dtl(db_batk, class_num, 1, seq_data[$ string($"num{i+1}")].type);
			var sk_data = db_get_row(db_batk, db_type_sklist, seq_data[$ string($"num{i+1}")].type);

			struct_remove(seq_data[$ string($"num{i+1}")], "seq_type");
			struct_remove(seq_data[$ string($"num{i+1}")], "set_num");
			struct_remove(seq_data[$ string($"num{i+1}")], "unlock_lv");

			var to_add = ["level", "eff_level", "upgraded", "sprite", "talent"];
			for (var j = 0; j < array_length(to_add); j++) {
				if (!is_struct(sk_data[$ to_add[j]])) {
					seq_data[$ string($"num{i+1}")][$ to_add[j]] = sk_data[$ to_add[j]];
				} else {
					seq_data[$ string($"num{i+1}")][$ to_add[j]] = variable_clone(sk_data[$ to_add[j]]);
				}
			}
			seq_data[$ string($"num{i+1}")].sk_type = sk_data.type;

			bots_set_skill_attr(db_skdtl, db_stats, seq_data[$ string($"num{i+1}")], bots_stat, class_num);

			delete sk_data;
			ds_grid_destroy(db_skdtl);
		}
	}

	delete bots_data;
	return seq_data;
}

function bots_get_skill(db_bots, db_skill, db_stats, bots_stat, skill_type, class_num, db_batk = 0) {
	var bots_data = db_get_row(db_bots, db_type_bots, class_num);
	var sk_used = [0, 0, bots_data.derv, bots_data.spmv, bots_data.ulti, 1, 1, 0];
	var result = {};

	switch (skill_type) {
		case bots_skill_derv:
			var seq_alt_data = db_get_batk_seq(db_bots, db_batk, class_num, 1, bots_data.alt_batk_set);
			
			var no_alt = 1;
			if (array_length(struct_get_names(seq_alt_data)) > 0) {
				if (seq_alt_data.num1.type == 0) {
					no_alt = 0;
				}
			} 

			if (no_alt) {								// DERV
				var db_skdtl = db_create_skill_dtl(db_skill, class_num, skill_type, sk_used[skill_type]);
				var sk_data = db_get_row(db_skill, db_type_sklist, sk_used[skill_type]);

				var to_add = ["level", "eff_level", "upgraded", "sprite", "talent"];
				for (var i = 0; i < array_length(to_add); i++) {
					if (!is_struct(sk_data[$ to_add[i]])) {
						result[$ to_add[i]] = sk_data[$ to_add[i]];
					} else {
						result[$ to_add[i]] = variable_clone(sk_data[$ to_add[i]]);
					}
				}
				result.type = sk_used[skill_type];
				result.sk_type = skill_type;

				result.adj = {};
				var sk_adj = db_get_skill_adj(db_skill, class_num, skill_type, sk_data.num, db_bots);
				for (var i = 0; i < array_length(struct_get_names(sk_adj)); i++) {
					struct_set(result.adj, string($"num{i+1}"), sk_adj[$ string($"num{i+1}")].adj_val);
				}

				bots_set_skill_attr(db_skdtl, db_stats, result, bots_stat, class_num);

				delete sk_adj;
				delete sk_data;
				ds_grid_destroy(db_skdtl);
			} else {									// Alt BATK
				result = bots_get_batk(db_bots, db_batk, db_stats, bots_stat, class_num, 2);
			}

			delete seq_alt_data;
			break;
	}

	return result;
}
*/

// Bots Constants
#macro bots_class_warrior 1
#macro bots_class_archer 2
#macro bots_class_medic 3

#macro bots_team_lastused 1

#macro bots_skill_batk 1
#macro bots_skill_derv 2
#macro bots_skill_spmv 3
#macro bots_skill_ulti 4
#macro bots_skill_intg 5
#macro bots_skill_uniq 6
#macro bots_skill_ext 7

#macro bots_batk_norm 1
#macro bots_batk_alt 2
#macro bots_batk_air 3

enum skill_target_type {
	none,
	own, 
	ally,
	enemy,
	team,
	all_bots
}

enum skill_dmg_type {
	none,
	physical,
	acid,
	absolute
}

enum skill_cd_type {
	none,
	timeout,
	dyn_timeout,
	auto_timeout,
	usage_batk
}

enum stats_list {				// < -3 = debuff, 100+ = multiplier
	hp_scale = -1, 
	atk_scale = -2, 
	def_scale = -3,
	hp = 1, 
	atk = 2, 
	def = 3, 
	agility = 4,
	dmg_output = 5,
	ignore_interruption = 6,
	cd_reduction = 7,
	crit_buildup = 8,
	crit_damage = 9,
	crit_protection = 10,
	healing_output = 11,
	atk_spd = 12,
	melee_do = 13,
	ranged_do = 14,
	aoe_dmg_scale = 15,
	physical_dmg_bonus = 16,
	physical_do = 17,
	dmg_reduction = 18,
	acidity_bonus = 19,
	acid_do = 20,
	tc = 21,
	def_penetration = 22,
	ap = 23,
	ab = 24,
	armor_burst = 25,
	armor_str = 26,
	dmg_res = 27,
	cc_power = 28,
	cc_res = 29,
	buff_power = 30,
	debuff_res = 31,
	charge_spd = 32,
	batk_db = 33,
	batk_do = 34,
	batk_power = 35,
	batk_eff = 36,
	derv_db = 37,
	derv_do = 38,
	derv_power = 39,
	derv_eff = 40,
	spmv_db = 41,
	spmv_do = 42,
	spmv_power = 43,
	spmv_eff = 44,
	ulti_db = 45,
	ulti_do = 46,
	ulti_power = 47,
	ulti_eff = 48,
	intg_db = 49,
	intg_do = 50,
	intg_power = 51,
	intg_eff = 52,
	accuracy = 53,
	recoil_reduction = 54,
	ammo_cap = 55,
	mags_cap = 56,
	reload_spd = 57
}

enum stats_ailment {
	debuff_slow = 1,
	debuff_weaken = 2,
	debuff_impair = 3,
	debuff_break = 4,
	debuff_soften = 5,
	debuff_enervate = 6,
	debuff_crack = 7,
	debuff_exhaust = 8,
	debuff_devitalize = 9,
	debuff_fracture = 10,
	debuff_languish = 11,
	debuff_erode = 12,

	cc_flinch = 101, 
	cc_bind = 102, 
	cc_knockback = 103, 
	cc_airborne = 104, 
	cc_knockdown = 105, 
	cc_silence = 106, 
	cc_stun = 107, 
	cc_paralyze = 108, 
	cc_neutralize = 109
}

enum stats_mod {
	immune_slow = 201,
	immune_weaken = 202,
	immune_impair = 203,
	immune_break = 204,
	immune_soften = 205,
	immune_enervate = 206,
	immune_crack = 207,
	immune_exhaust = 208,
	immune_devitalize = 209,
	immune_fracture = 210,
	immune_languish = 211,
	immune_erode = 212,
	immune_flinch = 213, 

	immune_bind = 214, 
	immune_knockback = 215, 
	immune_airborne = 216, 
	immune_knockdown = 217, 
	immune_silence = 218, 
	immune_stun = 219, 
	immune_paralyze = 220, 
	immune_neutralize = 221,

	cleanse_debuff = 251,
	cleanse_debuff_all = 252,

	infuse_physical = 301,
	infuse_acid = 302
}
