function bots_calc_skill_attr(db_skill_dtl, skill_data, to_add, bots_stat, val_str, val_opt, current_index) {
	// to_add = struct_get_names(skill_data.index)
	// current_index = skill_data.index[$ to_add[i]][j]
	var result = 0;

	if (db_get_value(db_skill_dtl, 3, current_index) == 1 && !is_string(val_str.init)) {
		var val_mod = db_get_value(db_skill_dtl, 8, current_index);
		var val_mod_names = ["scale_num", "flat_num", "amp_num"];
		
		result = (val_str.init + 
				 (val_str.low * (skill_data.level - 1)) + 
				 (val_str.mid * floor(skill_data.level / 5) * (skill_data.level - 4)) + 
				 (val_str.high * floor(skill_data.level / 7) * (skill_data.level - 7)));
		
		if (!val_opt.fixed) {
			for (var k = 0; k < array_length(val_mod_names); k++) {
				if (val_mod[$ val_mod_names[k]] != 0) {
					if (!is_array(val_mod[$ val_mod_names[k]])) {
						val_mod[$ val_mod_names[k]] = [val_mod[$ val_mod_names[k]]];
					}
				
					var add_val = 0;
					for (var l = 0; l < array_length(val_mod[$ val_mod_names[k]]); l++) {
						add_val += bots_get_stats_val(bots_stat, val_mod[$ val_mod_names[k]][l], "", true);
					}
					switch (val_mod_names[k]) {
						case "scale_num":
						case "amp_num":
							result *= (!val_mod.inv) ? ((100 + add_val) / 100) : ((100 - add_val) / 100);
							break;
						case "flat_num":
							result += add_val;
							break;
					}
				}
			}
		}
		
		if (val_opt.rounded) {
			result = round(result);
		}

		delete val_mod;
	} else {
		result = (is_struct(val_str)) ? val_str.init : val_str;
	}

	return result;
}

function bots_set_skill_attr(db_skill_dtl, db_stats, skill_data, bots_stat, class_num) {
	skill_data.multiplier = "atk";
	skill_data.dmg_type = skill_dmg_type.physical;
	skill_data.cd_type = skill_cd_type.timeout;
	skill_data.target = skill_target_type.enemy;
	skill_data.target_cnt = 1;
	
	bots_set_skill_index(skill_data, class_num);
	
	var to_add = ["int_power", "ignint_bonus"];
	var add_i = [-4, -3];
	if (!(skill_data.sk_type == bots_skill_intg || skill_data.sk_type == bots_skill_uniq)) {
		for (var i = 0; i < array_length(to_add); i++) {
			if (to_add[i] == "ignint_bonus") {
				skill_data.ignint_bonus = !db_get_value(db_skill_dtl, 7, ds_grid_height(db_skill_dtl) + add_i[i])
										  ? db_get_value(db_skill_dtl, 5, ds_grid_height(db_skill_dtl) + add_i[i])
										  : 0;
				skill_data.ignint_subs = db_get_value(db_skill_dtl, 7, ds_grid_height(db_skill_dtl) + add_i[i])
										 ? db_get_value(db_skill_dtl, 5, ds_grid_height(db_skill_dtl) + add_i[i])
										 : 0;
			} else {
				skill_data[$ to_add[i]] = db_get_value(db_skill_dtl, 5, ds_grid_height(db_skill_dtl) + add_i[i]);
			}
		}
	} 

	to_add = struct_get_names(skill_data.index);
	for (var i = 0; i < array_length(to_add); i++) {
		if (to_add[i] == "max_chain") {
			continue;
		}
		
		var to_array = 0;
		var index_add = 1;
		if (!is_array(skill_data.index[$ to_add[i]])) {
			skill_data.index[$ to_add[i]] = [skill_data.index[$ to_add[i]]];
			to_array = 1;
		}
		skill_data[$ to_add[i]] = [];

		for (var j = 0; j < array_length(skill_data.index[$ to_add[i]]); j++) {		
			switch (to_add[i]) {
				case "dmg":
					if (skill_data.index.dmg[j] == 0) {
						skill_data.index.dmg[j] = skill_data.chain_num + 1;
					}
					if (struct_exists(skill_data.index, "max_chain")) {
						skill_data.index.dmg[j] = clamp(skill_data.index.dmg[j], 0, skill_data.index.max_chain);
					}
					break;
				case "buff_dur":
				case "debuff_dur":
					if (skill_data.index[$ to_add[i]][j] <= 0) {
						array_push(skill_data[$ to_add[i]], 0);
						continue;
					}
					break;
			}
			
			skill_data.level = clamp(skill_data.level, 1, skill_data.eff_level);
			var val_str = db_get_value(db_skill_dtl, 5, skill_data.index[$ to_add[i]][j]);	
			var val_attr = db_get_value(db_skill_dtl, 6, skill_data.index[$ to_add[i]][j]);	
			var val_opt = db_get_value(db_skill_dtl, 7, skill_data.index[$ to_add[i]][j]);
			var val_cnt = 0;
			
			switch (to_add[i]) {
				case "dmg":
					for (var k = skill_data.index.dmg[j]; k > 0; k--) {
						if (db_get_value(db_skill_dtl, 6, k).unlocked) {		// Chain Unlocked
							break;
						}
						skill_data.index.dmg[j]--;
					}

					if (string_pos("*", val_attr.affix)) {
						skill_data.cnt = to_real(string_copy(val_attr.affix, 1, string_pos("*", val_attr.affix) - 1));
					}

					if (struct_exists(skill_data, "max_phase")) {
						if (j >= 1) {
							if (db_get_value(db_skill_dtl, 6, j).unlocked) {
								skill_data.max_phase++;
							}
						}
					}
					break;
			}
	
			// Extra Value from Stats 
			val_cnt = bots_calc_skill_attr(db_skill_dtl, skill_data, to_add, bots_stat, val_str, val_opt, skill_data.index[$ to_add[i]][j]);
			
			switch (to_add[i]) {
				case "stats_ovr":
					index_add = 0;
					if (!is_struct(skill_data[$ to_add[i]])) {
						skill_data[$ to_add[i]] = {};
					}
					if (!is_array(skill_data.stats_ovr_type)) {
						skill_data.stats_ovr_type = [skill_data.stats_ovr_type];
					}
					
					struct_set(skill_data.stats_ovr, db_get_value(db_stats, 5, skill_data.stats_ovr_type[j]), val_cnt);
					break;
			}
			
			
			if (index_add) {
				if (to_array) {
					skill_data[$ to_add[i]] = val_cnt;
				} else {
					array_push(skill_data[$ to_add[i]], val_cnt);
				}
			}
	
			if (is_struct(val_str)) {
				delete val_str;
			}
			if (is_struct(val_attr)) {
				delete val_attr;
			}
			if (is_struct(val_opt)) {
				delete val_opt;
			}
		}
	}
	
	// Extra Effect (Talent & Adjustment)
	switch (skill_data.sk_type) {
		case bots_skill_batk:
			switch (class_num) {
				case bots_class_warrior:			
					switch (skill_data.type) {
						case 1:					// Slash
						case 4:					// Doouble Slash
						case 9:					// Charged Slash
							if (skill_data.talent.s17) {
								target_cnt += 2;
							}
							if (skill_data.type == 9 && skill_data.talent.s11) {
								skill_data.no_reset = 1;
							}
						break;
						case 2:					// Blow
							skill_data.dir = (clamp(skill_data.adj.num1, 1, 2) == 1) ? "forward" : "downward";
							if (skill_data.talent.s3) {
								skill_data.dmg += 50;
								skill_data.cc_eff += 25;
								skill_data.debuff = stats_ailment.debuff_soften;
								skill_data.debuff_eff = 10;
								skill_data.debuff_dur = 3;
							}
						break;
						case 3:					// Thrust
							skill_data.move_hor = (clamp(skill_data.adj.num1, 1, 4) <= 2) ? (0.5 + (clamp(skill_data.adj.num1, 1, 4) * 0.25)) : (0.5 + (clamp(skill_data.adj.num1, 1, 4) * 0.25) + ((3 % (clamp(skill_data.adj.num1, 1, 4)-2)) + 1)*0.5);
						break;
						case 6:					// // Uppercut
							skill_data.jump = clamp(skill_data.adj.num1, 1, 2);
						break;
						case 11:				// Stab
							skill_data.move_hor = (clamp(skill_data.adj.num1, 1, 3) <= 2) ? (0.5 + (clamp(skill_data.adj.num1, 1, 3) * 0.25)) : (0.5 + (clamp(skill_data.adj.num1, 1, 3) * 0.25) + ((3 % (clamp(skill_data.adj.num1, 1, 3)-2)) + 1)*0.5);
							if (skill_data.talent.s18) {
								skill_data.dmg += 100;
							}
						break;
					}
				break;

				case bots_class_archer:				
					switch (skill_data.type) {
						case 1:			// Ordinary Shot
						case 4:			// Double Shot
						case 6:			// Acidic Shot
						case 7:			// Piercing Shot
							if (skill_data.talent.s7) {
								skill_data.dmg += 20;
							}
						break;
						case 2:			// Heavy Shot
							skill_data.move_hor = (clamp(skill_data.adj.num1, 1, 3) <= 2) ? -(0.25 + (clamp(skill_data.adj.num1, 1, 3) * 0.25)) : -(0.25 + (clamp(skill_data.adj.num1, 1, 3) * 0.25) + ((3 % (clamp(skill_data.adj.num1, 1, 3)-2)) + 1)*0.5);
							if (skill_data.talent.s12) {
								skill_data.debuff = stats_ailment.debuff_slow;
								skill_data.debuff_eff = 35;
								skill_data.debuff_dur = 3;
							}
						break;
						case 8:			// Charged Shot
							skill_data.move_hor = (clamp(skill_data.adj.num1, 1, 3) <= 2) ? -(0.25 + (clamp(skill_data.adj.num1, 1, 3) * 0.25)) : -(0.25 + (clamp(skill_data.adj.num1, 1, 3) * 0.25) + ((3 % (clamp(skill_data.adj.num1, 1, 3)-2)) + 1)*0.5);
						break;
						case 9:			// Explosive Shot
							skill_data.move_hor = (clamp(skill_data.adj.num1, 1, 3) <= 2) ? -(0.25 + (clamp(skill_data.adj.num1, 1, 3) * 0.25)) : -(0.25 + (clamp(skill_data.adj.num1, 1, 3) * 0.25) + ((3 % (clamp(skill_data.adj.num1, 1, 3)-2)) + 1)*0.5);
							if (skill_data.talent.s5) {
								skill_data.radius += 0.35;
							}
						break;
					}
				break;

				case bots_class_medic:				
					switch (skill_data.type) {
						case 2:					// Blow
						case 5:					// Uppercut
							if (skill_data.type == 2) {
								skill_data.dir = (clamp(skill_data.adj.num1, 1, 2) == 1) ? "forward" : "upward";
							}
							if (skill_data.talent.s2) {
								skill_data.cc_eff += 15;
							}
						break;
						case 3:					// Thrust
							skill_data.move_hor = (clamp(skill_data.adj.num1, 1, 3) <= 2) ? (0.5 + (clamp(skill_data.adj.num1, 1, 3) * 0.25)) : (0.5 + (clamp(skill_data.adj.num1, 1, 3) * 0.25) + ((3 % (clamp(skill_data.adj.num1, 1, 3)-2)) + 1)*0.5);
							if (skill_data.talent.s1) {
								skill_data.debuff = stats_ailment.debuff_slow;
								skill_data.debuff_eff = 20;
								skill_data.debuff_dur = 2;
							}
						break;
						case 6:					// Spinning Slash
							if (skill_data.talent.s3) {
								skill_data.target_cnt += 1;
							}
						break;
						case 8:					// Drag Out
							if (skill_data.talent.s4) {
								skill_data.debuff = stats_ailment.debuff_soften;
								skill_data.debuff_eff = 20;
								skill_data.debuff_dur = 3;
							}
						break;
					}
				break;
			}
		break;
				
		case bots_skill_derv:
			switch (class_num) {
				case bots_class_warrior:				
					switch (skill_data.type) {
						case 1:					// Self Repair
							if (skill_data.talent.s1) {
								skill_data.heal *= 1.5;
							}
						break;
						case 2:					// Redirect
							skill_data.immune_debuff = 1;
							skill_data.immune_cc = 1;
							skill_data.no_dmg = 1;
						break;
								
					}
				break;

				case bots_class_archer:				
					switch (skill_data.type) {
						case 1:					// Follow-up Shot
							if (skill_data.talent.s1) {
								skill_data.dmg += 10;
							}
						break;
					}
				break;

				case bots_class_medic:				
					switch (skill_data.type) {
						case 1:					// Dart/Serum Throw
							skill_data.dmg_type = (clamp(skill_data.adj.num1, 1, 3) == 3) ? [1, 2] : skill_data.adj.num1;
							if (skill_data.talent.s8) {
								skill_data.cd -= 2;
							}
							if (skill_data.talent.s17) {
								skill_data.cnt = 2;
							}
						break;
					}
				break;
			}
		break;

		case bots_skill_spmv:
			switch (class_num) {
				case bots_class_warrior:				
					switch (skill_data.type) {
						case 1:					// Determination
							if (skill_data.talent.s4) {
								array_push(skill_data.buff_eff, 20);
								array_push(skill_data.buff_dur, skill_data.buff_dur[0]);
								array_push(skill_data.buff, stats_list.ignore_interruption);

								for (var i = 0; i < array_length(skill_data.buff); i++) {
									skill_data.buff_dur[i] *= 1.5;
								}
							}
									
							for (var i = 0; i < array_length(skill_data.buff); i++) {
								skill_data.buff_eff[i] *= 0.25 + (clamp(skill_data.adj.num1, 1, 5) * 0.25);
								skill_data.buff_dur[i] *= 0.25 + (clamp(skill_data.adj.num2, 1, 5) * 0.25);
										
								skill_data.buff_eff[i] = round(skill_data.buff_eff[i]);
							}
							skill_data.cd *= 0.25 + (clamp(skill_data.adj.num3, 1, 5) * 0.25);
						break;
						case 2:					// Sidecut
							skill_data.move_hor = (clamp(skill_data.adj.num1, 1, 4) <= 2) ? (0.5 + (clamp(skill_data.adj.num1, 1, 4) * 0.25)) : (0.5 + (clamp(skill_data.adj.num1, 1, 4) * 0.25) + ((3 % (clamp(skill_data.adj.num1, 1, 4)-2)) + 1)*0.5);
									
							if (skill_data.talent.s8) {
								skill_data.dodge = 1;
							}
						break;
						case 3:					// Parry/Counter
							skill_data.immune_debuff = 1;
							skill_data.immune_cc = 1;
									
							if (skill_data.talent.s14) {
								skill_data.target_cnt = 3;
								array_push(skill_data.buff_eff, skill_data.buff_eff[0]);
								array_push(skill_data.buff_dur, skill_data.buff_dur[0]);
								array_push(skill_data.buff, stats_list.tc);
							}

							for (var i = 0; i < array_length(skill_data.buff); i++) {
								if (skill_data.buff_dur[i] > 0) {
									skill_data.buff_dur[i] *= 0.4 + (clamp(skill_data.adj.num1, 1, 3) * 0.3);
								}
							}
							skill_data.cd *= 0.4 + (clamp(skill_data.adj.num2, 1, 3) * 0.3);
						break;
					}
				break;

				case bots_class_archer:			
					switch (skill_data.type) {
						case 1:					// Flick Shot
							skill_data.move_hor = (clamp(skill_data.adj.num1, 1, 3) <= 2) ? -(0.25 + (clamp(skill_data.adj.num1, 1, 3) * 0.25)) : -(0.25 + (clamp(skill_data.adj.num1, 1, 3) * 0.25) + ((3 % (clamp(skill_data.adj.num1, 1, 3)-2)) + 1)*0.5);
							if (skill_data.talent.s16) {
								skill_data.target_cnt = 2;
								skill_data.move_hor -= 0.5;
							}
						break;
						case 2:					// Spike/Electric Shot
							skill_data.max_reduce = clamp(skill_data.adj.num1, 1, 3);
							if (skill_data.talent.s5) {
								skill_data.radius += 0.35;
							}
						break;
						case 3:					// Half-Moon Shot
							skill_data.move_hor = (clamp(skill_data.adj.num1, 1, 3) <= 2) ? (0.25 + (clamp(skill_data.adj.num1, 1, 3) * 0.25)) : (0.25 + (clamp(skill_data.adj.num1, 1, 3) * 0.25) + ((3 % (clamp(skill_data.adj.num1, 1, 3)-2)) + 1)*0.5);
							skill_data.max_reduce = clamp(skill_data.adj.num2, 1, 3);
							if (skill_data.talent.s19) {
								skill_data.dmg *= 0.75;
								skill_data.cnt = 2;
							}
						break;
						case 4:					// Supercharged Shot
							skill_data.max_reduce = clamp(skill_data.adj.num1, 1, 4);
						break;
					}	
				break;

				case bots_class_medic:				
					switch (skill_data.type) {
						case 1:					// Acid Vial
							if (skill_data.talent.s11) {
								for (var i = 0; i < array_length(skill_data.buff); i++) {
									skill_data.buff_dur[i] *= 1.5;
									skill_data.dur[i] *= 1.5;
								}
							}
						break;
						case 2:					// Healing Potion
							skill_data.regen *= clamp(skill_data.adj.num1, 1, 2) / 2;
							skill_data.interval *= clamp(skill_data.adj.num2, 1, 2) / 2;
							skill_data.ext = (skill_data.talent.s6);
						break;
						case 3:					// Diminisher Knife
							var avl_debuff_type = [0, stats_ailment.debuff_slow, stats_ailment.debuff_weaken, stats_ailment.debuff_break];
							var avl_debuff_eff = [0, 25, 20, 15];

							skill_data.dmg_type = clamp(skill_data.adj.num1, 1, 3);
							skill_data.debuff_type = avl_debuff_type[clamp(skill_data.adj.num2, 1, 3)];
							skill_data.debuff_eff = avl_debuff_eff[clamp(skill_data.adj.num2, 1, 3)];

							if (skill_data.talent.s18) {
								skill_data.dot = round(skill_data.dmg / 10);
								skill_data.interval = 1;
							}
						break;
					}
				break;
			}
		break;

		case bots_skill_ulti:
			switch (class_num) {
				case bots_class_warrior:				
					switch (skill_data.type) {
						case 1:					// Whirlwind Cut
							if (skill_data.talent.s13) {
								skill_data.target_cnt += 5;
								skill_data.cnt += 3;
							}
						break;
						case 2:					// Devastating Ambush
							skill_data.jump = 0.25 + (clamp(skill_data.adj.num1, 1, 3) / 2);
							if (skill_data.talent.s10) {
								skill_data.threshold = 200;
								skill_data.crit_hit = -2;
								skill_data.grave_hit = -2;
							}
						break;
						case 3:					// Daunting Shockwaves
							if (skill_data.talent.s20) {
								skill_data.dmg += 15;
								skill_data.target_cnt = 2;
							}
						break;
					}
				break;

				case bots_class_archer:				
					switch (skill_data.type) {
						case 1:					// Cloudburst Volley
							skill_data.radius = clamp(skill_data.adj.num1, 1, 3) * 1.5;
							skill_data.delay = clamp(skill_data.adj.num2, 1, 3);
							skill_data.max_reduce = clamp(skill_data.adj.num3, 0, 5) - 1;
							if (skill_data.talent.s4) {
								skill_data.cnt += clamp(skill_data.adj.num1, 1, 3) * 2;
								skill_data.target_cnt = 2;
							}
						break;
						case 2:					// Harpoon Breakout
							skill_data.max_reduce = clamp(skill_data.adj.num1, 0, 1) - 1;
							if (skill_data.talent.s9) {
								array_push(skill_data.buff_eff, 15, 10);
								array_push(skill_data.buff_dur, -1, 0);
								array_push(skill_data.buff, stats_list.dmg_output, stats_list.crit_damage);
							}
						break;
						case 3:					// Pinpoint Shooting
							skill_data.max_reduce = clamp(skill_data.adj.num1, 0, 5) - 1;
							if (skill_data.talent.s14) {
								skill_data.immune = [stats_mod.immune_knockback, stats_mod.immune_airborne];
								skill_data.auto_dodge_m = 1;
								skill_data.interval = 2;
							}
						break;
					}
				break;

				case bots_class_medic:				
					switch (skill_data.type) {
						case 1:					// Medical Kit
							var avl_heal_mul = [0.4, 0.7, 1, 1.2, 1.4];
							var avl_cd_mul = [0.6, 0.8, 1, 1.3, 1.6];

							skill_data.heal *= avl_heal_mul[clamp(skill_data.adj.num1, 1, 5)];
							skill_data.cd *= avl_cd_mul[clamp(skill_data.adj.num2, 1, 5)];
							if (skill_data.talent.s9) {
								array_push(skill_data.buff, stats_list.debuff_res);
								array_push(skill_data.buff_eff, 35);
								array_push(skill_data.buff_dur, skill_data.buff_dur[0]);
								skill_data.ext = [stats_mod.cleanse_debuff_all];
							}
							if (skill_data.talent.s16) {
								skill_data.total -= 1 + (skill_data.level >= 7);
							}
						break;
						case 2:					// Corrosive Concentration
							skill_data.spd = clamp(skill_data.adj.num1, 1, 3) * 10;
							if (skill_data.talent.s14) {
								skill_data.radius += 0.5;
							}
							if (skill_data.talent.s15) {
								skill_data.dmg[1] += 400;
								skill_data.crit_hit = 1;
							}
						break;
						case 3:					// CBC Booster
							var avl_buff_type = [
								0, stats_list.hp_scale, stats_list.atk_scale, stats_list.def_scale, stats_list.agility, stats_list.dmg_output, stats_list.ignore_interruption, 
								stats_list.cd_reduction, stats_list.crit_buildup, stats_list.crit_damage, stats_list.crit_protection, stats_list.healing_output, stats_list.atk_spd,
								stats_list.ab, stats_list.armor_str, stats_list.dmg_res, stats_list.cc_power, stats_list.buff_power, stats_list.charge_spd
							];
							var avl_buff_eff = [
								0, 15, 15, 15, 25, 15, 15, 
								10, 15, 20, 10, 15, 20, 
								25, 15, 20, 15, 15, 20
							];

							if (skill_data.talent.s16) {
								skill_data.total -= 1 + (skill_data.level >= 7);
							}
							if (skill_data.talent.s20) {
								if (skill_data.adj.num2 > 1) {
									skill_data.regen = clamp(skill_data.adj.num2, 1, 3) - 1;
									skill_data.buff_amp += (clamp(skill_data.adj.num2, 1, 3) - 1) * 0.25;
								}
							}
							skill_data.buff = avl_buff_type[clamp(skill_data.adj.num1, 1, 18)];
							skill_data.buff_eff = avl_buff_eff[clamp(skill_data.adj.num1, 1, 18)] * skill_data.buff_amp;
						break;
					}
				break;
			}
		break;

		case bots_skill_intg:
			if (skill_data.type == 1) {
				switch (class_num) {
					case bots_class_warrior:	
						if (skill_data.talent.s7) {
							array_push(skill_data.debuff, stats_list.crit_protection);
							array_push(skill_data.debuff_eff, 10);
							array_push(skill_data.debuff_dur, 10);
						}
					break;

					case bots_class_archer:	
						if (skill_data.talent.s2) {
							skill_data.max_stack *= 2;
						}
					break;

					case bots_class_medic:	
						skill_data.ext = (skill_data.talent.s6);			// 1 = infuse attack
						skill_data.ext += (skill_data.talent.s19) * 2;		// 2 = microbooster (3 = ext1 + ext2)

						if (skill_data.talent.s13) {
							skill_data.buff = [stats_mod.infuse_acid, stats_list.acid_do];
							skill_data.buff_eff = [0, 15];
						}
					break;
				}
			} else {
				switch (skill_data.type) {
					/*
					case 2:					// Grave Hit Module
									
					break;*/
					case 3:					// Superbody Module
						switch (class_num) {
							case bots_class_warrior:
								if (skill_data.talent.s5) {
									skill_data.threshold = 30;
									struct_remove(skill_data, "fade_dur");
								}
							break;
						}
					break;
					case 4:					// Sequence Breaker Module
						switch (class_num) {
							case bots_class_warrior:
								array_push(skill_data.buff, stats_list.atk_scale);
								array_push(skill_data.buff_eff, 15);
								array_push(skill_data.buff_dur, 5);
								skill_data.regen = 2;
								skill_data.interval = 1;
								skill_data.dur = 5;
							break;
						}
					break;
					/*
					case 5:					// Substitute Care Module
								
					break;
					case 6:					// Fatalism Prevention Module
								
					break;*/
				}
			}
		break;

		case bots_skill_uniq:
			switch (class_num) {
				case bots_class_warrior:	
					skill_data.auto_active = clamp(skill_data.adj.num1, 1, 2) - 1;
				break;

				case bots_class_archer:	
					if (skill_data.talent.s10) {
						skill_data.threshold = 4;
						skill_data.max_stack += 3;
					}
				break;

				case bots_class_medic:	
					skill_data.auto_active = clamp(skill_data.adj.num1, 1, 2) - 1;	
					skill_data.threshold = 100 + (clamp(skill_data.adj.num2, 1, 21) - 1) * 10;
				break;
			}
		break;

		case bots_skill_ext:
			switch (class_num) {
				case bots_class_warrior:				
					switch (skill_data.type) {
						case 1:			// Wavecleaver
						case 2:			// Doom Drop
						case 5:			// Starfall Strike
							skill_data.auto_active = clamp(skill_data.adj.num1, 1, 2) - 1;
						break;
					}
				break;

				case bots_class_archer:				
					switch (skill_data.type) {
						case 1:			// Twin Talon Shot
						case 2:			// Soulsnare Shot
						case 5:			// Predator's Trilogy
							skill_data.auto_active = clamp(skill_data.adj.num1, 1, 2) - 1;
						break;
					}
				break;

				case bots_class_medic:				
					switch (skill_data.type) {
						case 1:			// Stasis Flask/Bolt
						case 2:			// Abyss Flask
						case 5:			// Caustic Drip
							skill_data.auto_active = clamp(skill_data.adj.num1, 1, 2) - 1;
						break;
					}
				break;
			}
		break;
	}
}

function bots_get_batk(db_bots, db_batk, db_stats, bots_stat, class_num, batk_seqtype = bots_batk_norm) {
	var bots_data = db_get_row(db_bots, db_type_bots, class_num);
	var batk_seqtype_list = [0, bots_data.batk_set, bots_data.alt_batk_set, bots_data.air_batk_set];
	var seq_data = db_get_batk_seq(db_bots, db_batk, class_num, batk_seqtype, batk_seqtype_list[batk_seqtype]);

	for (var i = 0; i < array_length(struct_get_names(seq_data)); i++) {
		if (seq_data[$ string($"num{i+1}")].type == 0) {	// Empty
			for (var j = array_length(struct_get_names(seq_data)) - 1; j >= i; j--) {
				struct_remove(seq_data, string($"num{j+1}"));
			}
			break;
		} else {											// Non-Empty BATK
			var db_skdtl = db_create_skill_dtl(db_batk, class_num, 1, seq_data[$ string($"num{i+1}")].type);
			var sk_data = db_get_row(db_batk, db_type_sklist, seq_data[$ string($"num{i+1}")].type);

			struct_remove(seq_data[$ string($"num{i+1}")], "seq_type");
			struct_remove(seq_data[$ string($"num{i+1}")], "set_num");
			struct_remove(seq_data[$ string($"num{i+1}")], "unlock_lv");

			var to_add = ["level", "eff_level", "upgraded", "sprite", "talent"];
			for (var j = 0; j < array_length(to_add); j++) {
				if (!is_struct(sk_data[$ to_add[j]])) {
					seq_data[$ string($"num{i+1}")][$ to_add[j]] = sk_data[$ to_add[j]];
				} else {
					seq_data[$ string($"num{i+1}")][$ to_add[j]] = variable_clone(sk_data[$ to_add[j]]);
				}
			}
			seq_data[$ string($"num{i+1}")].sk_type = sk_data.type;

			bots_set_skill_attr(db_skdtl, db_stats, seq_data[$ string($"num{i+1}")], bots_stat, class_num);

			// Sequence Effect
			switch (class_num) {
				case bots_class_warrior:				
					switch (sk_data.type) {
						case 2:					// Blow
							var to_del = 1;
							if (i+1 >= 2) {
								if (seq_data[$ string($"num{i}")].type == 1 || seq_data[$ string($"num{i}")].type == 4) {
									to_del = 0;
								}
							}

							if (to_del) {
								struct_remove(seq_data[$ string($"num{i+1}")], "buff");
								struct_remove(seq_data[$ string($"num{i+1}")], "buff_dur");
								struct_remove(seq_data[$ string($"num{i+1}")], "buff_eff");
							}
						break;
						case 5:					// Twin Thrust
							if (i+1 >= 2) {
								if (sk_data.talent.s6 && seq_data[$ string($"num{i}")].type == 3) {
									sk_data.triple_thrust = 1;
								}
							}
						break;
					}
				break;
				
				case bots_class_archer:		
					if (i+1 >= 2) {
						if (struct_exists(seq_data[$ string($"num{i}")], "chain")) {
							if (seq_data[$ string($"num{i}")].chain == 3) {
								sk_data.cnt += ceil(seq_data[$ string($"num{i}")].chain / 2);
							}
						}
					}
				
					switch (sk_data.type) {
						case 2:					// Heavy Shot
							sk_data.buff_eff *= (i + 1);
						break;
						case 3:					// Multi Shot
							if (sk_data.talent.s18) {
								sk_data.buff = stats_list.atk_spd;
								sk_data.buff_eff = sk_data.cnt * 3;
								sk_data.buff_dur = 0;
							}
						break;
						case 4:					// Double Shot
							if (i+1 >= 3) {
								if (sk_data.talent.s8 && seq_data[$ string($"num{i-1}")].type == 6 && sk_data.type == 6) {
									seq_data[$ string($"num{i}")].dmg_type = skill_dmg_type.acid;
									seq_data[$ string($"num{i}")].int_power += 10;
								}
							}
						break;
						case 5:					// Parallel Shot
							if (i+1 >= 2) {
								if (seq_data[$ string($"num{i}")].chain == 3) {
									sk_data.cnt += ceil(seq_data[$ string($"num{i}")].chain / 2);
								}
							}
						break;
					}
				break;
				
				case bots_class_medic:				
					switch (sk_data.type) {
						case 2:					// Blow
							if (i+1 >= 2) {
								if (seq_data[$ string($"num{i}")].type == 1 || seq_data[$ string($"num{i}")].type == 4) {
									sk_data.buff_eff *= max(seq_data[$ string($"num{i}")].chain, 1);
								}
							}
						break;
						case 5:					// Uppercut
							var to_del = 1;
							if (i+1 < array_length(struct_get_names(seq_data))) {
								if (seq_data[$ string($"num{i+2}")].type == 2) {
									to_del = 0;
								}
							}

							if (to_del) {
								struct_remove(seq_data[$ string($"num{i+1}")], "buff");
								struct_remove(seq_data[$ string($"num{i+1}")], "buff_dur");
								struct_remove(seq_data[$ string($"num{i+1}")], "buff_eff");
							}
						break;
						case 6:					// Spinning Slash
							if (i+1 >= 2) {
								sk_data.buff_eff *= (i + 1);
								sk_data.dmg = sk_data.dmg[(seq_data[$ string($"num{i}")].chain < 3 && seq_data[$ string($"num{i+1}")].talent.m8)];
							}
						break;
						case 8:					// Drag Out
							if (sk_data.talent.s4) {
								if (i+1 == array_length(struct_get_names(seq_data))) {
									sk_data.crit_hit = 1;
								} else if (seq_data[$ string($"num{i+2}")].type == 0) {
									sk_data.crit_hit = 1;
								}
							}
						break;
					}
				break;
			}

			delete sk_data;
			ds_grid_destroy(db_skdtl);
		}
	}

	delete bots_data;
	return seq_data;
}

function bots_get_skill(db_bots, db_skill, db_stats, bots_stat, class_num, skill_type, skill_num, ext = 0) {
	// ext => db_skill_derv = db_bots
	var bots_data = db_get_row(db_bots, db_type_bots, class_num);
	var sk_used = [0, 0, bots_data.derv, bots_data.spmv, bots_data.ulti, 1, 1, 0];
	var result = {};

	switch (skill_type) {
		case bots_skill_derv:
			var seq_alt_data = db_get_batk_seq(db_bots, ext, class_num, 1, bots_data.alt_batk_set);
			
			var no_alt = 1;
			if (array_length(struct_get_names(seq_alt_data)) > 0) {
				if (seq_alt_data.num1.type == 0) {
					no_alt = 0;
				}
			} 

			if (no_alt) {						// DERV
				var db_skdtl = db_create_skill_dtl(db_skill, class_num, skill_type, sk_used[skill_type]);
				var sk_data = db_get_row(db_skill, db_type_sklist, sk_used[skill_type]);

				var to_add = ["level", "eff_level", "upgraded", "sprite", "talent"];
				for (var i = 0; i < array_length(to_add); i++) {
					if (!is_struct(sk_data[$ to_add[i]])) {
						result[$ to_add[i]] = sk_data[$ to_add[i]];
					} else {
						result[$ to_add[i]] = variable_clone(sk_data[$ to_add[i]]);
					}
				}
				result.type = sk_used[skill_type];
				result.sk_type = skill_type;

				result.adj = {};
				var sk_adj = db_get_skill_adj(db_skill, class_num, skill_type, sk_data.num, db_bots);
				for (var i = 0; i < array_length(struct_get_names(sk_adj)); i++) {
					struct_set(result.adj, string($"num{i+1}"), sk_adj[$ string($"num{i+1}")].adj_val);
				}

				bots_set_skill_attr(db_skdtl, db_stats, result, bots_stat, class_num);

				delete sk_adj;
				delete sk_data;
				ds_grid_destroy(db_skdtl);
			} else {							// Alt BATK
				result = bots_get_batk(db_bots, ext, db_stats, bots_stat, class_num, 2);
			}
			
			delete seq_alt_data;
			break;
		break;

		case bots_skill_intg:
		case bots_skill_ext:
			if (skill_type == bots_skill_intg && skill_num == 1) {
				skill_num = [1];
			} else {
				skill_num = [];
				for (var i = (skill_type == bots_skill_intg); i < (ds_grid_height(db_skill) - 20); i++) {
					array_push(skill_num, i + 1);
				}
			}
			
			var to_add = ["level", "eff_level", "upgraded", "sprite", "talent", "unlock_lv"];
			
			for (var i = 0; i < array_length(skill_num); i++) {
				var db_skdtl = db_create_skill_dtl(db_skill, class_num, skill_type, skill_num[i]);
				var sk_data = db_get_row(db_skill, db_type_sklist, skill_num[i]);

				if (sk_data.unlock_lv <= 0 || (skill_type == bots_skill_ext && !sk_data.unlocked)) {
					delete sk_data;
					ds_grid_destroy(db_skdtl);
					continue;
				}
				
				for (var j = 0; j < array_length(to_add); j++) {
					if (!is_struct(sk_data[$ to_add[j]])) {
						if (sk_data.str_name == "") {
							result[$ to_add[j]] = sk_data[$ to_add[j]];
						} else {
							if (!struct_exists(result, sk_data.str_name)) {
								struct_set(result, sk_data.str_name, {});
							}
							result[$ sk_data.str_name][$ to_add[j]] = sk_data[$ to_add[j]];
						}
					} else {
						if (sk_data.str_name == "") {
							result[$ to_add[j]] = variable_clone(sk_data[$ to_add[j]]);
						} else {
							if (!struct_exists(result, sk_data.str_name)) {
								struct_set(result, sk_data.str_name, {});
							}
							result[$ sk_data.str_name][$ to_add[j]] = variable_clone(sk_data[$ to_add[j]]);
						}
					}
				}
				
				if (sk_data.str_name == "") {
					result.sk_type = skill_type;	
					result.type = skill_num[i];
					result.adj = {};	
				} else {
					result[$ sk_data.str_name].sk_type = skill_type;	
					result[$ sk_data.str_name].type = skill_num[i];
					result[$ sk_data.str_name].adj = {};
				}
				
				var sk_adj = db_get_skill_adj(db_skill, class_num, skill_type, sk_data.num, db_bots);
				for (var j = 0; j < array_length(struct_get_names(sk_adj)); j++) {
					if (sk_data.str_name == "") {
						struct_set(result.adj, string($"num{j+1}"), sk_adj[$ string($"num{j+1}")].adj_val);
					} else {
						struct_set(result[$ sk_data.str_name].adj, string($"num{j+1}"), sk_adj[$ string($"num{j+1}")].adj_val);
					}
					
				}
				
				if (sk_data.str_name == "") {
					bots_set_skill_attr(db_skdtl, db_stats, result, bots_stat, class_num);	
				} else {
					bots_set_skill_attr(db_skdtl, db_stats, result[$ sk_data.str_name], bots_stat, class_num);	
				}

				delete sk_adj;
				delete sk_data;
				ds_grid_destroy(db_skdtl);
			}
		break;

		default:
			if (skill_type == bots_skill_spmv) {
				var unmod = 0;
				
				if (variable_instance_exists(id, "spmv")) {
					if (is_struct(id.spmv)) {
						var spmv_str = id.spmv;
						if (spmv_str.type == skill_num) {
							unmod = 1;
						}
					}
				}
				
				if (unmod) {
					switch (class_num) {
						case bots_class_archer:		
							switch (spmv_str.type) {
								case 1: 				// Flick Shot
									spmv_str.cd /= 2;
								break;
								case 2: 				// Spike/Electric Shot
									spmv_str.debuff_dur += 2;
								break;
								case 3: 				// Half-Moon Shot
									spmv_str.cd -= 7;
								break;
							}
						break;
						case bots_class_medic:		
							switch (spmv_str.type) {
								case 1: 				// Acid Vial
									spmv_str.cd /= 2;
								break;
								case 2: 				// Healing Potion
									spmv_str.dur *= 1.5;
								break;
								case 3: 				// Diminisher Knife
									spmv_str.debuff_amp += 0.5;
								break;
							}
						break;
					}

					delete result;
					return -1;
				}
			}
			
			var db_skdtl = db_create_skill_dtl(db_skill, class_num, skill_type, skill_num);
			var sk_data = db_get_row(db_skill, db_type_sklist, skill_num);

			var to_add = ["level", "eff_level", "upgraded", "sprite", "talent"];
			for (var i = 0; i < array_length(to_add); i++) {
				if (!is_struct(sk_data[$ to_add[i]])) {
					result[$ to_add[i]] = sk_data[$ to_add[i]];
				} else {
					result[$ to_add[i]] = variable_clone(sk_data[$ to_add[i]]);
				}
			}
			result.type = skill_num;
			result.sk_type = skill_type;

			result.adj = {};
			var sk_adj = db_get_skill_adj(db_skill, class_num, skill_type, sk_data.num, db_bots);
			for (var i = 0; i < array_length(struct_get_names(sk_adj)); i++) {
				struct_set(result.adj, string($"num{i+1}"), sk_adj[$ string($"num{i+1}")].adj_val);
			}
			
			bots_set_skill_attr(db_skdtl, db_stats, result, bots_stat, class_num);
		
			// Skill Effect
			switch (skill_type) {
				case bots_skill_uniq:
					switch (class_num) {
						case bots_class_warrior:				
							result.buff_eff *= bots_data.level;
						break;
					}
				break;
			}

			delete sk_adj;
			delete sk_data;
			ds_grid_destroy(db_skdtl);
	}

	return result;
}
