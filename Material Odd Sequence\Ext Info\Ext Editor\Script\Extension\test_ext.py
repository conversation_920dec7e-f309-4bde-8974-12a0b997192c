"""Module for testing .dll functions"""

import time
import ctypes

dll = ctypes.CDLL(
    "C:/Backup/Project/Material-Odence/Material Odd Sequence/Ext Info/"
    + "Ext Editor/Script/Extension/nexaflux.dll"
)

# ———— Function prototypes ————
# PoolHandle systemInit(int numThreads);
dll.systemInit.restype = ctypes.c_double
dll.systemInit.argtypes = [ctypes.c_double]

# void systemCleanup(PoolHandle pool);
dll.systemCleanup.restype = None
dll.systemCleanup.argtypes = [ctypes.c_double]

# TaskHandle taskCreate(int id);
dll.taskCreate.restype = ctypes.c_double
dll.taskCreate.argtypes = [ctypes.c_double]

# std::future<int>* taskUpdate(TaskHandle task, PoolHandle pool);
dll.taskUpdate.restype = ctypes.c_double
dll.taskUpdate.argtypes = [ctypes.c_double, ctypes.c_double]

# int taskGetResult(std::future<int>* futurePtr);
dll.taskGetResult.restype = ctypes.c_double
dll.taskGetResult.argtypes = [ctypes.c_double]

# void taskDestroy(TaskHandle task);
dll.taskDestroy.restype = None
dll.taskDestroy.argtypes = [ctypes.c_double]

dll.testPreset.restype = None
dll.testPreset.argtypes = []


# ———— Usage ————
def main():
    # 1) init pool of 4 threads
    pool = dll.systemInit(4)
    if not pool:
        raise RuntimeError("Failed to init thread pool")

    # 2) create 7 tasks
    tasks = [dll.taskCreate(i) for i in range(7)]

    # 3) kick off all tasks
    futures = []
    for t in tasks:
        # returns a pointer to a std::future<int>
        fut_ptr = dll.taskUpdate(t, pool)
        if not fut_ptr:
            raise RuntimeError("Failed to enqueue task")
        futures.append(fut_ptr)

    # Optional: do other stuff here...
    time.sleep(0.1)  # simulate frame delay

    # 4) collect results
    results = [dll.taskGetResult(f) for f in futures]
    print("Results:", results)  # should all be 100

    # 5) cleanup tasks + pool
    for t in tasks:
        dll.taskDestroy(t)
    dll.systemCleanup(pool)

    dll.testPreset()

if __name__ == "__main__":
    main()
