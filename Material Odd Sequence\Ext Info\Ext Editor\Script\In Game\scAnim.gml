function anim_create(follow, attr = [], keyframe = [], keyattr = []) {
    // follow = instance/struct yang diikuti 
    // attr = [spr, img, spd [, rot, z, xscale, yscale]] (default = [undefined, undefined, 1 [, 0, depth, 1, 1]])
    var total_time = 0;                                 // Jumlah step
    for (var i = 0; i < array_length(keyattr); i++) {
        total_time += room_speed * keyattr[i].time;
    }

    var anim_str = {
        start : 0,
        time : 0,                                       // Step sekarang                         
        follow : follow,
        spr : (attr[0] != undefined) ? attr[0] : follow.sprite_index,
        img : (attr[1] != undefined) ? attr[1] : follow.image_index,
        spd : (attr[2] != undefined) ? attr[2] : 1,
        keyframe : keyframe,                            // [[{x, y, rot, z, xscale, yscale}, ...], [{x, y, rot, z, xscale, yscale}, ...], ...]
        keyattr : keyattr,                              // [{time, curve, [action -> name-arg-time-done]}, ...]
        max_time : total_time,
        x : 0,
        y : 0
    };

    if (!instance_exists(follow)) {                     // Struct
        anim_str.z = keyframe[0][0].z;
        anim_str.rot = keyframe[0][0].rot;
        anim_str.xscale = keyframe[0][0].xscale;
        anim_str.yscale = keyframe[0][0].yscale;
    }

    return anim_str;
}

function anim_set_keyframe(preset = -1, point_arr = -1) {
    // point_arr = struct start-control-end point (1 kurva) -> [{x, y, rot, z, xscale, yscale}, ...] (kalau preset == -1)
    // preset = preset animasi
    var result = [];

    switch (preset) {
        case -1:
            array_push(result, point_arr);
            break;
        default: 
            switch (preset) {
                case 1:     // Lab - Mix
                    result = [
                        [{x: 0, y: 0, rot: 0, z: depth, xscale: 1, yscale: 1}, {x: 27, y: -1, rot: 0, z: depth, xscale: 1, yscale: 1}, {x: 44, y: -13, rot: 15, z: depth, xscale: 1, yscale: 1}],
                        [{x: 44, y: -13, rot: 15, z: depth, xscale: 1, yscale: 1}, {x: 27, y: -1, rot: 15, z: depth, xscale: 1, yscale: 1}, {x: 0, y: 0, rot: 0, z: depth, xscale: 1, yscale: 1}],
                        [{x: 0, y: 0, rot: 0, z: depth, xscale: 1, yscale: 1}, {x: -27, y: -1, rot: 0, z: depth, xscale: 1, yscale: 1}, {x: -44, y: -13, rot: -15, z: depth, xscale: 1, yscale: 1}],
                        [{x: -44, y: -13, rot: -15, z: depth, xscale: 1, yscale: 1}, {x: -27, y: -1, rot: -15, z: depth, xscale: 1, yscale: 1}, {x: 0, y: 0, rot: 0, z: depth, xscale: 1, yscale: 1}],
                        
                        [{x: 0, y: 0, rot: 0, z: depth, xscale: 1, yscale: 1}, {x: 27, y: -1, rot: 0, z: depth, xscale: 1, yscale: 1}, {x: 44, y: -13, rot: 15, z: depth, xscale: 1, yscale: 1}],
                        [{x: 44, y: -13, rot: 15, z: depth, xscale: 1, yscale: 1}, {x: 27, y: -1, rot: 15, z: depth, xscale: 1, yscale: 1}, {x: 0, y: 0, rot: 0, z: depth, xscale: 1, yscale: 1}],
                        [{x: 0, y: 0, rot: 0, z: depth, xscale: 1, yscale: 1}, {x: -27, y: -1, rot: 0, z: depth, xscale: 1, yscale: 1}, {x: -44, y: -13, rot: -15, z: depth, xscale: 1, yscale: 1}],
                        [{x: -44, y: -13, rot: -15, z: depth, xscale: 1, yscale: 1}, {x: -27, y: -1, rot: -15, z: depth, xscale: 1, yscale: 1}, {x: 0, y: 0, rot: 0, z: depth, xscale: 1, yscale: 1}]
                    ];
                    break;
				case 2:     // Lab - Craft
                    result = [
                        [{x: 0, y: 0, rot: -45, z: depth, xscale: -1, yscale: 1}, {x: 123, y: 303, rot: -45, z: depth, xscale: -1, yscale: 1}, {x: 438, y: 216, rot: -135, z: depth, xscale: -1, yscale: 1}, {x: 385, y: 91, rot: -135, z: depth, xscale: -1, yscale: 1}], 
                        [{x: 385, y: 91, rot: -135, z: depth, xscale: -1, yscale: 1}, {x: 249, y: -230, rot: -135, z: depth, xscale: -1, yscale: 1}, {x: 95, y: 219, rot: 0, z: depth, xscale: -1, yscale: 1}], 
                        [{x: 95, y: 219, rot: 0, z: depth, xscale: -1, yscale: 1}, {x: 166, y: 69, rot: 0, z: depth, xscale: -1, yscale: 1}, {x: 254, y: 351, rot: -45, z: depth, xscale: -1, yscale: 1}, {x: 317, y: 299, rot: -45, z: depth, xscale: -1, yscale: 1}], 
                        [{x: 317, y: 299, rot: -45, z: depth, xscale: -1, yscale: 1}, {x: 442, y: 231, rot: -45, z: depth, xscale: -1, yscale: 1}, {x: 420, y: 48, rot: -135, z: depth, xscale: -1, yscale: 1}], 
                        [{x: 420, y: 48, rot: -135, z: depth, xscale: -1, yscale: 1}, {x: 370, y: -188, rot: -135, z: depth, xscale: -1, yscale: 1}, {x: 138, y: 24, rot: 0, z: depth, xscale: -1, yscale: 1}, {x: 95, y: 219, rot: 0, z: depth, xscale: -1, yscale: 1}], 
                        [{x: 95, y: 219, rot: 0, z: depth, xscale: -1, yscale: 1}, {x: 164, y: 15, rot: 0, z: depth, xscale: -1, yscale: 1}, {x: 304, y: 435, rot: -45, z: depth, xscale: -1, yscale: 1}, {x: 338, y: 335, rot: -45, z: depth, xscale: -1, yscale: 1}], 
                        [{x: 338, y: 335, rot: -45, z: depth, xscale: -1, yscale: 1}, {x: 385, y: 250, rot: -45, z: depth, xscale: -1, yscale: 1}, {x: 420, y: 80, rot: -135, z: depth, xscale: -1, yscale: 1}], 
                        [{x: 420, y: 80, rot: -135, z: depth, xscale: -1, yscale: 1}, {x: 437, y: -35, rot: -135, z: depth, xscale: -1, yscale: 1}, {x: 385, y: -64, rot: -180, z: depth, xscale: -1, yscale: 1}], 
                        [{x: 385, y: -64, rot: -180, z: depth, xscale: -1, yscale: 1}, {x: 175, y: -197, rot: -180, z: depth, xscale: -1, yscale: 1}, {x: 95, y: 219, rot: 0, z: depth, xscale: -1, yscale: 1}],
                        [{x: 95, y: 219, rot: 0, z: depth, xscale: -1, yscale: 1}, {x: 0, y: 0, rot: -45, z: depth, xscale: -1, yscale: 1}], 
                    ];
                    break;
				
            }
    }

    return result;
}

function anim_set_keyattr(preset = -1, attr_struct = -1, ext = -1) {
    // attr_struct = struct time-curve -> {time, curve}
    // preset = preset animasi
	// ext = tambahan (opsional)
    var result = [];

    switch (preset) {
        case -1:
            array_push(result, attr_struct);
            break;
        default: 
            switch (preset) {
                case 1:     // Lab - Mix
                    for (var i = 0; i < 8; i++) {
						if (i < 7) {
							array_push(result, {time: 0.2, curve: global.anim_curve.easeout});
						} else {
							array_push(result, {time: 0.2, curve: global.anim_curve.linear});
						}
                    }
                    break;
				case 2:		// Lab - Craft		(ext = [selected.type, selected.group])
					result = [
						{time: 0.35, curve: global.anim_curve.linear}, {time: 0.15, curve: global.anim_curve.linear, action: {name: "fx_craft", arg: [soLabCraft1], time: 99, done: 0}}, {time: 0.2, curve: global.anim_curve.linear},
						{time: 0.35, curve: global.anim_curve.linear}, {time: 0.15, curve: global.anim_curve.linear, action: {name: "fx_craft", arg: [soLabCraft1], time: 99, done: 0}}, {time: 0.2, curve: global.anim_curve.linear},
						{time: 0.35, curve: global.anim_curve.linear}, {time: 0.2, curve: global.anim_curve.linear}, {time: 0.15, curve: global.anim_curve.linear, action: {name: "fx_craft", arg: [((ext[0] == 3) ? soLabScrap : ((ext[1] == 1) ? soLabCraft1 : soLabCraft2))], time: 99, done: 0}}, {time: 0.2 + (0.2*(ext[0] == 3)), curve: global.anim_curve.easeout}
					];
					break;
				
            }
    }

    return result;
}

function anim_update_parent_pos(anim_str) {
    // anim_str = struct dari anim_create
    anim_str.x = anim_str.follow.x;
    anim_str.y = anim_str.follow.y;
}

function anim_update_target_attr(anim_str, val_type = anim_value_add, spd_type = anim_speed_all) {
    // anim_str = struct dari anim_create
    // time = 1 step dari anim_str.time
	
	if (anim_str.start == 1) {
		var time = anim_str.time;
		time++;
		var spd_arr = [0, global.anim_speed.game, global.anim_speed.team, global.anim_speed.enemy];
		var add_time = delta_t((time - anim_str.time) * anim_str.spd * spd_arr[spd_type]);
	    anim_str.time += add_time;
    
	    var result = anim_calc_bezier(anim_str, anim_str.time);
		switch (val_type) {
			case 1:			// Substitute (Instance)
				anim_str.follow.x = ((val_type == 1) ? anim_str.x + result.x : result.x);
		        anim_str.follow.y = ((val_type == 1) ? anim_str.y + result.y : result.y);
		        anim_str.follow.depth = result.z;
		        anim_str.follow.image_angle = result.rot;
		        anim_str.follow.image_xscale = result.xscale;
		        anim_str.follow.image_yscale = result.yscale;
		        //anim_str.follow.image_speed = anim_str.spd;
		        //anim_str.follow.image_index = anim_str.img;
			break;
			case 2:			// Relative (Struct)
				anim_str.x = ((val_type == 1) ? anim_str.x + result.x : result.x);
		        anim_str.y = ((val_type == 1) ? anim_str.y + result.y : result.y);
		        anim_str.z = result.z;
		        anim_str.rot = result.rot;
		        anim_str.xscale = result.xscale;
		        anim_str.yscale = result.yscale;
			break;
		}
		
		var segment_time = 0;
		for (var i = 0; i < array_length(anim_str.keyattr); i++) {
	        segment_time += room_speed * anim_str.keyattr[i].time;
			if (anim_str.time <= segment_time) {
				if (struct_exists(anim_str.keyattr[i], "action")) {
					var action_time = (segment_time - (room_speed * anim_str.keyattr[i].time) + (room_speed * anim_str.keyattr[i].action.time));
					if (!anim_str.keyattr[i].action.done && (anim_str.keyattr[i].action.time == -1 || anim_str.time + add_time >= action_time || anim_str.time + add_time >= segment_time)) {
						anim_str.keyattr[i].action.done = 1;
						method_call(anim_str.follow.mtd[$ anim_str.keyattr[i].action.name], anim_str.keyattr[i].action.arg);
					}
				}
				break;
			}
	    }
		
	    if (anim_str.time >= anim_str.max_time) {
	        anim_str.start = -1;
			anim_str.time = 0;
			for (var i = 0; i < array_length(anim_str.keyattr); i++) {
				if (struct_exists(anim_str.keyattr[i], "action")) {
					anim_str.keyattr[i].action.done = 0;
				}
			}
	    }
	}
}

function anim_calc_bezier(anim_str, t) {
    // t = step sekarang
    var total_steps = 0;
    for (var i = 0; i < array_length(anim_str.keyattr); i++) {
        total_steps += anim_str.keyattr[i].time;
    }

    var current_step = 0;
    for (var i = 0; i < array_length(anim_str.keyframe); i++) {
        var segment_start = current_step / total_steps * anim_str.max_time;
        var segment_end = (current_step + anim_str.keyattr[i].time) / total_steps * anim_str.max_time;

        if (t >= segment_start && t <= segment_end) {
            var relative_t = (t - segment_start) / (segment_end - segment_start);
            return anim_calc_decasteljau(anim_str.keyframe[i], relative_t, anim_str.keyattr[i].curve);
        }

        current_step += anim_str.keyattr[i].time;
    }

    return anim_str.keyframe[array_length(anim_str.keyframe) - 1][array_length(anim_str.keyframe[array_length(anim_str.keyframe) - 1]) - 1];
}

function anim_calc_decasteljau(points, t, curve = global.anim_curve.linear) {
    // points = struct start-control-end point (1 kurva) -> [{x, y, rot, z, xscale, yscale}, ...]
    // t = 0 .. 1
    if (array_length(points) == 1) {
        return points[0];
    }

    var next_points = [];
    for (var i = 0; i < array_length(points) - 1; i++) {
        if (curve.name == "linear") {
            array_push(next_points, {
                x: ((1 - t) * points[i].x + t * points[i + 1].x),
                y: ((1 - t) * points[i].y + t * points[i + 1].y),
                rot: ((1 - t) * points[i].rot + t * points[i + 1].rot),
                z: points[i].z,
                xscale: ((1 - t) * points[i].xscale + t * points[i + 1].xscale),
                yscale: ((1 - t) * points[i].yscale + t * points[i + 1].yscale)
            });
        } else {
            var curve_t = animcurve_channel_evaluate(curve, t);
            array_push(next_points, {
                x: points[i].x + (((1 - t) * points[i].x + t * points[i + 1].x) - points[i].x) * curve_t,
                y: points[i].y + (((1 - t) * points[i].y + t * points[i + 1].y) - points[i].y) * curve_t,
                rot: points[i].rot + (((1 - t) * points[i].rot + t * points[i + 1].rot) - points[i].rot) * curve_t,
                z: points[i].z,
                xscale: points[i].xscale + (((1 - t) * points[i].xscale + t * points[i + 1].xscale) - points[i].xscale) * curve_t,
                yscale: points[i].yscale + (((1 - t) * points[i].yscale + t * points[i + 1].yscale) - points[i].yscale) * curve_t
            });
        }
    }

    return anim_calc_decasteljau(next_points, t, curve);
}


// Anim Constants
#macro anim_preset_mm_mix 1
#macro anim_preset_mm_craft 2

#macro anim_value_substitute 1
#macro anim_value_add 2

#macro anim_speed_all 1
#macro anim_speed_team 2
#macro anim_speed_enemy 3

global.anim_curve = {
	linear : animcurve_get_channel(acUI, "linear"),
	easein : animcurve_get_channel(acUI, "ease_in"),
	easeout : animcurve_get_channel(acUI, "ease_out"),
	easeinout : animcurve_get_channel(acUI, "ease_inout")
};

global.anim_speed = {
	game : 1,
	team : 1,
	enemy : 1,
};
