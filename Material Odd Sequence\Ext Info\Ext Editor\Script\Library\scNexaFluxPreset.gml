/*
 * File for storing preset animations for NexaFlux
 */

enum NEXAFLUX_PRESET {
	NONE,
	SWORD_DEF,
	SWORD_DEF_DRAWN,
	SWORD_MOVE,
	SWORD_MOVE_DRAWN,
	SWORD_DRAWING_1,
	SWORD_SHEATHING_1,
	SWORD_IDLE,
	SWORD_IDLE_DRAWN,
	SWORD_ACC1_IDLE,
	SWORD_ACC1_MOVE,
	SWORD_SLASH_1,
	SWORD_SLASH_1_RTN,
	SWORD_SLASH_2,
	SWORD_SLASH_2_RTN
}


function NexaFluxPresetAction(_funcs = [], _times = [], _args = undefined) constructor {
	funcs = _funcs;				// 1D/2D array of NEXAFLUX_ACTION
	times = _times;				// 1D array of time in frames (int)
	args = _args ?? [];			// 2D array of arguments to be passed to the action

	if (!is_array(funcs)) {
		funcs = [_funcs];
	}
	if (!is_array(times)) {
		times = [_times];
	}
	
	args = [_args];
}

function NexaFluxPreset(preset_enum, action_struct = undefined) constructor {
	types = [];                     // 1D array of "translate_x", "translate_y", "rotate", "scale_x", "scale_y", "depth", "alpha"
	values = [];                    // 3D array of real values
	key_times = [];                 // 2D array of key times in frames (int) 
	curves = [];                    // 1D/2D array of NEXAFLUX_CURVES
	
	nexaflux = other;
	actions = action_struct;		// NexaFluxPresetAction()
	loop_count = undefined;
	on_end = undefined;

	switch (preset_enum) {
		case NEXAFLUX_PRESET.SWORD_DEF:
			types = [NEXAFLUX_VALUE.TRANSLATE_X, NEXAFLUX_VALUE.TRANSLATE_Y, NEXAFLUX_VALUE.ROTATE, NEXAFLUX_VALUE.SCALE_X, NEXAFLUX_VALUE.SCALE_Y, NEXAFLUX_VALUE.DEPTH, NEXAFLUX_VALUE.INIT_DEPTH];
			values = [
				[[62.862]], [[28.388]], [[-165]], [[1]], [[-1]], [[4]], [[1]]
			];
			key_times = [
				[0], [0], [0], [0], [0], [0], [0]
			];
			loop_count = 1;
			break;
			
		case NEXAFLUX_PRESET.SWORD_DEF_DRAWN:
			types = [NEXAFLUX_VALUE.TRANSLATE_X, NEXAFLUX_VALUE.TRANSLATE_Y, NEXAFLUX_VALUE.ROTATE, NEXAFLUX_VALUE.SCALE_X, NEXAFLUX_VALUE.SCALE_Y, NEXAFLUX_VALUE.DEPTH, NEXAFLUX_VALUE.INIT_DEPTH];
			values = [
				[[66.399]], [[37.072]], [[-315]], [[1]], [[1]], [[-4]], [[-1]]
			];
			key_times = [
				[0], [0], [0], [0], [0], [0], [0]
			];
			loop_count = 1;
			break;

		case NEXAFLUX_PRESET.SWORD_MOVE:
			types = [NEXAFLUX_VALUE.TRANSLATE_X, NEXAFLUX_VALUE.TRANSLATE_Y, NEXAFLUX_VALUE.ROTATE, NEXAFLUX_VALUE.SCALE_X, NEXAFLUX_VALUE.SCALE_Y, NEXAFLUX_VALUE.DEPTH, NEXAFLUX_VALUE.INIT_DEPTH];
			values = [
				[[62.862], [68.212], [73.062], [68.212], [62.862]], 
				[[28.388], [36.412], [26.788], [36.412], [28.388]], 
				[[-165], [-160], [-165]], 
				[[1]], [[-1]], [[4]], [[1]]
			];
			key_times = [
				[0, 15, 30, 45, 60], 
				[0, 15, 30, 45, 60], 
				[0, 30, 60],
				[0], [0], [0], [0]
			];
			loop_count = -1;
			break;

		case NEXAFLUX_PRESET.SWORD_IDLE:
			types = [NEXAFLUX_VALUE.TRANSLATE_X, NEXAFLUX_VALUE.TRANSLATE_Y, NEXAFLUX_VALUE.ROTATE, NEXAFLUX_VALUE.SCALE_X, NEXAFLUX_VALUE.SCALE_Y, NEXAFLUX_VALUE.DEPTH, NEXAFLUX_VALUE.INIT_DEPTH];
			values = [
				[[62.862], [62.862], [62.862]], 
				[[28.388], [33.4], [28.388]], 
				[[-165]], [[1]], [[-1]], [[4]], [[1]]
			];
			key_times = [
				[0, 60, 120], 
				[0, 60, 120], 
				[0], [0], [0], [0], [0]
			];
			curves = [
				NEXAFLUX_CURVES.EASE_INOUT_QUAD, 
				NEXAFLUX_CURVES.EASE_INOUT_QUAD,
				NEXAFLUX_CURVES.LINEAR,
				NEXAFLUX_CURVES.LINEAR,
				NEXAFLUX_CURVES.LINEAR,
				NEXAFLUX_CURVES.LINEAR,
				NEXAFLUX_CURVES.LINEAR
			];
			loop_count = -1;
			break;
		
		case NEXAFLUX_PRESET.SWORD_DRAWING_1:
			types = [NEXAFLUX_VALUE.TRANSLATE_X, NEXAFLUX_VALUE.TRANSLATE_Y, NEXAFLUX_VALUE.ROTATE, NEXAFLUX_VALUE.SCALE_X, NEXAFLUX_VALUE.SCALE_Y, NEXAFLUX_VALUE.DEPTH, NEXAFLUX_VALUE.INIT_DEPTH];
			values = [
				[[62.862], [248.526], [261.837], [233.298], [195.284], [130.459], [81.353], [68.973], [71.839], [65.138]], 
				[[28.388], [-21.232], [-32.73], [-47.387], [-49.409], [-66.695], [-54.256], [-45.537], [19.046], [40.115]], 
				[[-165], [-165], [-197.1], [-231.7], [-254.7], [-315]], 
				[[1]], 
				[[-1], [-1], [-0.7], [0.1], [1]], 
				[[4], [-4]], [[1], [-1]]
			];
			key_times = [
				[0, 16, 20, 26, 30, 41, 51, 55, 65, 70], 
				[0, 16, 20, 26, 30, 41, 51, 55, 65, 70], 
				[0, 16, 30, 41, 51, 70], 
				[0], [0, 20, 30, 41, 51], 
				[0, 30], [0, 30]
			];
			loop_count = 1;
			on_end = new NexaFluxAction(NEXAFLUX_ACTION.TOGGLE_STATE_DRAW);
			break;
		
		case NEXAFLUX_PRESET.SWORD_SHEATHING_1:
			types = [NEXAFLUX_VALUE.TRANSLATE_X, NEXAFLUX_VALUE.TRANSLATE_Y, NEXAFLUX_VALUE.ROTATE, NEXAFLUX_VALUE.SCALE_X, NEXAFLUX_VALUE.SCALE_Y, NEXAFLUX_VALUE.DEPTH, NEXAFLUX_VALUE.INIT_DEPTH];
			values = [
				[[65.138], [100.431], [125.394], [152.94], [223.526], [242.464], [248.526], [62.862]], 
				[[40.115], [47.002], [48.723], [47.002], [17.734], [-4.647], [-21.232], [28.388]], 
				[[-315], [-297.9], [-262], [-200], [-181.3], [-172.8], [-165], [-165]], 
				[[1]], 
				[[1], [0.5], [-1]], 
				[[-4], [4]], [[-1], [1]]
			];
			key_times = [
				[0, 8, 15, 26, 35, 45, 50, 60], 
				[0, 8, 15, 26, 35, 45, 50, 60], 
				[0, 8, 15, 26, 35, 45, 50, 60], 
				[0], [0, 8, 15], 
				[0, 15], [0, 15]
			];
			loop_count = 1;
			on_end = new NexaFluxAction(NEXAFLUX_ACTION.TOGGLE_STATE_DRAW);
			break;
		
		case NEXAFLUX_PRESET.SWORD_IDLE_DRAWN:
			types = [NEXAFLUX_VALUE.TRANSLATE_X, NEXAFLUX_VALUE.TRANSLATE_Y, NEXAFLUX_VALUE.ROTATE, NEXAFLUX_VALUE.SCALE_X, NEXAFLUX_VALUE.SCALE_Y, NEXAFLUX_VALUE.DEPTH, NEXAFLUX_VALUE.INIT_DEPTH];
			values = [
				[[65.138], [62.138], [65.138]], 
				[[40.115], [45.115], [40.115]], 
				[[-315], [-317], [-315]], 
				[[1]], [[1]], [[-4]], [[-1]]
			];
			key_times = [
				[0, 60, 120], 
				[0, 60, 120], 
				[0, 60, 120], 
				[0], [0], [0], [0]
			];
			curves = [
				NEXAFLUX_CURVES.EASE_INOUT_QUAD, 
				NEXAFLUX_CURVES.EASE_INOUT_QUAD,
				NEXAFLUX_CURVES.EASE_INOUT_QUAD,
				NEXAFLUX_CURVES.LINEAR,
				NEXAFLUX_CURVES.LINEAR,
				NEXAFLUX_CURVES.LINEAR,
				NEXAFLUX_CURVES.LINEAR
			];
			loop_count = -1;
			break;
		
		case NEXAFLUX_PRESET.SWORD_MOVE_DRAWN:
			types = [NEXAFLUX_VALUE.TRANSLATE_X, NEXAFLUX_VALUE.TRANSLATE_Y, NEXAFLUX_VALUE.ROTATE, NEXAFLUX_VALUE.SCALE_X, NEXAFLUX_VALUE.SCALE_Y, NEXAFLUX_VALUE.DEPTH, NEXAFLUX_VALUE.INIT_DEPTH];
			values = [
				[[66.399], [61.731], [53.518], [44.236], [53.518], [61.731], [66.399]], 
				[[37.072], [44.304], [46.852], [45.123], [46.852], [44.304], [37.072]], 
				[[-315], [-325], [-315]], 
				[[1]], [[1]], [[-4]], [[-1]]
			];
			key_times = [
				[0, 10, 20, 30, 40, 50, 60], 
				[0, 10, 20, 30, 40, 50, 60], 
				[0, 30, 60], 
				[0], [0], [0], [0]
			];
			loop_count = -1;
			break;

		case NEXAFLUX_PRESET.SWORD_SLASH_1:
			types = [NEXAFLUX_VALUE.TRANSLATE_X, NEXAFLUX_VALUE.TRANSLATE_Y, NEXAFLUX_VALUE.ROTATE, NEXAFLUX_VALUE.SCALE_X, NEXAFLUX_VALUE.SCALE_Y, NEXAFLUX_VALUE.DEPTH, NEXAFLUX_VALUE.INIT_DEPTH];
			values = [
				[[65.138], [28.123], [13.489], [55.669], [120.229], [152.94], [100.431]], 
				[[40.115], [14.291], [-12.394], [-24.445], [-13.255], [13.43], [33.229]], 
				[[-315], [-270.3], [-232.8], [-212.3], [-285], [-373.8], [-510.3]], 
				[[1]], [[1], [1], [0.5], [-1]], [[-4], [4]], [[-1], [1]]
			];
			key_times = [
				[0, 3, 8, 11, 14, 20, 24], 
				[0, 3, 8, 11, 14, 20, 24], 
				[0, 3, 8, 11, 14, 20, 24], 
				[0], [0, 14, 20, 24], [0, 14], [0, 14]
			];
			loop_count = 1;
			break;
		
		case NEXAFLUX_PRESET.SWORD_SLASH_1_RTN:
			types = [NEXAFLUX_VALUE.TRANSLATE_X, NEXAFLUX_VALUE.TRANSLATE_Y, NEXAFLUX_VALUE.ROTATE, NEXAFLUX_VALUE.SCALE_X, NEXAFLUX_VALUE.SCALE_Y, NEXAFLUX_VALUE.DEPTH, NEXAFLUX_VALUE.INIT_DEPTH];
			values = [
				[[100.431], [117.934], [96.012], [61.002], [54.806], [65.138]], 
				[[33.229], [16.586], [5.884], [7.167], [26.409], [40.115]], 
				[[-510.3], [-445.4], [-362.848], [-301.059], [-287], [-315.3]], 
				[[1]], [[-1], [0.5], [1]], [[4], [-4]], [[-1], [1]]
			];
			key_times = [
				[0, 13, 19, 24, 27, 30], 
				[0, 13, 19, 24, 27, 30], 
				[0, 13, 19, 24, 27, 30], 
				[0], [0, 19, 24], [0, 13], [0, 13]
			];
			loop_count = 1;
			break;

		case NEXAFLUX_PRESET.SWORD_SLASH_2:
			types = [NEXAFLUX_VALUE.TRANSLATE_X, NEXAFLUX_VALUE.TRANSLATE_Y, NEXAFLUX_VALUE.ROTATE, NEXAFLUX_VALUE.SCALE_X, NEXAFLUX_VALUE.SCALE_Y, NEXAFLUX_VALUE.DEPTH, NEXAFLUX_VALUE.INIT_DEPTH];
			values = [
				[[100.431], [45.339], [85.797], [145.193], [161.548], [111.621]], 
				[[33.229], [9.987], [-32.193], [-39.079], [-9.812], [42.698]], 
				[[-510.3], [-494.4], [-524.9], [-576.2], [-669.2], [-870.6]], 
				[[1]], [[-1], [-0.5], [1]], [[4], [-4]], [[-1], [1]]
			];
			key_times = [
				[0, 5, 10, 13, 16, 20], 
				[0, 5, 10, 13, 16, 20], 
				[0, 5, 10, 13, 16, 20], 
				[0], [0, 5, 10], [0, 13], [0, 13]
			];
			loop_count = 1;
			break;
		
		case NEXAFLUX_PRESET.SWORD_SLASH_2_RTN:
			types = [NEXAFLUX_VALUE.TRANSLATE_X, NEXAFLUX_VALUE.TRANSLATE_Y, NEXAFLUX_VALUE.ROTATE, NEXAFLUX_VALUE.SCALE_X, NEXAFLUX_VALUE.SCALE_Y, NEXAFLUX_VALUE.DEPTH, NEXAFLUX_VALUE.INIT_DEPTH];
			values = [
				[[111.621], [109.383], [97.751], [78.103], [55.417], [49.518], [65.138]], 
				[[42.698], [21.436], [1.558], [-4.169], [10.879], [25.927], [40.115]], 
				[[-870.6], [-492.079], [-403.707], [-331.696], [-297.055], [-272.737], [-315]], 
				[[1]], [[1]], [[-4]], [[1]]
			];
			key_times = [
				[0, 6, 12, 18, 24, 27, 30], 
				[0, 6, 12, 18, 24, 27, 30], 
				[0, 1, 6, 12, 18, 24, 30], 
				[0], [0], [0], [0]
			];
			loop_count = 1;
			break;

	}
}
