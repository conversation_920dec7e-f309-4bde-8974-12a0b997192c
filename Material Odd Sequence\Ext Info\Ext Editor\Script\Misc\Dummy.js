// Skill Details
/*var level = 0;
var val_init = 110;
var val_low = 8;
var val_mid = 3;
var val_high = 8;
var suffix = "%";
var rounded = true;

var result = 0;
if (level > 0) {
    if (rounded) {
        result = Math.round(val_init + (val_low * (level - 1)) + (val_mid * Math.floor(level / 5) * (level - 4)) + (val_high * Math.floor(level / 7) * (level - 7)));
    } else {
        result = (val_init + (val_low * (level - 1)) + (val_mid * Math.floor(level / 5) * (level - 4)) + (val_high * Math.floor(level / 7) * (level - 7))).toFixed(1);
    }
    console.log("Level", level, "=", result.toString() + suffix);
    console.log("\n" + val_init + ", " + val_low + ", " + val_mid + ", " + val_high + "\t\t[" + result.toString() + suffix + "]");
} else {
    var lv9_res = 0;
    var lv10_res = 0;
    for (var i = 1; i <= 10; i++) {
        if (rounded) {
            result = Math.round(val_init + (val_low * (i - 1)) + (val_mid * Math.floor(i / 5) * (i - 4)) + (val_high * Math.floor(i / 7) * (i - 7)));
        } else {
            result = (val_init + (val_low * (i - 1)) + (val_mid * Math.floor(i / 5) * (i - 4)) + (val_high * Math.floor(i / 7) * (i - 7))).toFixed(1);
        }
        console.log("Level", i, "=", result.toString() + suffix);

        if (i == 9) {
            lv9_res = result;
        } else if (i == 10) {
            lv10_res = result;
        }
    }
    console.log("\n" + val_init + ", " + val_low + ", " + val_mid + ", " + val_high + "\t\t[" + lv9_res.toString() + suffix + ", " + lv10_res.toString() + suffix + "]");
}*/


// DB Group
/*var ctrl_val = 2;
var type1_cnt = 6;
var type2_cnt = 3;
var type3_cnt = 6;
var type4_cnt = 3;
var type5_cnt = 2;
var type6_cnt = 18 * ctrl_val;
var type7_cnt = 3;
var all_type = [type1_cnt, type2_cnt, type3_cnt, type4_cnt, type5_cnt, type6_cnt, type7_cnt];

for (var i = 0; i < 59; i++) {
    var current_type = 0;
    var type_sum = 0;
    var last_type_sum = 0;
    for (var j = 0; j < all_type.length; j++) {
        type_sum += all_type[j];
        if (j > 0) {
            last_type_sum += all_type[j - 1];
        }
        if (i + 1 <= type_sum) {
            current_type = j + 1;
            break;
        }
    }

    if (current_type == 6) {
        console.log("row", i + 1, "=", current_type, "sum =", type_sum, "last_sum =", last_type_sum, "group =", Math.ceil((i + 1 - last_type_sum) / (type6_cnt / ctrl_val)));
    } else {
        console.log("row", i + 1, "=", current_type, "sum =", type_sum, "relative row =", i + 1 - last_type_sum);
    }
}*/



// Anim
/*function deCasteljau(points, t, curve = 1) {
    if (points.length === 1) {
        return points[0];
    }

    let nextPoints = [];
    for (let i = 0; i < points.length - 1; i++) {
        if (curve == 1) {
            nextPoints.push({
                x: ((1 - t) * points[i].x + t * points[i + 1].x),
                y: ((1 - t) * points[i].y + t * points[i + 1].y),
                rot: ((1 - t) * points[i].rot + t * points[i + 1].rot)
            });
        } else {
            let curve_t = t;
            nextPoints.push({
                x: points[i].x + (((1 - t) * points[i].x + t * points[i + 1].x) - points[i].x) * curve_t,
                y: points[i].y + (((1 - t) * points[i].y + t * points[i + 1].y) - points[i].y) * curve_t,
                rot: ((1 - t) * points[i].rot + t * points[i + 1].rot)
            });
        }
    }
    //console.log(t)
    return deCasteljau(nextPoints, t);
}

function interpolateBezier(t, pointsArray, pointsAttr, max_step) {
    let totalSteps = 0;
    for (let i = 0; i < pointsAttr.length; i++) {
        totalSteps += pointsAttr[i].step;

        if (t + 1 == totalSteps) {
            console.log("Breakpoint =", t + 1)
        }
    }

    let currentStep = 0;
    //console.log(t)
    for (let i = 0; i < pointsArray.length; i++) {
        let segmentStart = currentStep / totalSteps * max_step;
        let segmentEnd = (currentStep + pointsAttr[i].step) / totalSteps * max_step;

        if (t >= segmentStart && t <= segmentEnd) {
            let relativeT = (t - segmentStart) / (segmentEnd - segmentStart);
            return deCasteljau(pointsArray[i], relativeT);
        }

        currentStep += pointsAttr[i].step;

    }

    return pointsArray[pointsArray.length - 1][pointsArray[pointsArray.length - 1].length - 1];
}

const pointsArray = [
    [{ x: 0, y: 0, rot: 0 }, { x: 27, y: 1, rot: 0 }, { x: 44, y: 13, rot: -15 }],
    [{ x: 44, y: 13, rot: -15 }, { x: 27, y: 1, rot: -15 }, { x: 0, y: 0, rot: 0 }],
    [{ x: 0, y: 0, rot: 0 }, { x: -27, y: 1, rot: 0 }, { x: -44, y: 13, rot: 15 }]
];

const pointsAttr = [
    { step: 33 },
    { step: 34 },
    { step: 33 }
];

let t = 0;
let inc_val = 1;
let max_step = 100;

while (t <= max_step) {
    console.log("step", t + 1, "=", interpolateBezier(t, pointsArray, pointsAttr, max_step));
    t += inc_val;
}

if (max_step % inc_val) {
    console.log("step", t + 1, "=", pointsArray[pointsArray.length - 1][pointsArray[pointsArray.length - 1].length - 1]);
}*/


// Test value
/*function clamp(value, min, max) {
    return (Math.min(Math.max(value, min), max));
}
function lerp(a, b, t) {
    return a + (b - a) * t;
}

let current = 375;
let target = 100;
let spd = 60 / 60;

for (let i = 0; i < 300; i++) {
   if (target >= current) {
        var last = current;
        var next = Math.ceil(lerp(current, target, 0.2 * spd) * 1000) / 1000;
        current = last + clamp(next - last, -20 * spd, 20 * spd);
        console.log(`step ${i} = ${current}`);
        if (current >= target) {
            break;
        }
    } else {
        var last = current;
        var next = Math.floor(lerp(current, target, 0.2 * spd) * 1000) / 1000;
        current = last + clamp(next - last, -20 * spd, 20 * spd);
        console.log(`step ${i} = ${current}`);
        if (current <= target) {
            break;
        }
    }
}*/

// Gravity 
// Fixed timestep physics with consistent summation
function runConsistentSumSimulation(renderFps) {
    // Constants
    const FIXED_DT = 1/240; // Fixed physics timestep (in seconds)
    const target_y = 200;
    const max_vel = 5;
    const strength = 0.3;
    const ORIGINAL_FPS = 60; // Our reference frame rate
    
    // State variables
    let vel_y = -7.5;
    let val = 0;
    let sum_y_min = vel_y;
    let sum_y_pos = 0;
    let frameCount = 0;
    
    // Time tracking
    const frameTime = 1 / renderFps; // Time per rendered frame
    let accumulator = 0;
    let simulationTime = 0;
    
    // For summation sampling - use 60Hz reference for all simulations
    const summationInterval = 1 / ORIGINAL_FPS;
    let nextSummationTime = 0;
    
    console.log(`Running simulation at ${renderFps} FPS with consistent summation:`);
    
    // Main simulation loop
    while (val < target_y) {
        // Add frame time to accumulator
        accumulator += frameTime;
        
        // Run physics steps
        while (accumulator >= FIXED_DT) {
            // Physics update with fixed timestep
            vel_y += strength * FIXED_DT * 60;
            
            if (vel_y < 0) {
                vel_y += (strength * 5) * FIXED_DT * 60;
                vel_y = Math.min(vel_y, 0);
                val += (vel_y * 2) * FIXED_DT * 60;
            } else {
                vel_y = Math.min(vel_y, max_vel);
                val += vel_y * FIXED_DT * 60;
            }
            
            // Check if we've reached a summation sample point
            // This ensures all simulations sample at the same rate regardless of render FPS
            if (simulationTime >= nextSummationTime) {
                // Track values at consistent intervals
                if (val < 0) {
                    sum_y_min += val;
                } else {
                    sum_y_pos += val;
                }
                
                // Set next summation time
                nextSummationTime += summationInterval;
                
                // Print sample state (only for 60 Hz samples)
                console.log(`sample ${Math.round(simulationTime * ORIGINAL_FPS) + 1}, time: ${simulationTime.toFixed(3)}s, val = ${val.toFixed(2)}`);
            }
            
            // Reduce accumulator and advance simulation time
            accumulator -= FIXED_DT;
            simulationTime += FIXED_DT;
        }
        
        frameCount++;
    }
    
    console.log(`sum_y_min = ${sum_y_min.toFixed(2)}, sum_y_pos = ${sum_y_pos.toFixed(2)}`);
    return { 
        frames: frameCount, 
        simulationTime: simulationTime.toFixed(3), 
        sum_y_min: sum_y_min.toFixed(2), 
        sum_y_pos: sum_y_pos.toFixed(2) 
    };
}

// Run simulations at different frame rates
const result60 = runConsistentSumSimulation(60);
const result30 = runConsistentSumSimulation(30);

console.log("\nComparison:");
console.log(`60 FPS: ${result60.frames} frames, simulation time: ${result60.simulationTime}s`);
console.log(`sum_y_min = ${result60.sum_y_min}, sum_y_pos = ${result60.sum_y_pos}`);

console.log(`30 FPS: ${result30.frames} frames, simulation time: ${result30.simulationTime}s`);
console.log(`sum_y_min = ${result30.sum_y_min}, sum_y_pos = ${result30.sum_y_pos}`);


/*const DESIGN_FPS = 60;
const strengthPerFrame = 0.3;
const maxVelPerFrame = 5;
const accelPerSec = strengthPerFrame * DESIGN_FPS;
const maxVelPerSec = maxVelPerFrame * DESIGN_FPS;

let vel_y = -0.125 * DESIGN_FPS;
let val = 0;
const target = 200;
const SPEED_PER_FRAME = 1 / DESIGN_FPS;  // dt per “frame”

function step() {
    const dt = SPEED_PER_FRAME;  // simulate a fixed-step loop
    vel_y += accelPerSec * dt;
    if (vel_y < 0) {
        vel_y += accelPerSec * 5 * dt;
        vel_y = Math.min(vel_y, 0);
    } else {
        vel_y = Math.min(vel_y, maxVelPerSec);
    }
    val += vel_y * dt;
    return val;
}

let i = 0;
while (val < target) {
    console.log(`step ${++i}, val = ${val.toFixed(2)}`);
    step();
}*/



// Struct/Object size calculator
/*function memorySizeOf(obj) {
	var bytes = 0;

	function sizeOf(obj) {
		if (obj !== null && obj !== undefined) {
			switch (typeof obj) {
				case "number":
					bytes += 8;
					break;
				case "string":
					bytes += obj.length * 2;
					break;
				case "boolean":
					bytes += 4;
					break;
				case "object":
					var objClass = Object.prototype.toString.call(obj).slice(8, -1);
					if (objClass === "Object" || objClass === "Array") {
						for (var key in obj) {
							if (!obj.hasOwnProperty(key)) continue;
							sizeOf(obj[key]);
						}
					} else bytes += obj.toString().length * 2;
					break;
			}
		}
		return bytes;
	}

	function formatByteSize(bytes) {
		if (bytes < 1024) return bytes + " bytes";
		else if (bytes < 1048576) return (bytes / 1024).toFixed(3) + " KiB";
		else if (bytes < 1073741824) return (bytes / 1048576).toFixed(3) + " MiB";
		else return (bytes / 1073741824).toFixed(3) + " GiB";
	}

	return formatByteSize(sizeOf(obj));
}

let struct = {
    num2: {
        ext: {
            dodge: {
                eff_level: 1,
                dmg_type: 1,
                unlock_lv: 1,
                total: 2,
                adj: {
                    num1: 1
                },
                multiplier: "atk",
                cd_type: 0,
                target: 3,
                max_dur: 3,
                type: 1,
                sk_type: 7,
                dmg: 159,
                target_cnt: 1,
                upgraded: 0,
                index: {
                    dmg: [1],
                    max_dur: [2]
                },
                spd: 40,
                cc: 101,
                level: 1,
                auto_active: 0,
                talent: {},
                int_power: 70,
                ignint_bonus: 0,
                recoil: 35,
                ignint_subs: 100,
                cnt: 2
            },
            switch_in: {
                eff_level: 1,
                dmg_type: 1,
                unlock_lv: 10,
                total: 2,
                adj: {},
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                interval: 0.20,
                max_dur: 3,
                type: 3,
                sk_type: 7,
                dmg: [90, 20],
                target_cnt: 1,
                upgraded: 0,
                cd: 5,
                index: {
                    cd: [6],
                    interval: [4],
                    dmg: [1, 2],
                    max_dur: [5],
                    cnt: [3]
                },
                spd: 40,
                level: 1,
                talent: {},
                int_power: 70,
                ignint_bonus: 0,
                recoil: 25,
                ignint_subs: 100,
                cnt: 3
            },
            retaliate: {
                eff_level: 1,
                dmg_type: 3,
                unlock_lv: 1,
                adj: {
                    num1: 1
                },
                multiplier: "-hp_enemy",
                cd_type: 0,
                target: 3,
                max_dur: 3,
                type: 2,
                sk_type: 7,
                dmg: 10,
                target_cnt: 1,
                upgraded: 0,
                index: {
                    dmg: [1],
                    max_dur: [2]
                },
                spd: 60,
                cc: 105,
                level: 1,
                auto_active: 0,
                talent: {},
                int_power: 100,
                ignint_bonus: 0,
                recoil: 20,
                ignint_subs: 100
            },
            switch_out: {
                eff_level: 1,
                dmg_type: 1,
                unlock_lv: 20,
                adj: {},
                multiplier: "atk",
                cd_type: 0,
                target: 3,
                type: 4,
                sk_type: 7,
                dmg: 25,
                target_cnt: 1,
                upgraded: 0,
                index: {
                    dmg: [1],
                    cnt: [2]
                },
                spd: 50,
                level: 1,
                talent: {},
                int_power: 40,
                ignint_bonus: 0,
                recoil: 25,
                ignint_subs: 100,
                cnt: 15
            }
        },
        data: {
            xp: 5,
            air_batk_set: 1,
            sprite_w: 436,
            core: 9,
            unlocked: 1,
            bearing: 12,
            num: 2,
            spmv_mod: 2,
            level: 29,
            crust: 12,
            name: "Archer",
            weapon: 4,
            batk_set: 2,
            ulti: 1,
            alt_batk_set: 1,
            spmv: 1,
            derv: 1
        },
        stats: {
            armor_dmg: 0,
            physical_do: 0,
            acid_do: 0,
            armor_burst: 50,
            armor_str: 100,
            dmg_res: 40,
            batk_db: 0,
            batk_do: 6,
            batk_power: 0,
            batk_eff: 0,
            derv_db: 0,
            derv_do: 0,
            aoe_dmg: 20,
            physical_db: 0,
            derv_power: 0,
            def_pen: 4,
            acid_db: 0,
            derv_eff: 0,
            ignore_int: 0,
            spmv_db: 0,
            armor_pen: 15,
            heal_otp: 100,
            cd_rdc: 0,
            spmv_do: 0,
            acid_rdc: 0,
            spmv_power: 0,
            physical_rdc: 5,
            crit_prt: 15,
            spmv_eff: 0,
            recoil_rdc: 50,
            agility: 100,
            debuff_res: 0,
            ulti_db: 0,
            dmg_output: 103,
            hp: 13985,
            atk: 2879,
            ulti_do: 15,
            def: 1445,
            hp_flat: 4050,
            atk_spd: 100,
            ulti_power: 0,
            atk_flat: 2218,
            ranged_do: 14,
            def_flat: 308,
            ulti_eff: 0,
            cc_power: 100,
            hp_scale: 100,
            cc_res: 0,
            atk_scale: 100,
            intg_db: 0,
            def_scale: 115,
            melee_do: 0,
            intg_do: 0,
            intg_power: 0,
            charge_spd: 100,
            crit_bld: 25,
            intg_eff: 0,
            crit_dmg: 125,
            buff_power: 100,
            accuracy: 50,
            ammo_cap: 0,
            mags_cap: 0,
            reload_spd: 0
        },
        batk: {
            num4: {
                chain_num: 4,
                eff_level: 7,
                dmg_type: 1,
                adj: {},
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                type: 7,
                sk_type: 1,
                dmg: 63,
                target_cnt: 2,
                upgraded: 0,
                dmg_excess: 0,
                cd: 0.20,
                index: {
                    dmg: [5],
                    max_chain: 7,
                    target_cnt: [8],
                    dmg_excess: [9],
                    cd: [10]
                },
                spd: 40,
                level: 2,
                talent: {
                    m10: 1,
                    s7: 0
                },
                int_power: 40,
                ignint_bonus: 10,
                recoil: 15,
                ignint_subs: 0
            },
            num5: {
                chain_num: 0,
                eff_level: 7,
                dmg_type: 1,
                radius: 100,
                adj: {
                    num1: 1
                },
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                type: 9,
                sk_type: 1,
                dmg: 154,
                target_cnt: 0,
                upgraded: 0,
                cd: 2,
                index: {
                    radius: [2],
                    dmg: [1],
                    cc_eff: [3],
                    cd: [4]
                },
                move_hor: -0.50,
                spd: 35,
                cc_eff: 80,
                cc: 103,
                level: 5,
                talent: {
                    s5: 0
                },
                int_power: 65,
                ignint_bonus: 20,
                recoil: 35,
                ignint_subs: 0
            },
            num2: {
                chain_num: 2,
                eff_level: 7,
                dmg_type: 1,
                adj: {},
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                type: 1,
                sk_type: 1,
                dmg: 47,
                target_cnt: 1,
                upgraded: 0,
                cd: 0.10,
                index: {
                    dmg: [3],
                    max_chain: 7,
                    cd: [8]
                },
                spd: 40,
                level: 4,
                talent: {
                    m6: 1,
                    m7: 1,
                    s7: 0
                },
                int_power: 30,
                ignint_bonus: 10,
                recoil: 10,
                ignint_subs: 0
            },
            num3: {
                chain_num: 3,
                eff_level: 7,
                dmg_type: 1,
                adj: {},
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                type: 7,
                sk_type: 1,
                dmg: 18,
                target_cnt: 2,
                upgraded: 0,
                dmg_excess: 0,
                cd: 0.20,
                index: {
                    dmg: [4],
                    max_chain: 7,
                    target_cnt: [8],
                    dmg_excess: [9],
                    cd: [10]
                },
                spd: 40,
                level: 2,
                talent: {
                    m10: 1,
                    s7: 0
                },
                int_power: 40,
                ignint_bonus: 10,
                recoil: 15,
                ignint_subs: 0,
                cnt: 2
            },
            num1: {
                chain_num: 1,
                eff_level: 7,
                dmg_type: 1,
                adj: {},
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                type: 1,
                sk_type: 1,
                dmg: 41,
                target_cnt: 1,
                upgraded: 0,
				cd: 0.10,
                index: {
                    dmg: [2],
                    max_chain: 7,
                    cd: [8]
                },
                spd: 40,
                level: 4,
                talent: {
                    m6: 1,
                    m7: 1,
                    s7: 0
                },
                int_power: 30,
                ignint_bonus: 10,
                recoil: 10,
                ignint_subs: 0
            }
        },
        air_batk: {},
        spmv_mod: {
            eff_level: 10,
            dmg_type: 1,
            radius: 112,
            adj: {
                num1: 3
            },
            multiplier: "atk",
            cd_type: 1,
            target: 5,
            max_reduce: 3,
            type: 2,
            sk_type: 3,
            dmg: 395,
            target_cnt: 20,
            upgraded: 3,
            dmg_excess: 50,
            cd: 15,
            index: {
                cc_dur: [3],
                dmg: [1],
                target_cnt: [6],
                dmg_excess: [7],
                cd: [8],
                radius: [2],
                buff_eff: [4],
                buff_dur: [5]
            },
            move_hor: 1.50,
            buff_eff: 15,
            spd: 40,
            buff_dur: 5,
            dash: 1,
            buff: -2,
            cc: 107,
            level: 4,
            cc_dur: 3,
            jump: 1,
            move_hor_delay: 0.50,
            talent: {
                s5: 0,
                s17: 0
            },
            int_power: 100,
            ignint_bonus: 0,
            recoil: 35,
            ignint_subs: 100
        },
        intg: {
            max_stack: 5,
            eff_level: 1,
            sk_type: 5,
            dmg_type: 0,
            target_cnt: 1,
            upgraded: 0,
            unlock_lv: 1,
            cd: 3,
            index: {
                max_stack: [2],
                buff_eff: [1],
                buff_dur: [0],
                cd: [3]
            },
            buff_eff: 8,
            buff_dur: [0],
            adj: {},
            buff: 2,
            level: 1,
            multiplier: "",
            cd_type: 3,
            target: 3,
            talent: {
                s2: 0
            },
            type: 1
        },
        module: {
            grave: {
                eff_level: 1,
                sk_type: 5,
                dmg: 100,
                dmg_type: 0,
                target_cnt: 1,
                upgraded: 0,
                unlock_lv: 1,
                index: {
                    dmg: [2],
                    buildup: [1]
                },
                adj: {},
                buildup: 5,
                level: 1,
                multiplier: "",
                cd_type: 0,
                target: 3,
                talent: {},
                type: 2
            }
        },
        uniq: {
            threshold: 5,
            max_stack: 4,
            sk_type: 6,
            eff_level: 1,
            dmg_type: 0,
            target_cnt: 1,
            upgraded: 0,
            index: {
                max_stack: [1]
            },
            adj: {},
            level: 1,
            multiplier: "",
            cd_type: 0,
            target: 3,
            talent: {
                s10: 0
            },
            type: 1
        },
        ulti: {
            eff_level: 10,
            dmg_type: 1,
            radius: 4.50,
            adj: {
                num2: 3,
                num3: 6,
                num1: 3
            },
            multiplier: "atk",
            cd_type: 1,
            target: 3,
            delay: 3,
            max_reduce: 4,
            type: 1,
            sk_type: 4,
            dmg: 46,
            target_cnt: 1,
            upgraded: 0,
            cd: 45,
            index: {
                cd: [3],
                dmg: [1],
                cnt: [2]
            },
            spd: 50,
            level: 3,
            talent: {
                s4: 0
            },
            int_power: 60,
            ignint_bonus: 0,
            recoil: 30,
            ignint_subs: 100,
            cnt: 19
        },
        spmv: {
            eff_level: 10,
            dmg_type: 1,
            adj: {
                num1: 3
            },
            multiplier: "atk",
            cd_type: 1,
            target: 3,
            type: 1,
            sk_type: 3,
            dmg: 149,
            target_cnt: 1,
            upgraded: 0,
            cd: 6,
            index: {
                move_hor: [2],
                dmg: [1],
                cd: [3]
            },
            move_hor: -1.50,
            spd: 40,
            dash: 1,
            level: 3,
            talent: {
                s17: 0,
                s16: 0
            },
            int_power: 70,
            ignint_bonus: 0,
            recoil: 15,
            ignint_subs: 100
        },
        derv: {
            eff_level: 10,
            dmg_type: 0,
            adj: {},
            multiplier: "atk",
            cd_type: 0,
            target: 3,
            type: 1,
            sk_type: 2,
            dmg: 31,
            target_cnt: 1,
            upgraded: 0,
            index: {
                dmg: [1],
                cc_eff: [2]
            },
            spd: 40,
            cc_eff: 70,
            level: 3,
            talent: {
                s1: 0
            },
            int_power: 50,
            ignint_bonus: 20,
            recoil: 10,
            ignint_subs: 0
        }
    },
    num3: {
        ext: {
            dodge: {
                eff_level: 1,
                dmg_type: 2,
                unlock_lv: 1,
                adj: {
                    num1: 1
                },
                multiplier: "atk",
                cd_type: 0,
                target: 3,
                max_dur: 3,
                type: 1,
                sk_type: 7,
                target_cnt: 1,
                upgraded: 0,
                index: {
                    max_dur: [3],
                    debuff_dur: [2],
                    debuff_eff: [1]
                },
                debuff_dur: 5,
                debuff_eff: 60,
                level: 1,
                debuff: 1,
                auto_active: 0,
                talent: {},
                int_power: 70,
                ignint_bonus: 0,
                ignint_subs: 100
            },
            switch_in: {
                eff_level: 1,
                dmg_type: 1,
                unlock_lv: 10,
                adj: {},
                multiplier: "hp",
                cd_type: 1,
                target: 2,
                heal: 15,
                max_dur: 3,
                type: 3,
                sk_type: 7,
                target_cnt: 1,
                upgraded: 0,
                cd: 5,
                index: {
                    heal: [1],
                    max_dur: [2],
                    cd: [3]
                },
                level: 1,
                talent: {},
                int_power: 0,
                ignint_bonus: 0,
                ignint_subs: 100
            },
            switch_out: {
                eff_level: 1,
                dmg_type: 1,
                unlock_lv: 20,
                adj: {},
                multiplier: "hp",
                cd_type: 0,
                target: 2,
                interval: 0.50,
                max_dur: 5,
                type: 4,
                sk_type: 7,
                target_cnt: 1,
                upgraded: 0,
                index: {
                    regen: [1],
                    interval: [2],
                    max_dur: [3]
                },
                level: 1,
                talent: {},
                regen: 2,
                int_power: 0,
                ignint_bonus: 0,
                ignint_subs: 100
            }
        },
        data: {
            xp: 360,
            air_batk_set: 1,
            sprite_w: 396,
            core: 13,
            unlocked: 1,
            bearing: 15,
            num: 3,
            spmv_mod: 1,
            level: 21,
            crust: 15,
            name: "Medic",
            weapon: 1,
            batk_set: 2,
            ulti: 2,
            alt_batk_set: 1,
            spmv: 1,
            derv: 1
        },
        stats: {
            armor_dmg: 35,
            physical_do: 0,
            acid_do: 10,
            armor_burst: 50,
            armor_str: 100,
            dmg_res: 40,
            batk_db: 0,
            batk_do: 0,
            batk_power: 0,
            batk_eff: 0,
            derv_db: 0,
            derv_do: 0,
            aoe_dmg: 20,
            physical_db: 0,
            derv_power: 0,
            def_pen: 0,
            acid_db: 11,
            derv_eff: 0,
            ignore_int: 11,
            spmv_db: 0,
            armor_pen: 0,
            heal_otp: 103,
            cd_rdc: 5,
            spmv_do: 0,
            acid_rdc: 0,
            spmv_power: 0,
            physical_rdc: 0,
            crit_prt: 0,
            spmv_eff: 0,
            recoil_rdc: 0,
            agility: 126,
            debuff_res: 0,
            ulti_db: 0,
            dmg_output: 107,
            hp: 14102,
            atk: 962,
            ulti_do: 0,
            def: 1364,
            hp_flat: 5600,
            atk_spd: 100,
            ulti_power: 8,
            atk_flat: 550,
            ranged_do: 0,
            def_flat: 511,
            ulti_eff: 0,
            cc_power: 100,
            hp_scale: 100,
            cc_res: 16,
            atk_scale: 100,
            intg_db: 0,
            def_scale: 110,
            melee_do: 0,
            intg_do: 0,
            intg_power: 0,
            charge_spd: 100,
            crit_bld: 20,
            intg_eff: 0,
            crit_dmg: 50,
            buff_power: 100,
            accuracy: 0,
            ammo_cap: 0,
            mags_cap: 0,
            reload_spd: 0
        },
        batk: {
            num4: {
                chain_num: 0,
                eff_level: 7,
                dmg_type: 1,
                adj: {},
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                type: 8,
                sk_type: 1,
                dmg: 63,
                target_cnt: 1,
                upgraded: 0,
                cd: 0.95,
                index: {
                    buff_eff: [4],
                    dmg: [1],
                    buff_dur: [0],
                    max_chain: 3,
                    cd: [5]
                },
                buff_eff: 80,
                buff_dur: [0],
                buff: 23,
                level: 5,
                talent: {
                    m11: 0,
                    m10: 0,
                    s4: 0
                },
                int_power: 70,
                ignint_bonus: 20,
                ignint_subs: 0
            },
            num2: {
                chain_num: 0,
                eff_level: 7,
                dmg_type: 1,
                adj: {
                    num1: 1
                },
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                type: 3,
                sk_type: 1,
                dmg: 29,
                target_cnt: 1,
                upgraded: 0,
                cd: 0.19,
                index: {
                    buff_eff: [2],
                    dmg: [1],
                    buff_dur: [0],
                    cd: [3]
                },
                buff_eff: 40,
                move_hor: 0.75,
                buff_dur: [0],
                buff: 23,
                level: 3,
                talent: {
                    s1: 0
                },
                int_power: 40,
                ignint_bonus: 15,
                ignint_subs: 0
            },
            num3: {
                chain_num: 0,
                eff_level: 7,
                dmg_type: 1,
                adj: {},
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                type: 7,
                sk_type: 1,
                dmg: 23,
                target_cnt: 1,
                upgraded: 0,
                cd: 0.19,
                index: {
                    buff_eff: [5],
                    dmg: [1],
                    buff_dur: [0],
                    max_chain: 4,
                    cd: [6]
                },
                buff_eff: 60,
                buff_dur: [0],
                buff: 23,
                level: 5,
                talent: {
                    m11: 0,
                    m10: 0
                },
                int_power: 60,
                ignint_bonus: 15,
                ignint_subs: 0
            },
            num1: {
                chain_num: 0,
                eff_level: 7,
                dmg_type: 1,
                adj: {
                    num1: 1
                },
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                type: 3,
                sk_type: 1,
                dmg: 29,
                target_cnt: 1,
                upgraded: 0,
                cd: 0.19,
                index: {
                    buff_eff: [2],
                    dmg: [1],
                    buff_dur: [0],
                    cd: [3]
                },
                buff_eff: 40,
                move_hor: 0.75,
                buff_dur: [0],
                buff: 23,
                level: 3,
                talent: {
                    s1: 0
                },
                int_power: 40,
                ignint_bonus: 15,
                ignint_subs: 0
            }
        },
        air_batk: {},
        spmv_mod: -1,
        intg: {
            ext: 0,
            eff_level: 1,
            sk_type: 5,
            device_type: "Healing",
            dmg_type: 0,
            target_cnt: 1,
            upgraded: 0,
            unlock_lv: 1,
            cd: 15,
            index: {
                heal: [1],
                device_type: [3],
                cd: [4]
            },
            adj: {},
            level: 1,
            multiplier: "hp",
            cd_type: 1,
            target: 2,
            talent: {
                s13: 0,
                s19: 0,
                s6: 0
            },
            heal: 7,
            type: 1
        },
        module: {
            sub_care: {
                eff_level: 1,
                sk_type: 5,
                dmg_type: 0,
                target_cnt: 1,
                upgraded: 0,
                unlock_lv: 1,
                cd: 5,
                index: {
                    heal: [1],
                    cd: [3]
                },
                adj: {},
                level: 1,
                multiplier: "hp",
                cd_type: 1,
                target: 2,
                talent: {},
                heal: 5,
                type: 5
            }
        },
        uniq: {
            eff_level: 1,
            dmg_type: 0,
            adj: {
                num2: 11,
                num1: 1
            },
            multiplier: "hp",
            cd_type: 0,
            target: 3,
            max_point: 300,
            interval: 1,
            type: 1,
            sk_type: 6,
            target_cnt: 1,
            upgraded: 0,
            index: {
                regen: [2],
                interval: [3],
                buff_eff: [4, 5],
                buff_dur: [6, 6],
                max_point: [1]
            },
            buff_eff: [15, 15],
            buff_dur: [30, 30],
            buff: [18, 21],
            level: 1,
            auto_active: 0,
            talent: {
                s7: 0
            },
            regen: 2,
            threshold: 200
        },
        heal_potion: {},
        booster: {},
        ulti: {
            eff_level: 10,
            dmg_type: 2,
            radius: 1,
            adj: {
                num1: 3
            },
            multiplier: "atk",
            cd_type: 1,
            target: 3,
            interval: 0.50,
            type: 2,
            sk_type: 4,
            device_type: "Offensive",
            dmg: [74],
            target_cnt: 50,
            upgraded: 0,
            dmg_excess: 50,
            dur: 8.10,
            cd: 42.75,
            index: {
                device_type: [9],
                dmg: [1],
                target_cnt: [6],
                dmg_excess: [7],
                dur: [2],
                cd: [8],
                interval: [3],
                buff_eff: [4],
                buff_dur: [5]
            },
            buff_eff: 35,
            buff_dur: 16.20,
            spd: 30,
            buff: 21,
            level: 2,
            talent: {
                s14: 0,
                s15: 0
            },
            int_power: 50,
            ignint_bonus: 0,
            ignint_subs: 100
        },
        spmv: {
            eff_level: 10,
            dmg_type: 0,
            adj: {},
            multiplier: "",
            cd_type: 2,
            target: 1,
            type: 1,
            sk_type: 3,
            device_type: "Utility",
            target_cnt: 1,
            upgraded: 0,
            cd: 0.50,
            dur: [4, 4],
            index: {
                dur: [2, 2],
                device_type: [4],
                buff_eff: [1, 1],
                buff_dur: [2, 2],
                cd: [3]
            },
            buff_eff: [10, 10],
            buff_dur: [4, 4],
            buff: [302, 19],
            level: 1,
            talent: {
                s11: 0
            },
            int_power: 0,
            ignint_bonus: 0,
            ignint_subs: 70
        },
        derv: {
            eff_level: 10,
            dmg_type: 2,
            adj: {
                num1: 2
            },
            multiplier: "atk",
            cd_type: 1,
            target: 3,
            type: 1,
            stats_ovr: {
                accuracy: 70
            },
            max_stack: 2,
            device_type: "Offensive",
            sk_type: 2,
            dmg: 38,
            stats_ovr_type: [53],
            target_cnt: 1,
            upgraded: 1,
            cd: 3.80,
            index: {
                cd: [4],
                max_stack: [3],
                device_type: [5],
                dmg: [1],
                stats_ovr: [2]
            },
            level: 3,
            talent: {
                s8: 0,
                s17: 0
            },
            int_power: 40,
            ignint_bonus: 0,
            ignint_subs: 0
        }
    },
    num1: {
        ext: {
            dodge: {
                eff_level: 1,
                dmg_type: 1,
                unlock_lv: 1,
                adj: {
                    num1: 1
                },
                multiplier: "atk",
                cd_type: 0,
                target: 3,
                interval: 0.20,
                max_dur: 3,
                type: 1,
                sk_type: 7,
                dmg: 35,
                target_cnt: 5,
                upgraded: 0,
                dmg_excess: 50,
                index: {
                    dmg: [1],
                    target_cnt: [4],
                    dmg_excess: [5],
                    interval: [2],
                    max_dur: [6],
                    cc_eff: [3]
                },
                cc_eff: 46,
                cc: 103,
                level: 1,
                auto_active: 0,
                talent: {},
                int_power: 70,
                ignint_bonus: 0,
                ignint_subs: 100,
                cnt: 5
            },
            switch_in: {
                eff_level: 1,
                dmg_type: 1,
                unlock_lv: 10,
                adj: {},
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                max_dur: 3,
                type: 3,
                sk_type: 7,
                dmg: 325,
                target_cnt: 3,
                upgraded: 0,
                dmg_excess: 50,
                cd: 5,
                index: {
                    dmg: [1],
                    target_cnt: [3],
                    dmg_excess: [4],
                    cd: [6],
                    max_dur: [5],
                    cc_eff: [2]
                },
                cc_eff: 172,
                cc: 103,
                level: 1,
                talent: {},
                int_power: 70,
                ignint_bonus: 0,
                ignint_subs: 100
            },
            retaliate: {
                eff_level: 1,
                dmg_type: 3,
                unlock_lv: 1,
                adj: {
                    num1: 1
                },
                multiplier: "-hp_enemy",
                cd_type: 0,
                target: 3,
                max_dur: 3,
                type: 2,
                sk_type: 7,
                dmg: 10,
                target_cnt: 1,
                upgraded: 0,
                index: {
                    dmg: [1],
                    max_dur: [2]
                },
                cc: 101,
                level: 1,
                auto_active: 0,
                talent: {},
                int_power: 100,
                ignint_bonus: 0,
                ignint_subs: 100
            },
            switch_out: {
                eff_level: 1,
                dmg_type: 1,
                unlock_lv: 20,
                adj: {},
                multiplier: "atk",
                cd_type: 0,
                target: 3,
                type: 4,
                sk_type: 7,
                dmg: [120, 100],
                target_cnt: 1,
                upgraded: 0,
                index: {
                    dmg: [1, 2],
                    cc_eff: [3]
                },
                cc_eff: 115,
                cc: 104,
                level: 1,
                talent: {},
                int_power: 70,
                ignint_bonus: 0,
                ignint_subs: 100
            },
            joint: {
                eff_level: 1,
                dmg_type: 1,
                unlock_lv: 30,
                adj: {
                    num1: 1
                },
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                max_dur: 3,
                type: 5,
                sk_type: 7,
                dmg: [67, 448],
                target_cnt: 1,
                upgraded: 0,
                cd: 10.80,
                index: {
                    dmg: [1, 2],
                    max_dur: [4],
                    cc_eff: [3],
                    cd: [5]
                },
                cc_eff: 115,
                cc: 103,
                level: 1,
                auto_active: 0,
                talent: {},
                int_power: 70,
                ignint_bonus: 0,
                ignint_subs: 100,
                cnt: 2
            }
        },
        data: {
            xp: 2550,
            air_batk_set: 2,
            sprite_w: 475,
            core: 14,
            unlocked: 1,
            bearing: 14,
            num: 1,
            spmv_mod: 2,
            level: 40,
            crust: 14,
            name: "Warrior",
            weapon: 4,
            batk_set: 1,
            ulti: 1,
            alt_batk_set: 1,
            spmv: 2,
            derv: 2
        },
        stats: {
            armor_dmg: 0,
            physical_do: 0,
            acid_do: 0,
            armor_burst: 150,
            armor_str: 100,
            dmg_res: 40,
            batk_db: 0,
            batk_do: 0,
            batk_power: 0,
            batk_eff: 0,
            derv_db: 0,
            derv_do: 0,
            aoe_dmg: 20,
            physical_db: 0,
            derv_power: 0,
            def_pen: 5,
            acid_db: 0,
            derv_eff: 0,
            ignore_int: 15,
            spmv_db: 0,
            armor_pen: 14,
            heal_otp: 100,
            cd_rdc: 10,
            spmv_do: 12,
            acid_rdc: 0,
            spmv_power: 0,
            physical_rdc: 37,
            crit_prt: 0,
            spmv_eff: 0,
            recoil_rdc: 0,
            agility: 100,
            debuff_res: 0,
            ulti_db: 0,
            dmg_output: 100,
            hp: 32220,
            atk: 3306,
            ulti_do: 0,
            def: 3020,
            hp_flat: 5600,
            atk_spd: 100,
            ulti_power: 0,
            atk_flat: 1170,
            ranged_do: 6,
            def_flat: 610,
            ulti_eff: 0,
            cc_power: 115,
            hp_scale: 110,
            cc_res: 0,
            atk_scale: 113,
            intg_db: 0,
            def_scale: 100,
            melee_do: 17,
            intg_do: 0,
            intg_power: 0,
            charge_spd: 100,
            crit_bld: 17,
            intg_eff: 0,
            crit_dmg: 112,
            buff_power: 135,
            accuracy: 0,
            ammo_cap: 0,
            mags_cap: 0,
            reload_spd: 0
        },
        batk: {
            num4: {
                chain_num: 4,
                eff_level: 7,
                dmg_type: 1,
                adj: {},
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                type: 4,
                sk_type: 1,
                dmg: 77,
                target_cnt: 3,
                upgraded: 0,
                dmg_excess: 50,
                cd: 0.18,
                index: {
                    dmg: [4],
                    max_chain: 4,
                    target_cnt: [5],
                    dmg_excess: [6],
                    cd: [7]
                },
                level: 4,
                talent: {
                    s17: 0,
                    m9: 1
                },
                int_power: 40,
                ignint_bonus: 10,
                ignint_subs: 0,
                cnt: 2
            },
            num5: {
                chain_num: 2,
                eff_level: 7,
                dmg_type: 1,
                adj: {},
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                type: 10,
                sk_type: 1,
                dmg: 145,
                target_cnt: 1,
                upgraded: 0,
                cd: 0.45,
                index: {
                    dmg: [3],
                    max_chain: 4,
                    cc_eff: [5],
                    cd: [6]
                },
                cc_eff: 80,
                cc: 105,
                level: 5,
                talent: {
                    m11: 1
                },
                int_power: 65,
                ignint_bonus: 20,
                ignint_subs: 0
            },
            num6: {
                chain_num: 0,
                eff_level: 7,
                dmg_type: 1,
                adj: {
                    num1: 3
                },
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                type: 11,
                sk_type: 1,
                dmg: 236,
                target_cnt: 1,
                upgraded: 0,
                cd: 2.70,
                index: {
                    buff_eff: [2, 3],
                    dmg: [1],
                    buff_dur: [-1, 0],
                    cd: [4]
                },
                buff_eff: [40, 80],
                move_hor: 1.75,
                buff_dur: [0, 0],
                buff: [33, 23],
                level: 5,
                talent: {
                    s18: 0
                },
                int_power: 70,
                ignint_bonus: 30,
                ignint_subs: 0
            },
            num2: {
                chain_num: 2,
                eff_level: 7,
                dmg_type: 1,
                adj: {},
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                type: 1,
                sk_type: 1,
                dmg: 59,
                target_cnt: 3,
                upgraded: 0,
                dmg_excess: 50,
                cd: 0.09,
                index: {
                    dmg: [3],
                    max_chain: 7,
                    target_cnt: [8],
                    dmg_excess: [9],
                    cd: [10]
                },
                level: 4,
                talent: {
                    m8: 1,
                    s17: 0,
                    m7: 1
                },
                int_power: 40,
                ignint_bonus: 10,
                ignint_subs: 0
            },
            num3: {
                chain_num: 3,
                eff_level: 7,
                dmg_type: 1,
                adj: {},
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                type: 4,
                sk_type: 1,
                dmg: 77,
                target_cnt: 3,
                upgraded: 0,
                dmg_excess: 50,
                cd: 0.18,
                index: {
                    dmg: [4],
                    max_chain: 4,
                    target_cnt: [5],
                    dmg_excess: [6],
                    cd: [7]
                },
                level: 4,
                talent: {
                    s17: 0,
                    m9: 1
                },
                int_power: 40,
                ignint_bonus: 10,
                ignint_subs: 0,
                cnt: 2
            },
            num1: {
                chain_num: 1,
                eff_level: 7,
                dmg_type: 1,
                adj: {},
                multiplier: "atk",
                cd_type: 1,
                target: 3,
                type: 1,
                sk_type: 1,
                dmg: 54,
                target_cnt: 3,
                upgraded: 0,
                dmg_excess: 50,
                cd: 0.09,
                index: {
                    dmg: [2],
                    max_chain: 7,
                    target_cnt: [8],
                    dmg_excess: [9],
                    cd: [10]
                },
                level: 4,
                talent: {
                    m8: 1,
                    s17: 0,
                    m7: 1
                },
                int_power: 40,
                ignint_bonus: 10,
                ignint_subs: 0
            }
        },
        air_batk: {},
        spmv_mod: -1,
        intg: {
            eff_level: 1,
            dmg_type: 0,
            unlock_lv: 1,
            adj: {},
            multiplier: "",
            cd_type: 0,
            target: 0,
            type: 1,
            sk_type: 5,
            target_cnt: 1,
            upgraded: 0,
            index: {
                buff_eff: [2, 3],
                buff_dur: [0, -1],
                debuff_dur: [0],
                debuff_eff: [1]
            },
            buff_eff: [30, 20],
            buff_dur: [0, 0],
            debuff_dur: [0, 10],
            buff: [4, -3],
            debuff_eff: [30, 10],
            level: 1,
            debuff: [1, 10],
            talent: {
                s7: 1
            }
        },
        module: {
            grave: {
                eff_level: 1,
                sk_type: 5,
                dmg: 100,
                dmg_type: 0,
                target_cnt: 1,
                upgraded: 0,
                unlock_lv: 1,
                index: {
                    dmg: [2],
                    buildup: [1]
                },
                adj: {},
                buildup: 5,
                level: 1,
                multiplier: "",
                cd_type: 0,
                target: 3,
                talent: {},
                type: 2
            },
            superbody: {
                threshold: 40,
                eff_level: 1,
                sk_type: 5,
                dmg_type: 0,
                target_cnt: 1,
                upgraded: 0,
                unlock_lv: 1,
                index: {
                    armor: [1],
                    fade_dur: [3]
                },
                adj: {},
                level: 1,
                armor: 35,
                fade_dur: 15,
                multiplier: "-hp_lost",
                cd_type: 0,
                target: 3,
                talent: {
                    s5: 0
                },
                type: 3
            }
        },
        uniq: {
            eff_level: 1,
            dmg_type: 0,
            adj: {
                num1: 1
            },
            armor: 20,
            fade_dur: 10,
            multiplier: "hp",
            cd_type: 0,
            target: 3,
            max_point: 200,
            heal: 15,
            type: 1,
            sk_type: 6,
            target_cnt: 1,
            upgraded: 0,
            index: {
                armor: [3],
                fade_dur: [6],
                max_point: [1],
                heal: [4],
                buff_eff: [2],
                buff_dur: [5]
            },
            buff_eff: 520,
            buff_dur: 10,
            buff: 2,
            level: 1,
            auto_active: 0,
            talent: {
                s2: 0
            },
            threshold: 200
        },
        ulti: {
            eff_level: 10,
            dmg_type: 1,
            adj: {},
            multiplier: "atk",
            cd_type: 1,
            target: 3,
            type: 1,
            sk_type: 4,
            dmg: [182, 660],
            target_cnt: 5,
            upgraded: 0,
            dmg_excess: 54,
            cd: 27,
            index: {
                dmg: [1, 2],
                target_cnt: [5],
                dmg_excess: [6],
                cd: [7],
                cc_eff: [4],
                cnt: [3]
            },
            cc_eff: 172,
            cc: 103,
            level: 3,
            talent: {
                s13: 0
            },
            int_power: 80,
            ignint_bonus: 0,
            ignint_subs: 100,
            cnt: 4
        },
        spmv: {
            eff_level: 10,
            dmg_type: 1,
            adj: {
                num1: 4
            },
            multiplier: "atk",
            cd_type: 1,
            target: 3,
            dodge: 1,
            type: 2,
            max_stack: 2,
            sk_type: 3,
            dmg: 78,
            target_cnt: 2,
            upgraded: 0,
            dmg_excess: 20,
            cd: 4.50,
            index: {
                max_stack: [2],
                dmg: [1],
                target_cnt: [5],
                dmg_excess: [6],
                cd: [7],
                buff_eff: [3],
                buff_dur: [4]
            },
            buff_eff: 20,
            move_hor: 2.50,
            buff_dur: 4,
            buff: 12,
            level: 2,
            talent: {
                s8: 1
            },
            int_power: 40,
            ignint_bonus: 0,
            ignint_subs: 70,
            cnt: 2
        },
        derv: {
            eff_level: 10,
            dmg_type: 0,
            adj: {},
            multiplier: "",
            immune_debuff: 1,
            cd_type: 1,
            immune_cc: 1,
            target: 3,
            no_dmg: 1,
            type: 2,
            sk_type: 2,
            target_cnt: 1,
            upgraded: 0,
            cd: 3.60,
            index: {
                buff_eff: [1],
                buff_dur: [2],
                cd: [3]
            },
            buff_eff: 14,
            buff_dur: 5,
            buff: 12,
            level: 5,
            talent: {
                s9: 0
            },
            int_power: 100,
            ignint_bonus: 0,
            ignint_subs: 100
        }
    }
}

console.log(memorySizeOf(struct))*/
