Bots    
----
    1 - Bo<PERSON>            [1]
        1 - num
        2 - name
        3 - {unlocked}
        4 - {level}
        5 - {xp}
        6 - {weapon}
        7 - {core}
        8 - {bearing}
        9 - {crust}
        10 - sprite
        11 - sprite_w
        12 - {derv}
        13 - {spmv}
        14 - {spmv_mod}
        15 - {ulti}
        16 - {batk_set}
        17 - {alt_batk_set}
        18 - {air_batk_set}
        ---- [mc_desc]
        ---- [ts_desc]
        ---- [stat_info]

    2 - Stats
        1 - num
        2 - type
        3 - name_en
        4 - name_id
        5 - name_str

    3 - Trait
        1 - num
        2 - stat_num
        3 - init_val
        4 - add_val
        5 - sect

    4 - Weapon          [2]         
        1 - class_num
        2 - num
        3 - name
        4 - {unlocked}
        5 - rarity
        6 - {level}
        7 - {trait1_type}
        8 - {trait1_tier}
        9 - {trait2_type}
        10 - {trait2_tier}
        11 - {variant}
        12 - cost_num
        13 - cost
        14 - batk_limit

    5 - Equipment       [3]     
        1 - type
        2 - class_num
        3 - num
        4 - code
        5 - name
        6 - {unlocked}
        7 - rarity
        8 - {level}
        9 - {trait1_type}
        10 - {trait1_tier}
        11 - {trait2_type}
        12 - {trait2_tier}
        13 - cost_num
        14 - cost

    6 - Talents         [5]
        1 - num
        2 - name
        3 - {unlocked}
        4 - sprite
        5 - frame
        6 - type
        7 - title
        8 - desc
        9 - active_req
        10 - cost

    7 - Skill List      [6]
        1 - num
        2 - sprite
        3 - title
        4 - desc -> text-adv-dis-mdf
        5 - tag -> num-text
        6 - {unlocked}
        7 - {level}
        8 - unlock_lv           // -1 = unavailable, 0 = locked
        9 - talent -> {mn}-{...}-{sx}
        10 - atk_cost
        11 - eff_level
        12 - type
        13 - {upgraded}         // image_index tambahan kalo di-upgrade
        14 - adj_count
        15 - chain -> valid-val-[num]
        16 - sk_mod -> sect_a-sect_b
        17 - str_name

    8 - Skill Details   [7]
        1 - num
        2 - skill_num
        3 - dtl_type        // 1-features, 2-attributes
        4 - text
        5 - val -> init-low-mid-high-[total]
        6 - attr -> unlocked-affix-suffix-upgradeable
        7 - opt -> fixed-rounded-subs
            ---- dtl_type = 2, opt buat subs
        8 - val_mod -> inv-flat_num-scale_num-amp_num
            ---- num dari stats, inv = 100 - n, scale = init + lv_up


    --- Skill Up
        [-] item_num
        [-] item_cost

    --- Skill Adj
        [-] num1 ->
            [-] text
            [-] val
            [-] tooltip_text
            [-] bound -> adj_num - linear - letter
                         ---- linear = 0-contrary, 1-linear, 2-contrary_linear
                         ---- adj_num = <int>, [<int1>, ...], [[<con1>, ...], [<lin1>, ...]]
            [-] [adj_val]       // non-BATK
            [-] [real_val]      // nilai asli, non-BATK
        [-] num2
        [-] ...

    --- BATK Sequence
        [-] num1 -> seq_type - {set_num} - {type} - {adj} - unlock_lv - [chain_num]
            ---- seq_type = 1-normal, 2-alt, 3-airborne
            ---- {adj} -> {num1}-{num2}-{...}
        [-] num2
        [-] ...

    Note:
        {} = nilai ambil dari file terkait
        [] = tambahan ke data
        --[db] = nilai ambil dari database terkait
        x -> y-z = x adalah struct, bagian y dan z
        [-] = nama struct
        Skill List ada 20 baris lebih buat dummy (oBotsUI--(Create & Step), scDatabase--db_create_skill_dtl, oBotsSkillsSeqChangeUI--Step)
        Baris Skill Details >= Baris Skill List (harus)
        Edit db_create_lab() kalo tambah item sebelum EQ/WP



Main Menu
---------
    1 - Items           [4]
        1 - num
        2 - type
        3 - name_file
        4 - sprite
        5 - frame
        6 - grade
        7 - {count}
        8 - name_en
        9 - desc_en
        10 - name_id
        11 - desc_id
        12 - link_num
        13 - attribute -> cost-limit
            ---- cost -> num-val
            ---- limit -> type-val-{bought}
                ==== type = 1-daily, 2-weekly, 3-monthly, 4-non-recurring    
        14 - spr_item
        
        ---- data = ..., 8-name, 9-desc, 10-link_num, 11-attribute

    2 - Control
        1 - num
        2 - name
        3 - val

    3 - Setting         [8]
        1 - num
        2 - type
        3 - name
        4 - title
        5 - desc
        6 - val -> list-[group]-{selected}-def-min      // list = -1, [dropdown_item, ...]
        7 - change_type                                 // 1-dropdown, 2-checkbox, 3-slider, 4-input

    4 - Lab             [9]
        1 - num
        2 - type
        3 - item_num
        4 - class_num                   // crafting, transfer
        5 - list -> num-cnt             // {num: [], cnt: []} / {method1: {num: [], cnt: []}, ...}  (item_num)
        6 - out_val                     // int / {num: [], cnt: []}
    
    5 - Stage           [10]
        1 - num
        2 - type                                            // tab
        3 - name                                            // file section
        4 - title -> idn-en
        5 - desc -> idn-en
        6 - {unlocked} -> type-val-status                   // status = 0 / 1
        7 - bg
        8 - entry -> name-val

    6 - Stage Details   [11]
        1 - num
        2 - stage_num
        3 - group                                           // sub menu (stage) / menu (stage list)
        4 - type                                            // 1 = normal, 2 = elite, 3 = boss
        5 - name                                            // file key
        6 - title [-> idn-en]                               // DB Stage col 4 / dari type, global = struct
        7 - desc [-> idn-en]                                // DB Stage col 5 / dari type, global = struct
        8 - icon -> spr-frame                               // frame mulai dari 1
        9 - {lost}
        10 - {star}                                         // 0 = uncleared, 1-6 = cleared
        11 - {clear}                                        // -1 = locked, 0 = uncleared, 1-5 = cleared
        12 - diff                                           // override difficulty (default = 0)
        13 - reward -> first-perm                           // {num: [], cnt: []} / {num: [[], ...], cnt: [[], ...]} / {num: -1, cnt: -1} (item_num)
        14 - detail -> code-lvl-desc-
                       star1-star2-star3-tips
            ---- star = {type: int, desc: string, val: any}
        15 - bg -> spr-frame
        16 - bots -> slot                                   // slot = [0, 2] (0-any, 1..n-class_trial) [, clear = [class_num, ...] ]
        17 - tex                                            // array texture group
        18 - tg_room                                        // room target
        19 - parallax                                       // {layer_name: [string], spd_x: [float], spd_y: [float]}
        20 - view                                           // stage_view_*
        ---- [tab]
        ---- [menu]                                         // baris di global.db_stage


    --- Settings
        [1] General
            1 - Language = ID, EN                                 // 1-2
            2 - Damage Text = Hide, Small, Medium, Large          // 0-3
            3 - CC Text = Hide, Small, Medium, Large              // 0-3
            4 - Debuff Text = Hide, Small, Medium, Large          // 0-3
            5 - Effect Text = Hide, Small, Medium, Large          // 0-3
            6 - Auto Cloud = Inactive, Active                     // 0-1
            7 - Preload Bots Menu = Inactive, Active              // 0-1
        [2] Display
            8 - Window Mode = Windowed, Borderless, Fullscreen    // 0-2
            9 - Resolution = 640x360, 1280x720, 1366x768, 1600x900, 1920x1080, 2560x1440 , Fullscreen  // 1-7
            10 - Framerate = 30, 60 [, 90, 120]                   // 1-2 [4]
            // - UI Scaling = 70%/80% - 100%
        [3] Visual
            11 - Visual Effect = Low, Medium, High                 // 1-3
            12 - Particle = Low, Medium, High                      // 1-3
            13 - Shadow = Simple, Dynamic                          // 1-2
            14 - Lighting = Inactive, Active                       // 0-1
            15 - Bloom = Inactive, Active                          // 0-1
            16 - Anti-Aliasing = Inactive, 2x, 4x, 8x              // 0-3
        [4] Camera
            17 - Distance = Close, Medium, Far                     // 1-3
            18 - Tracking Speed = Sluggish, Slow, Medium, Fast, Swift, Instant        // 1-6
            19 - Shake Intensity = None, Weak, Medium, Strong      // 0-3
        [5] Sound
            20 - Sound Effect = 0% - 100%                          // 0-1
            21 - Ambience Sound = 0% - 100%                        // 0-1
            // - Voice Over = 0% - 100%                            // 0-1
        [6] Control
            22 - Move Right
            23 - Move Left
            24 - Move Up
            25 - Move Down
            26 - Jump
            27 - Dash / Sprint
            28 - Action
            29 - Toggle Weapon State
            30 - Basic Attacks
            31 - Derivative Skill
            32 - Special Moves
            33 - Ultimate Skill
            34 - Switch Bots 1
            35 - Switch Bots 2
            36 - Toggle Stage Objectives
            37 - Toggle UI
            38 - Zoom In
            39 - Zoom Out
        [7] Gameplay
            40 - Targeting Mode = Manual, Automatic                // 1-2
            41 - Target Preference = Direction, Cursor, Nearest    // 1-3
            42 - Side-scroller Jump = Inactive, Active             // 0-1

        ---- Ganti window_center() di oSettingsSaveUI (Step), oBtnW (Alarm 0), oIntro (Create), scFileData (settings_apply())
