function file_text_migrate(type) {
	switch (type) {
		case 1:			// Migrasi level Skill
			var bot_name = ["warrior", "archer", "medic"];
			for (var i = 0; i < array_length(bot_name); i++) {
				var sect_name = ["basic_atk", "special_mv", "ultimate"];
				var new_sect_name = ["sk_batk", "sk_spmv", "sk_ulti"];
				for (var j = 0; j < array_length(sect_name); j++) {
					var type_list = ["type1", "type2", "type3", "type4", "type5", "type6", "type7", "type8", "type9", "type10"];
					var new_type = [];
					switch (new_sect_name[j]) {
						case "sk_batk":
							switch (i) {
								case 0: 
									new_type = ["type1", "type2", "type3", "type4", "type6", "type7", "type8", "type5", "type9", "type10"];
									break;
								case 1: 
									new_type = ["type1", "type2", "type3", "type4", "type5", "type6", "type8", "type9"];
									break;
								case 2: 
									new_type = ["type1", "type2", "type3", "type4", "type5", "type6", "type7", "type8"];
									break;
							}
						break;
						case "sk_spmv":
							switch (i) {
								case 0: 
									new_type = ["type2", "type1", "type3"];
									break;
								case 1: 
									new_type = ["type1", "type2", ["sk_derv", "type1"]];
									break;
								case 2: 
									new_type = ["type1", ["sk_derv", "type1"], "type3", "type4"];
									break;
							}
						break;
						case "sk_ulti":
							switch (i) {
								case 0: 
									new_type = ["type1", "type2", "type3"];
									break;
								case 1: 
									new_type = ["type1", "type2", "type3"];
									break;
								case 2: 
									new_type = ["type1", "type2", "type3"];
									break;
							}
						break;
					}
					
					file_text_decrypt(bot_name[i] + ".txt");
					for (var k = 0; k < array_length(new_type); k++) {
						var lv = ini_read_real(sect_name[j], type_list[k] + "_level", 1);
						if (!is_array(new_type[k])) {
							ini_write_real(new_sect_name[j], new_type[k] + "_lv", lv);
						} else {
							ini_write_real(new_type[k][0], new_type[k][1] + "_lv", lv);
						}
						
					}
					file_text_encrypt(bot_name[i] + ".txt");
				}
				
				var sect_del = ["basic_atk", "special_mv", "ultimate", "talent"];
				file_text_decrypt(bot_name[i] + ".txt");
				for (var j = 0; j < array_length(sect_del); j++) {
					if (ini_section_exists(sect_del[j])) {
						ini_section_delete(sect_del[j]);
					}
				}
				file_text_encrypt(bot_name[i] + ".txt");
			}
			
			file_text_decrypt("general.txt");
			ini_write_real("main", "migration1", 1);
			file_text_encrypt("general.txt");
			break;
		case 2:			// Reset shop
			file_text_decrypt("general.txt");
			var last_day = ini_read_real("shop", "last_day", current_day);
			var last_month = ini_read_real("shop", "last_month", current_month);
			var last_year = ini_read_real("shop", "last_year", current_year);
			ini_section_delete("shop");
			ini_write_real("shop", "last_day", last_day);
			ini_write_real("shop", "last_month", last_month);
			ini_write_real("shop", "last_year", last_year);
			ini_write_real("main", "migration" + string(type), 1);
			file_text_encrypt("general.txt");
			break;
		case 3:			// Rename save files
			var file_name = file_find_first("*.txt", fa_none);
			
			while (file_name != "") {
				file_rename(file_name, string_copy(file_name, 1, string_length(file_name)-4) + ".osq");
				file_name = file_find_next();
			}
			file_find_close();

			/*file_text_decrypt("general.osq");
			ini_write_real("main", "migration" + string(type), 1);
			file_text_encrypt("general.osq");*/
			break;
		
		/*case 4:			// Konversi save file Steam
			if (global.steam_api) {
				var files_str = steam_file_get_list();
				for (var i = 0; i < array_length(files_str); i++) {
					if (files_str[i].file_name != "steam_appid.txt") {
						var data = steam_file_read(files_str[i].file_name);
						data = base64_decode(data);
						data = base64_decode(data);
						steam_file_write(string_copy(files_str[i].file_name, 1, string_length(files_str[i].file_name)-4) + ".osq", data, string_length(data));
						steam_file_delete(files_str[i].file_name);
					}
				}
				
				file_text_decrypt("general.osq");
				ini_write_real("main", "migration" + string(type), 1);
				file_text_encrypt("general.osq");
			}
		break;
		case 5:			// Pindah save file lama dari Steam
			if (global.steam_api) {
				var files_str = steam_file_get_list();
				for (var i = 0; i < array_length(files_str); i++) {
					if (files_str[i].file_name != "steam_appid.txt") {
						var path = "Legacy/" + files_str[i].file_name;
						var file = file_text_open_write(path);
						
						var data = steam_file_read(files_str[i].file_name);
						data = base64_encode(data);
						data = base64_encode(data);
						file_text_write_string(file, data);
						file_text_close(file);
						steam_file_delete(files_str[i].file_name);
					}
				}
				
				file_text_decrypt("general.osq");
				ini_write_real("main", "migration" + string(type), 1);
				file_text_encrypt("general.osq");
			}
		break;*/
	}
}

function settings_apply(to_apply = [8, 9, 10, 20, 21]) {
	for (var i = 0; i < array_length(to_apply); i++) {
		var selected = db_get_value(global.db_setting, 6, to_apply[i]).selected;
		switch (to_apply[i]) {
			case 8:				// Window Mode
				window_set_fullscreen((selected == 2));
				window_set_showborder((selected == 0));
				break;
			case 9:				// Resolution
				if (db_get_value(global.db_setting, 6, 7).selected < 2) {
					switch (selected) {
						case 1: window_set_size(640, 360); break;
						case 2: window_set_size(1280, 720); break;
						case 3: window_set_size(1366, 768); break;
						case 4: window_set_size(1600, 900); break;
						case 5: window_set_size(1920, 1080); break;
						case 6: window_set_size(2560, 1440); break;
						case 7: 
							var disp_hmin = 0;
							var disp_yoffset = 0;
							if (db_get_value(global.db_setting, 6, 7).selected == 0) {
								switch (os_type) {
									case os_windows:
										disp_hmin = (display_get_height() > 768 && display_get_height() < 1080) ? 38 : ((display_get_height() > 1080) ? 46 : 30);
										disp_yoffset = disp_hmin;
										disp_hmin += (display_get_height() > 768 && display_get_height() < 1080) ? 50 : ((display_get_height() > 1080) ? 60 : 40);
									break;
								}
							}
							
							var display_mul = floor((display_get_height()-disp_hmin) / 9);
							window_set_rectangle(0, disp_yoffset, display_get_width(), 9 * display_mul);
						break;
					}
				}
				break;
			case 10:			// Framerate
				game_set_speed(30 * selected, gamespeed_fps);
				break;
			case 20:			// SFX
				audio_group_set_gain(audiogroup_sfx, selected, 0);
				break;
			case 21:			// Ambience
				audio_group_set_gain(audiogroup_amb, selected, 0);
				break;
		}
	}
}
