function db_update(db, col_num, row_num, val, file = []) {
	// col_num & row_num mulai dari 1
	// file = [sect, name] / [file, sect, name]
	ds_grid_set(db, col_num - 1, row_num - 1, val);
	switch (array_length(file)) {
	    case 2:
	        ini_write_real(file[0], file[1], val);
			break;
	    case 3:
	        file_text_decrypt(file[0]);
	        ini_write_real(file[1], file[2], val);
	        file_text_encrypt(file[0]);
			break;
	    case 4:
	        file_text_decrypt(file[0]);
	        ini_write_real(file[1], file[2], file[3]);
	        file_text_encrypt(file[0]);
			break;
	}
}

function db_update_rel(db, col_num, row_num, val, file = []) {
	// col_num & row_num mulai dari 1
	// file = [sect, name] / [file, sect, name]
	var prev_val = db_get_value(db, col_num, row_num);
	ds_grid_set(db, col_num - 1, row_num - 1, prev_val + val);
	switch (array_length(file)) {
	    case 2:
	        ini_write_real(file[0], file[1], prev_val + val);
			break;
	    case 3:
	        file_text_decrypt(file[0]);
	        ini_write_real(file[1], file[2], prev_val + val);
	        file_text_encrypt(file[0]);
			break;
	    case 4:
	        file_text_decrypt(file[0]);
	        ini_write_real(file[1], file[2], prev_val + file[3]);
	        file_text_encrypt(file[0]);
			break;
	}
}

function db_refresh(db, type, db_bots = "", class = 0, num = 0, num2 = 0) {
	// type = 1-db_bots, 2-db_weapon, 3-db_equipment, 4-g.db_items, 5-db_talents, 6-db_skill_list, 
	//        7-db_skill_dtl, 8-settings, 9-lab, 11-db_stagelist
	// class & num mulai dari 1
	var db_new = "";
	if (ds_exists(db, ds_type_grid)) {
		ds_grid_destroy(db);
	}
	
	switch (type) {
		case 1:
			db_new = db_create_bots();
			break;
		case 2:
			db_new = db_create_weapon(db_bots, class);
			break;
		case 3:
			db_new = db_create_equipment(db_bots, class, num);
			break;
		case 4:
			db_new = db_create_items();
			break;
		case 5:
			db_new = db_create_talent(db_bots, class);
			break;
		case 6:
			db_new = db_create_skill_list(db_bots, class, num);
			break;
		case 7:
			db_new = db_create_skill_dtl(db_bots, class, num, num2);
			break;
		case 8:
			db_new = db_create_settings();
			break;
		case 9:
			// db_bots = lab_type
			db_new = db_create_lab(db_bots, num, class);
			break;
		case 11:
			// db_bots = global.db_stage
			// num = tab
			// num2 = menu
			db_new = db_create_stage_list(global.db_stage, num, num2);
			break;
	}
	return db_new;
}

function db_update_batk_seq(db_bots, class_num, batkseq_type, set, seq, max_seq, type) {
	// class_num dimulai dari 1
	// batkseq_type = 1-normal, 2-alt, 3-airborne
	// name = "set{x}_num{y}", x = set_num, y = seq_num
	var seq_type_name = ["seq_norm", "seq_alt", "seq_air"];
	var bots_data = db_get_row(db_bots, 1, class_num);
	
	file_text_decrypt(string_lower(bots_data.name) + ".txt");
	ini_write_real(seq_type_name[batkseq_type-1], string("set{0}_num{1}", string(set), string(seq)), type);
	if (type == 0) {
		for (var i = seq+1; i <= max_seq; i++) {
			ini_write_real(seq_type_name[batkseq_type-1], string("set{0}_num{1}", string(set), string(i)), 0);
		}
	}
	file_text_encrypt(string_lower(bots_data.name) + ".txt");
	
	delete bots_data;
}

function db_update_batk_set(db_bots, class_num, batkseq_type, set) {
	// class_num dimulai dari 1
	// batkseq_type = 1-normal, 2-alt, 3-airborne
	// name = "set{x}_num{y}", x = set_num, y = seq_num
	var seq_title = ["", switch_lang("Rangkaian Serangan", "Attack Sequence"), switch_lang("Rangkaian Serangan Alternatif", "Alternative Attack Sequence"), switch_lang("Rangkaian Serangan Udara", "Airborne Attack Sequence")];
	var seq_type_file = ["batk_set", "alt_batk_set", "air_batk_set"];
	var bots_data = db_get_row(db_bots, 1, class_num);
	
	db_update(db_bots, 15 + batkseq_type, class_num, set, [string_lower(bots_data.name) + ".txt", string_lower(bots_data.name), seq_type_file[batkseq_type-1]]);
	var notif_text = switch_lang(string("{0} milik {1} telah diatur ke preset {2}", seq_title[batkseq_type], bots_data.name, string(set)), 
								 string("{0}'s {1} has been set to preset {2}", bots_data.name, seq_title[batkseq_type], string(set)));
	create_notification_small(notif_text, -1, 3);
	
	delete bots_data;
}

function db_update_skill_adj(db_bots, class_num, type, skill_num, adj_num, adj_val, batkseq_type = -1, set = -1, seq = -1) {
	// class_num dimulai dari 1
	// type = 1-batk, 2-derv, 3-spmv, 4-ulti, 5-intg, 6-uniq, 7-ext
	// batkseq_type = 1-normal, 2-alt, 3-airborne
	// name = "set{x}_num{y}_adj{z}", x = set_num, y = seq_num, z = adj_num
	var sect_name = ["", "sk_batk", "sk_derv", "sk_spmv", "sk_ulti", "sk_intg", "sk_uniq", "sk_ext"];
	var seq_type_name = ["seq_norm", "seq_alt", "seq_air"];
	var bots_data = db_get_row(db_bots, 1, class_num);
	
	file_text_decrypt(string_lower(bots_data.name) + ".txt");
	switch (type) {
		case 1:		// BATK
			ini_write_real(seq_type_name[batkseq_type-1], "set" + string(set) + "_num" + string(seq) + "_adj" + string(adj_num), adj_val);
		break;
		default:
			ini_write_real(sect_name[type], "type" + string(skill_num) + "_adj" + string(adj_num), adj_val);
	}
	file_text_encrypt(string_lower(bots_data.name) + ".txt");
	
	delete bots_data;
}

function db_update_spmv_mod(db_bots, class_num, spmv_mod) {
	var bots_data = db_get_row(db_bots, 1, class_num);
	ds_grid_set(db_bots, 13, class_num-1, spmv_mod);
	file_text_decrypt(string_lower(bots_data.name) + ".txt");
	ini_write_real(string_lower(bots_data.name), "spmv_mod", spmv_mod);
	file_text_encrypt(string_lower(bots_data.name) + ".txt");
	delete bots_data;
}

function db_refresh_items(item_type, refresh_attr = false) {
	// type = 1-unlockable, 2-consumable, 3-materials, 4-growth, 5-core, 6-bearing, 7-crust, 8-sword, 9-bow, 10-knife
	var type2_cnt = 5;
	var type3_cnt = 41;
	var type4_cnt = 9;
	var all_type = [type2_cnt, type3_cnt, type4_cnt];
	
	var start_num = 0;
	for (var i = 0; i < item_type-2; i++) {
		start_num += all_type[i];
	}
	
	file_text_decrypt("general.txt");
	for (var i = start_num; i < all_type[item_type-2] + start_num; i++) {
		if (db_get_value(global.db_items, 3, i+1) == "daily_point") {
			ds_grid_set(global.db_items, 6, i, ini_read_real("task", db_get_value(global.db_items, 3, i+1), 0));
		} else if (db_get_value(global.db_items, 3, i+1) == "total_point") {
			ds_grid_set(global.db_items, 6, i, ini_read_real("achievement", db_get_value(global.db_items, 3, i+1), 0));
		} else {
			ds_grid_set(global.db_items, 6, i, ini_read_real("main", db_get_value(global.db_items, 3, i+1), 0));
		}
		
		if (refresh_attr) {
			var item_attr = db_get_value(global.db_items, 13, i+1);
			if (is_struct(item_attr)) {
				item_attr.limit.bought = ini_read_real("shop", "item"+string(i+1), 0);
				ds_grid_set(global.db_items, 12, i, item_attr);
			}
		}
	}
	file_text_encrypt("general.txt");
}

