function db_create_bots(){
	var name = ["Warrior", "Archer", "Medic"];
	var spr = [[sMMClassWarrior1, sMMClassWarrior2], [sMMClassArcher1, sMMClassArcher2], [sMMClassMedic1, sMMClassMedic2]];
	var spr_w = [475, 436, 396];
	
	var db = ds_grid_create(18, array_length(name));
	for (var i = 0; i < array_length(name); i++) {
		file_text_decrypt(string_lower(name[i]) + ".txt"); 
		var unlocked = ini_read_real(string_lower(name[i]), "unlocked", 0);
		var level = ini_read_real(string_lower(name[i]), "level", 1);
		var xp = ini_read_real(string_lower(name[i]), "xp", 0);
		var weapon = ini_read_real(string_lower(name[i]), "weapon", 1);
		var core = ini_read_real(string_lower(name[i]), "core", 1);
		var bearing = ini_read_real(string_lower(name[i]), "bearing", 1);
		var crust = ini_read_real(string_lower(name[i]), "crust", 1);
		var derv = ini_read_real(string_lower(name[i]), "derv", 1);
		var spmv = ini_read_real(string_lower(name[i]), "spmv", 1);
		var spmv_mod = ini_read_real(string_lower(name[i]), "spmv_mod", 1);
		var ulti = ini_read_real(string_lower(name[i]), "ulti", 1);
		var batk_set = ini_read_real(string_lower(name[i]), "batk_set", 1);
		var alt_batk_set = ini_read_real(string_lower(name[i]), "alt_batk_set", 1);
		var air_batk_set = ini_read_real(string_lower(name[i]), "air_batk_set", 1);
		file_text_encrypt(string_lower(name[i]) + ".txt");
		
		var val = [i+1, name[i], unlocked, level, xp, weapon, core, bearing, crust, spr[i], spr_w[i], derv, spmv, spmv_mod, ulti, batk_set, alt_batk_set, air_batk_set];
		for (var j = 0; j < array_length(val); j++) {
			ds_grid_set(db, j, i, val[j]);
		}
	}
	return db;
}

function db_create_weapon(bots_db, class_num) {
	// class_num mulai dari 1
	var data = db_get_row(bots_db, 1, class_num);
	var wp_name = [["Old Sword", "Damascus Sword", "Spring Sword", "Carbon-steel: I", "Sky Blue", "High CS Sword"],
				   ["Old Bow", "Longbow", "Recurve Bow", "Compound Bow", "Violet", "High CO Bow"],
				   ["Old Knife", "Damascus Knife", "D2 Steel Knife", "Carbon-steel: II", "Viridian", "High CS Knife"]];
	var wp_rarity = [[1, 2, 3, 4, 4, 5], 
					 [1, 2, 3, 4, 4, 5], 
					 [1, 2, 3, 4, 4, 5]];
	var wp_cost_num = [[48, 48, 48, 48, 48, 49], 
					   [48, 48, 48, 48, 48, 49], 
					   [48, 48, 48, 48, 48, 49]];
	var wp_cost = [[0, 10000, 25000, 100000, 100000, 300], 
				   [0, 10000, 25000, 100000, 100000, 300], 
				   [0, 10000, 25000, 100000, 100000, 300]];
	var wp_batk_limit = [[[7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], 
						  [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], 
						  [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], 
						  [7, 10, 7, 7, 7, 7, 7, 7, 7, 7, 7], 
						  [7, 7, 7, 7, 7, 7, 7, 10, 7, 7, 7], 
						  [8, 10, 8, 10, 8, 8, 8, 8, 8, 8, 8]], 
						 [[7, 7, 7, 7, 7, 7, 7, 7, 7], 
						  [7, 7, 7, 7, 7, 7, 7, 7, 7], 
						  [7, 7, 7, 7, 7, 7, 7, 7, 7], 
						  [7, 10, 7, 7, 7, 7, 7, 7, 7], 
						  [7, 7, 7, 7, 7, 10, 7, 7, 7], 
						  [8, 10, 8, 10, 8, 8, 8, 8, 8]], 
						 [[7, 7, 7, 7, 7, 7, 7, 7], 
						  [7, 7, 7, 7, 7, 7, 7, 7], 
						  [7, 7, 7, 7, 7, 7, 7, 7], 
						  [7, 10, 7, 7, 7, 7, 7, 7], 
						  [7, 7, 7, 10, 7, 7, 7, 7], 
						  [8, 10, 8, 8, 8, 10, 8, 8]]];
	
	var db = ds_grid_create(14, array_length(wp_name[class_num - 1]));
	file_text_decrypt(string_lower(data.name) + ".txt"); 
	for (var i = 0; i < array_length(wp_name[class_num - 1]); i++) {
		var unlocked = ini_read_real("weapon" + string(i+1), "unlocked", 0);
		var level = ini_read_real("weapon" + string(i+1), "level", 1);
		var trait1_type = ini_read_real("weapon" + string(i+1), "trait1_type", 0);
		var trait1_tier = ini_read_real("weapon" + string(i+1), "trait1_tier", 1);
		var trait2_type = ini_read_real("weapon" + string(i+1), "trait2_type", 0);
		var trait2_tier = ini_read_real("weapon" + string(i+1), "trait2_tier", 1);
		var variant = ini_read_real("weapon" + string(i+1), "variant", 1);
		
		if (i == 0) {
			unlocked = 1;
		}
		var arr = [class_num, i+1, wp_name[class_num - 1][i], unlocked, wp_rarity[class_num - 1][i], level, trait1_type, trait1_tier, trait2_type, trait2_tier, variant, wp_cost_num[class_num - 1][i], wp_cost[class_num - 1][i], wp_batk_limit[class_num - 1][i]];
		for (var j = 0; j < array_length(arr); j++) {
			ds_grid_set(db, j, i, arr[j]);
		}
	}
	file_text_encrypt(string_lower(data.name) + ".txt");
	
	return db;
}

function db_create_equipment(bots_db, class_num, type) {
	// class_num mulai dari 1
	// type = 1-core, 2-bearing, 3-crust
	var data = db_get_row(bots_db, 1, class_num);
	var eq_rarity = [1, 2, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5];
	var eq_code = ["A", "A", "A", "B", "C", "D", "E", "F", "A", "B", "C", "D", "A", "B", "C", "D"];
	var all_eq_name = [["Simple Core", "Mediocre Core", "Advanced Core A", "Advanced Core B", "Advanced Core C", "Advanced Core D", "Advanced Core E", "Advanced Core F", "Expert Core A", "Expert Core B", "Expert Core C", "Expert Core D", "Elite Core A", "Elite Core B", "Elite Core C", "Elite Core D"],
					   ["Simple Bearing", "Mediocre Bearing", "Advanced Bearing A", "Advanced Bearing B", "Advanced Bearing C", "Advanced Bearing D", "Advanced Bearing E", "Advanced Bearing F", "Expert Bearing A", "Expert Bearing B", "Expert Bearing C", "Expert Bearing D", "Elite Bearing A", "Elite Bearing B", "Elite Bearing C", "Elite Bearing D"],
					   ["Simple Crust", "Mediocre Crust", "Advanced Crust A", "Advanced Crust B", "Advanced Crust C", "Advanced Crust D", "Advanced Crust E", "Advanced Crust F", "Expert Crust A", "Expert Crust B", "Expert Crust C", "Expert Crust D", "Elite Crust A", "Elite Crust B", "Elite Crust C", "Elite Crust D"]];
	var eq_cost_num = [48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 49, 49, 49, 49];
	var eq_cost = [0, 6000, 15000, 15000, 15000, 15000, 15000, 15000, 50000, 50000, 50000, 50000, 150, 150, 150, 150];
	
	var eq_name = "";
	switch (type) {
		case 1: eq_name = "core"; break;
		case 2: eq_name = "bearing"; break;
		case 3: eq_name = "crust"; break;
	}
	var db = ds_grid_create(14, array_length(eq_rarity));
	file_text_decrypt(string_lower(data.name) + ".txt"); 
	for (var i = 0; i < array_length(eq_rarity); i++) {
		var unlocked = ini_read_real(eq_name + string(i+1), "unlocked", 0);
		var level = ini_read_real(eq_name + string(i+1), "level", 1);
		var trait1_type = ini_read_real(eq_name + string(i+1), "trait1_type", 0);
		var trait1_tier = ini_read_real(eq_name + string(i+1), "trait1_tier", 1);
		var trait2_type = ini_read_real(eq_name + string(i+1), "trait2_type", 0);
		var trait2_tier = ini_read_real(eq_name + string(i+1), "trait2_tier", 1);
		
		if (i == 0) {
			unlocked = 1;
		}
		var arr = [type, class_num, i+1, eq_code[i], all_eq_name[type-1][i], unlocked, eq_rarity[i], level, trait1_type, trait1_tier, trait2_type, trait2_tier, eq_cost_num[i], eq_cost[i]];
		for (var j = 0; j < array_length(arr); j++) {
			ds_grid_set(db, j, i, arr[j]);
		}
	}
	file_text_encrypt(string_lower(data.name) + ".txt");
	
	return db;
}

function db_create_stats() {
	var name_en = [
		"Health Points", "Attack", "Defense", "Agility", "Damage Output", "Ignore Interruption", "Cooldown Reduction", "Critical Buildup", "Critical Damage", "Critical Protection", 
		"Healing Output", "Attack Speed", "Melee Damage Output", "Ranged Damage Output", "AoE Damage Scale", "Physical Damage Bonus", "Physical Damage Output", "Physical Damage Reduction", "Acid Damage Bonus", "Acid Damage Output",
		"Acid Damage Reduction", "Defense Penetration", "Armor Penetration", "Armor Damage", "Armor Burst", "Armor Strength", "Damage Resistance", "Crowd-Control Power", "Crowd-Control Resistance", "Buff Power",
		"Debuff Resistance", "Charge Speed", "BATK Damage Bonus", "BATK Damage Output", "BATK Power", "BATK Effectiveness", "DERV Damage Bonus", "DERV Damage Output", "DERV Power", "DERV Effectiveness",
		"SPMV Damage Bonus", "SPMV Damage Output", "SPMV Power", "SPMV Effectiveness", "ULTI Damage Bonus", "ULTI Damage Output", "ULTI Power", "ULTI Effectiveness", "INTG Damage Bonus", "INTG Damage Output",
		"INTG Power", "INTG Effectiveness", "Accuracy", "Recoil Reduction", "Ammo Capacity", "Magazine Capacity", "Reload Speed"
	];
	var name_id = [
		"Poin Kesehatan", "Serangan", "Pertahanan", "Kelincahan", "Output Kerusakan", "Abaikan Interupsi", "Pengurangan Cooldown", "Pembangunan Kritis", "Kerusakan Kritis", "Perlindungan Kritis", 
		"Output Penyembuhan", "Kecepatan Serangan", "Output Kerusakan Melee", "Output Kerusakan Ranged", "Skala Kerusakan AoE", "Bonus Kerusakan Fisik", "Output Kerusakan Fisik", "Pengurangan Kerusakan Fisik", "Bonus Kerusakan Asam", "Output Kerusakan Asam",
		"Lapisan Teflon", "Penembusan Pertahanan", "Penembusan Armor", "Kerusakan Armor", "Kehancuran Armor", "Kekuatan Armor", "Kekebalan Kerusakan", "Kekuatan Crowd-Control", "Kekebalan Crowd-Control", "Kekuatan Buff",
		"Kekebalan Debuff", "Kecepatan Charge", "Bonus Kerusakan BATK", "Output Kerusakan BATK", "Kekuatan BATK", "Efektivitas BATK", "Bonus Kerusakan DERV", "Output Kerusakan DERV", "Kekuatan DERV", "Efektivitas DERV",
		"Bonus Kerusakan SPMV", "Output Kerusakan SPMV", "Kekuatan SPMV", "Efektivitas SPMV", "Bonus Kerusakan ULTI", "Output Kerusakan ULTI", "Kekuatan ULTI", "Efektivitas ULTI", "Bonus Kerusakan INTG", "Output Kerusakan INTG",
		"Kekuatan INTG", "Efektivitas INTG", "Akurasi", "Pengurangan Hentakan", "Kapasitas Amunisi", "Kapasitas Magasin", "Kecepatan Isi Ulang"
	];
	var name_str = [
		"hp", "atk", "def", "agility", "dmg_output", "ignore_interruption", "cd_reduction", "crit_buildup", "crit_damage", "crit_protection",
		"healing_output", "atk_spd", "melee_do", "ranged_do", "aoe_dmg_scale", "physical_dmg_bonus", "physical_do", "dmg_reduction", "acidity_bonus", "acid_do",
		"tc", "def_penetration", "ap", "ab", "armor_burst", "armor_str", "dmg_res", "cc_power", "cc_res", "buff_power",
		"debuff_res", "charge_spd", "batk_db", "batk_do", "batk_power", "batk_eff", "derv_db", "derv_do", "derv_power", "derv_eff",
		"spmv_db", "spmv_do", "spmv_power", "spmv_eff", "ulti_db", "ulti_do", "ulti_power", "ulti_eff", "intg_db", "intg_do",
		"intg_power", "intg_eff", "accuracy", "recoil_reduction", "ammo_cap", "mags_cap", "reload_spd"
	];

	var db = ds_grid_create(5, array_length(name_en));
	for (var i = 0; i < array_length(name_en); i++) {
		var arr = [i+1, 1 + (i >= 3), name_en[i], name_id[i], name_str[i]];
		
		for (var j = 0; j < array_length(arr); j++) {
			ds_grid_set(db, j, i, arr[j]);
		}
	}
	return db;
}

function db_create_trait() {
	var wp_trait_num = [33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 
						43, 44, 45, 46, 47, 48, 49, 50, 51, 52];
	var wp_trait_init = [10, 5, 5, 4, 10, 5, 5, 4, 15, 5, 
						 10, 4, 20, 5, 15, 4, 20, 5, 15, 4];		
	var wp_trait_add = [20, 10, 15, 16, 20, 10, 15, 16, 25, 15, 
						20, 16, 30, 20, 15, 16, 30, 20, 15, 16];
	var cr_trait_num = [2, 6, 8, 11, 12, 22, 23, 28, 30];
	var cr_trait_init = [5, 5, 3, 5, 3, 2, 5, 10, 10];
	var cr_trait_add = [15, 15, 12, 10, 12, 8, 15, 25, 15];
	var br_trait_num = [1, 5, 9, 18, 21, 25, 27, 29, 31];
	var br_trait_init = [5, 3, 10, 5, 5, 15, 5, 10, 10];
	var br_trait_add = [15, 12, 20, 10, 10, 35, 10, 25, 25];
	var cs_trait_num = [3, 4, 7, 10, 13, 14, 15, 24, 32];
	var cs_trait_init = [5, 5, 2, 5, 3, 3, 10, 10, 5];
	var cs_trait_add = [15, 15, 8, 15, 12, 12, 20, 25, 15];
	var all_trait_num = array_concat(wp_trait_num, cr_trait_num, br_trait_num, cs_trait_num);
	var all_trait_init = array_concat(wp_trait_init, cr_trait_init, br_trait_init, cs_trait_init);
	var all_trait_add = array_concat(wp_trait_add, cr_trait_add, br_trait_add, cs_trait_add);
	
	var wp_trait_cnt = array_length(wp_trait_num);
	var cr_trait_cnt = array_length(cr_trait_num);
	var br_trait_cnt = array_length(br_trait_num);
	var cs_trait_cnt = array_length(cs_trait_num);
	var trait_cnt_list = [wp_trait_cnt, cr_trait_cnt, br_trait_cnt, cs_trait_cnt];
	var trait_sect = ["wp", "cr", "br", "cs"];
	var current_sect = 0;
	var threshold = trait_cnt_list[current_sect];
	var db = ds_grid_create(5, array_length(all_trait_num));
	for (var i = 0; i < array_length(all_trait_num); i++) {
		if (i >= threshold) {
	        current_sect++; 
	        if (current_sect < array_length(trait_sect)) {
	            threshold += trait_cnt_list[current_sect];
	        }
	    }
		var arr = [i+1, all_trait_num[i], all_trait_init[i], all_trait_add[i], trait_sect[current_sect]];
		for (var j = 0; j < array_length(arr); j++) {
			ds_grid_set(db, j, i, arr[j]);
		}
	}
	return db;
}

function db_create_items() {
	// type = 1-unlockable, 2-consumable, 3-materials, 4-growth, 5-core, 6-bearing, 7-crust, 8-sword, 9-bow, 10-knife
	#region		// Data
	// ganti juga db_refresh_items()
	var type2_cnt = 5;
	var type3_cnt = 41;
	var type4_cnt = 9;
	var type5_cnt = 16;
	var type6_cnt = 16;
	var type7_cnt = 16;
	var type8_cnt = 6;
	var type9_cnt = 6;
	var type10_cnt = 6;
	var all_type = [type2_cnt, type3_cnt, type4_cnt, type5_cnt, type6_cnt, type7_cnt, type8_cnt, type9_cnt, type10_cnt];
	var item_name = ["oil_box1", "oil_box2", "oil_box3", "oil_box4", "oil_box5", 
					 "colorless_oil", "red_oil", "lime_oil", "blue_oil", "olive_oil", "orange_oil", "yellow_oil", "chartreuse_oil", "purple_oil", "deeppink_oil", 
					 "magenta_oil", "electricindigo_oil", "teal_oil", "springgreen_oil", "aqua_oil", "dodgerblue_oil", "orangered_oil", "harlequin_oil", "torchred_oil", "hanpurple_oil", 
					 "fsgreen_oil", "navyblue_oil", "citrus_oil", "deepmagenta_oil", "reblue_oil", "contessa_oil", "fern_oil", "bluemarguerite_oil", "metal_fragment", "metal_piece", 
					 "metal_ingot", "metal_block", "a-rank_subs", "s-rank_subs", "ss-rank_subs", "core_mold", "bearing_mold", "crust_mold", "sword_mold", "bow_mold", 
					 "knife_mold",
					 "player_xp", "coin", "crystal", "daily_point", "total_point", "battery1", "battery2", "battery3", "battery4",
					 "core1", "core2", "core3", "core4", "core5", "core6", "core7", "core8", "core9", "core10", "core11", "core12", "core13", "core14", "core15", "core16",
					 "bearing1", "bearing2", "bearing3", "bearing4", "bearing5", "bearing6", "bearing7", "bearing8", "bearing9", "bearing10", "bearing11", "bearing12", "bearing13", "bearing14", "bearing15", "bearing16",
					 "crust1", "crust2", "crust3", "crust4", "crust5", "crust6", "crust7", "crust8", "crust9", "crust10", "crust11", "crust12", "crust13", "crust14", "crust15", "crust16",
					 "weapon1", "weapon2", "weapon3", "weapon4", "weapon5", "weapon6", 
					 "weapon1", "weapon2", "weapon3", "weapon4", "weapon5", "weapon6", 
					 "weapon1", "weapon2", "weapon3", "weapon4", "weapon5", "weapon6"
					 ];
	var item_spr = [sItemsSprConsumable, sItemsSprMaterials, sItemsSprGrowth, sItemsSprCore, sItemsSprBearing, sItemsSprCrust, sItemsSprSword, sItemsSprBow, sItemsSprKnife];
	var item_itemspr = [sItemsConsumable, sItemsMaterials, sItemsGrowth, sItemsCore, sItemsBearing, sItemsCrust, sItemsSword, sItemsBow, sItemsKnife];
	var item_type = [2, 2, 2, 2, 2, 
					 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
					 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
					 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
					 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
					 3, 
					 4, 4, 4, 4, 4, 4, 4, 4, 4, 
					 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5,
					 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 
					 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 
					 8, 8, 8, 8, 8, 8, 
					 9, 9, 9, 9, 9, 9, 
					 10, 10, 10, 10, 10, 10
					 ];
	var item_grade = [1, 2, 3, 4, 5, 
					  1, 2, 2, 2, 3, 3, 3, 3, 3, 3, 
					  3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 
					  4, 4, 4, 4, 4, 5, 5, 5, 2, 3, 
					  4, 5, 4, 5, 6, 4, 4, 4, 4, 4,
					  4,
					  1, 1, 5, 1, 1, 2, 3, 4, 5, 
					  1, 2, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 
					  1, 2, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 
					  1, 2, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 
					  1, 2, 3, 4, 4, 5, 
					  1, 2, 3, 4, 4, 5,
					  1, 2, 3, 4, 4, 5
					  ];
	var item_attr = [0, 0, 0, 0, 0,
					 {cost: {num: 48, val: 100}, limit: {type: 1, val: 100}}, {cost: {num: 48, val: 400}, limit: {type: 1, val: 50}}, {cost: {num: 48, val: 400}, limit: {type: 1, val: 50}}, {cost: {num: 48, val: 400}, limit: {type: 1, val: 50}}, {cost: {num: 48, val: 3000}, limit: {type: 2, val: 30}}, 
					 {cost: {num: 48, val: 4500}, limit: {type: 2, val: 30}}, {cost: {num: 48, val: 6000}, limit: {type: 2, val: 30}}, {cost: {num: 48, val: 4500}, limit: {type: 2, val: 30}}, {cost: {num: 48, val: 3000}, limit: {type: 2, val: 30}}, {cost: {num: 48, val: 4500}, limit: {type: 2, val: 30}}, 
					 {cost: {num: 48, val: 6000}, limit: {type: 2, val: 30}}, {cost: {num: 48, val: 4500}, limit: {type: 2, val: 30}}, {cost: {num: 48, val: 3000}, limit: {type: 2, val: 30}}, {cost: {num: 48, val: 4500}, limit: {type: 2, val: 30}}, {cost: {num: 48, val: 6000}, limit: {type: 2, val: 30}}, 
					 {cost: {num: 48, val: 4500}, limit: {type: 2, val: 30}}, {cost: {num: 48, val: 24000}, limit: {type: 3, val: 15}}, {cost: {num: 48, val: 24000}, limit: {type: 3, val: 15}}, {cost: {num: 48, val: 24000}, limit: {type: 3, val: 15}}, {cost: {num: 48, val: 24000}, limit: {type: 3, val: 15}}, 
					 {cost: {num: 48, val: 24000}, limit: {type: 3, val: 15}}, {cost: {num: 48, val: 24000}, limit: {type: 3, val: 15}}, {cost: {num: 48, val: 27000}, limit: {type: 3, val: 15}}, {cost: {num: 48, val: 27000}, limit: {type: 3, val: 15}}, {cost: {num: 48, val: 27000}, limit: {type: 3, val: 15}}, 
					 0, 0, 0, {cost: {num: 48, val: 1500}, limit: {type: 1, val: 40}}, {cost: {num: 48, val: 9000}, limit: {type: 2, val: 25}}, 
					 {cost: {num: 48, val: 37500}, limit: {type: 3, val: 15}}, 0, 0, 0, 0, 
					 0, 0, 0, 0, 0, 
					 0, 0, 0, 0, 0, 
					 0, {cost: {num: 48, val: 2000}, limit: {type: 2, val: 50}}, {cost: {num: 48, val: 6000}, limit: {type: 3, val: 25}}, {cost: {num: 48, val: 15000}, limit: {type: 4, val: 10}}, 0,
					 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
					 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
					 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
					 0, 0, 0, 0, 0, 0,
					 0, 0, 0, 0, 0, 0,
					 0, 0, 0, 0, 0, 0
					 ];
	#endregion
	
	#region		// Nama + Deskripsi EN
	var name_en = ["Oil Box 1", "Oil Box 2", "Oil Box 3", "Oil Box 4", "Oil Box 5", 
				   "Colorless Oil", "Red Oil", "Lime Oil", "Blue Oil", "Olive Oil", "Orange Oil", "Yellow Oil", "Chartreuse Oil", "Patriarch Oil", "Bright Pink Oil", 
				   "Fuchsia Oil", "Violet Oil", "Teal Oil", "Guppie Green Oil", "Aqua Oil", "Azure Oil", "Red-Orange Oil", "Harlequin Oil", "Electric Crimson Oil", "Electric Ultramarine Oil", 
				   "Malachite Oil", "Navy Blue Oil", "Acid Green Oil", "Deep Magenta Oil", "Tiffany Blue Oil", "Electric Brown Oil", "Mantis Oil", "Slate Blue Oil", "Metal Fragment", "Metal Piece", 
				   "Metal Ingot", "Metal Block", "A-Rank Substance", "S-Rank Substance", "SS-Rank Substance", "Core Mold", "Bearing Mold", "Crust Mold", "Sword Mold", "Bow Mold", 
				   "Knife Mold", 
				   "Player EXP", "Coin", "Crystal", "Daily Point", "Achievement Point", "Lead Acid Battery", "NiMH Battery", "Li-Ion Battery", "Li-Po Battery",
				   "Simple Core", "Mediocre Core", "Advanced Core A", "Advanced Core B", "Advanced Core C", "Advanced Core D", "Advanced Core E", "Advanced Core F", "Expert Core A", "Expert Core B", "Expert Core C", "Expert Core D", "Elite Core A", "Elite Core B", "Elite Core C", "Elite Core D", 
				   "Simple Bearing", "Mediocre Bearing", "Advanced Bearing A", "Advanced Bearing B", "Advanced Bearing C", "Advanced Bearing D", "Advanced Bearing E", "Advanced Bearing F", "Expert Bearing A", "Expert Bearing B", "Expert Bearing C", "Expert Bearing D", "Elite Bearing A", "Elite Bearing B", "Elite Bearing C", "Elite Bearing D", 
				   "Simple Crust", "Mediocre Crust", "Advanced Crust A", "Advanced Crust B", "Advanced Crust C", "Advanced Crust D", "Advanced Crust E", "Advanced Crust F", "Expert Crust A", "Expert Crust B", "Expert Crust C", "Expert Crust D", "Elite Crust A", "Elite Crust B", "Elite Crust C", "Elite Crust D", 
				   "Old Sword", "Damascus Sword", "Spring Sword", "Carbon-steel: I", "Sky Blue", "High CS Sword", 
				   "Old Bow", "Longbow", "Recurve Bow", "Compound Bow", "Violet", "High CO Bow", 
				   "Old Knife", "Damascus Knife", "D2 Steel Knife", "Carbon-steel: II", "Viridian", "High CS Knife"
				   ];
	var desc_en = ["A common material. You can choose one of available common oil using this item. Can be obtained from gift.",
				   "An uncommon material. You can choose one of available uncommon oil using this item. Can be obtained from gift or from lab (scrapping).",
				   "A rare material. You can choose one of available rare oil using this item. Can be obtained from gift or from lab (scrapping).",
				   "An epic material. You can choose one of available epic oil using this item. Can be obtained from gift or from lab (scrapping).",
				   "A legendary material. You can choose one of available legendary oil using this item. Can be obtained from gift or from lab (scrapping).",
				   "A common material. Commonly used to increase the amount of certain oils. Can be obtained from shop or item drop from enemies.",
				   "An uncommon material. Its hex code is #FF0000. Commonly used as a base material for oil mixing. Can be obtained from shop or item drop from enemies.",
				   "An uncommon material. Its hex code is #00FF00. Commonly used as a base material for oil mixing. Can be obtained from shop or item drop from enemies.",
				   "An uncommon material. Its hex code is #0000FF. Commonly used as a base material for oil mixing. Can be obtained from shop or item drop from enemies.",
				   "A rare material from a combination of red and lime oils. Its hex code is #7F7F00. Commonly used as a progressive material for oil mixing and upgrading Bots' skills. Can be obtained from shop, lab (mixing), or item drop from enemies.",
				   "A rare material from a combination of red and lime oils. Its hex code is #FF7F00. Commonly used as a progressive material for oil mixing and upgrading Bots' skills. Can be obtained from shop, lab (mixing), or item drop from enemies.",
				   "A rare material from a combination of red and lime oils. Its hex code is #FFFF00. Commonly used as a progressive material for oil mixing and upgrading Bots' skills. Can be obtained from shop, lab (mixing), or item drop from enemies.",
				   "A rare material from a combination of red and lime oils. Its hex code is #7FFF00. Commonly used as a progressive material for oil mixing and upgrading Bots' skills. Can be obtained from shop, lab (mixing), or item drop from enemies.",
				   "A rare material from a combination of red and blue oils. Its hex code is #7F007F. Commonly used as a progressive material for oil mixing and upgrading Bots' skills. Can be obtained from shop, lab (mixing), or item drop from enemies.",
				   "A rare material from a combination of red and blue oils. Its hex code is #FF007F. Commonly used as a progressive material for oil mixing and upgrading Bots' skills. Can be obtained from shop, lab (mixing), or item drop from enemies.",
				   "A rare material from a combination of red and blue oils. Its hex code is #FF00FF. Commonly used as a progressive material for oil mixing and upgrading Bots' skills. Can be obtained from shop, lab (mixing), or item drop from enemies.",
				   "A rare material from a combination of red and blue oils. Its hex code is #7F00FF. Commonly used as a progressive material for oil mixing and upgrading Bots' skills. Can be obtained from shop, lab (mixing), or item drop from enemies.",
				   "A rare material from a combination of lime and blue oils. Its hex code is #007F7F. Commonly used as a progressive material for oil mixing and upgrading Bots' skills. Can be obtained from shop, lab (mixing), or item drop from enemies.",
				   "A rare material from a combination of lime and blue oils. Its hex code is #00FF7F. Commonly used as a progressive material for oil mixing and upgrading Bots' skills. Can be obtained from shop, lab (mixing), or item drop from enemies.",
				   "A rare material from a combination of lime and blue oils. Its hex code is #00FFFF. Commonly used as a progressive material for oil mixing and upgrading Bots' skills. Can be obtained from shop, lab (mixing), or item drop from enemies.",
				   "A rare material from a combination of lime and blue oils. Its hex code is #007FFF. Commonly used as a progressive material for oil mixing and upgrading Bots' skills. Can be obtained from shop, lab (mixing), or item drop from enemies.",
				   "An epic material from a combination of orange and red oils. Its hex code is #FF4000. Commonly used as an advanced material for oil mixing, upgrading Bots' skills, and crafting weapons or equipments. Can be obtained from lab (mixing).",
				   "An epic material from a combination of chartreuse and lime oils. Its hex code is #40FF00. Commonly used as an advanced material for oil mixing, upgrading Bots' skills, and crafting weapons or equipments. Can be obtained from lab (mixing).",
				   "An epic material from a combination of bright pink and red oils. Its hex code is #FF0040. Commonly used as an advanced material for oil mixing, upgrading Bots' skills, and crafting weapons or equipments. Can be obtained from lab (mixing).",
				   "An epic material from a combination of violet and blue oils. Its hex code is #4000FF. Commonly used as an advanced material for oil mixing, upgrading Bots' skills, and crafting weapons or equipments. Can be obtained from lab (mixing).",
				   "An epic material from a combination of guppie green and lime oils. Its hex code is #00FF40. Commonly used as an advanced material for oil mixing, upgrading Bots' skills, and crafting weapons or equipments. Can be obtained from lab (mixing).",
				   "An epic material from a combination of azure and blue oils. Its hex code is #0040FF. Commonly used as an advanced material for oil mixing, upgrading Bots' skills, and crafting weapons or equipments. Can be obtained from lab (mixing).",
				   "An epic material from a combination of olive and yellow oils, or orange and chartreuse oils. Its hex code is #BFBF00. Commonly used as an advanced material for oil mixing, upgrading Bots' skills, and crafting weapons or equipments. Can be obtained from lab (mixing).",
				   "An epic material from a combination of patriarch and fuchsia oils, or bright pink and violet oils. Its hex code is #BF00BF. Commonly used as an advanced material for oil mixing, upgrading Bots' skills, and crafting weapons or equipments. Can be obtained from lab (mixing).",
				   "An epic material from a combination of teal and aqua oils, or guppie green and azure oils. Its hex code is #00BFBF. Commonly used as an advanced material for oil mixing, upgrading Bots' skills, and crafting weapons or equipments. Can be obtained from lab (mixing).",
				   "A legendary material from a combination of acid green and deep magenta oils. Its hex code is #BF6060. Commonly used as a superior material for oil mixing, upgrading Bots' skills, and crafting weapons or equipments. Can be obtained from lab (mixing).",
				   "A legendary material from a combination of acid green and tiffany blue oils. Its hex code is #60BF60. Commonly used as a superior material for oil mixing, upgrading Bots' skills, and crafting weapons or equipments. Can be obtained from lab (mixing).",
				   "A legendary material from a combination of deep magenta and tiffany blue oils. Its hex code is #6060BF. Commonly used as a superior material for oil mixing, upgrading Bots' skills, and crafting weapons or equipments. Can be obtained from lab (mixing).",
				   "An uncommon material. Commonly used as base material for crafting higher grade metals. Can be obtained from shop or item drop from enemies.",
				   "A rare material. Commonly used as progressive material for crafting higher grade metals, upgrading Bots' skills, and crafting weapons or equipments. Can be obtained from shop, lab (crafting), or item drop from enemies.",
				   "An epic material. Commonly used as advanced material for crafting higher grade metals, upgrading Bots' skills, and crafting weapons or equipments. Can be obtained from lab (crafting).",
				   "A legendary material. Commonly used as superior material for crafting higher grade metals, upgrading Bots' skills, and crafting weapons or equipments. Can be obtained from lab (crafting).",
				   "An epic material. Commonly used as advanced material for crafting higher grade substances or for crafting weapons or equipments. Can be obtained from lab (crafting or scrapping).",
				   "A legendary material. Commonly used as superior material for crafting higher grade substances or for crafting weapons or equipments. Can be obtained from lab (crafting or scrapping).",
				   "A mythic material. Commonly used as apex material for crafting weapons or equipments. Can be obtained from lab (crafting or scrapping).",
				   "An epic material. Commonly used as material for crafting Cores. Can be obtained from lab (scrapping).",
				   "An epic material. Commonly used as material for crafting Bearings. Can be obtained from lab (scrapping).",
				   "An epic material. Commonly used as material for crafting Crust. Can be obtained from lab (scrapping).",
				   "An epic material. Commonly used as material for crafting Swords. Can be obtained from lab (scrapping).",
				   "An epic material. Commonly used as material for crafting Bows. Can be obtained from lab (scrapping).",
				   "An epic material. Commonly used as material for crafting crust. Can be obtained from lab (scrapping).",
				   "A common growth point. Used to level up your player level. Can almost be obtained from anywhere in the game.",
				   "A common currency. Used to unlock Bots classes, unlock and upgrade weapons or equipment, or purchase something in the shop. Easy to obtain and of course easy to spend.",
				   "A precious currency. Used to unlock Bots classes, unlock high-tier weapons or equipment, or purchase something valuable. Can be obtained from first completion of a stage or item drop from powerful enemies.",
				   "Common growth point. Used to increase your daily task points, allowing you to earn daily task rewards. Can be obtained from completing daily tasks.", 
				   "Common growth point. Used to increase your achievement points. Can be obtained from completing achievements.",
				   "An uncommon growth item. Used to increase Bots' EXP by 500 points. Can be obtained from gifts.", 
				   "A rare growth item. Used to increase Bots' EXP by 2,000 points. Can be obtained from gifts.", 
				   "An epic growth item. Used to increase Bots' EXP by 5,000 points. Can be obtained from gifts.", 
				   "A legendary growth item. Used to increase Bots' EXP by 15,000 points. Can be obtained from gifts.",
				   "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
				   "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
				   "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
				   "", "", "", "", "", "",
				   "", "", "", "", "", "",
				   "", "", "", "", "", ""
				   ];
	#endregion
	
	#region		// Nama + Deskripsi ID
	var name_id = ["Kotak Minyak 1", "Kotak Minyak 2", "Kotak Minyak 3", "Kotak Minyak 4", "Kotak Minyak 5", 
				   "Minyak Bening", "Minyak Merah", "Minyak Hijau Muda", "Minyak Biru", "Minyak Olive", "Minyak Oranye", "Minyak Kuning", "Minyak Chartreuse", "Minyak Ungu", "Minyak Pink", 
				   "Minyak Fuchsia", "Minyak Violet", "Minyak Teal", "Minyak Guppie Green", "Minyak Aqua", "Minyak Azure", "Minyak Merah-Oranye", "Minyak Harlequin", "Minyak Electric Crimson", "Minyak Electric Ultramarine", 
				   "Minyak Malachite", "Minyak Navy Blue", "Minyak Acid Green", "Minyak Deep Magenta", "Minyak Tiffany Blue", "Minyak Electric Brown", "Minyak Mantis", "Minyak Slate Blue", "Pecahan Logam", "Kepingan Logam", 
				   "Batangan Logam", "Balok Logam", "Substansi Rank-A", "Substansi Rank-S", "Substansi Rank-SS", "Cetakan Core", "Cetakan Bearing", "Cetakan Crust", "Cetakan Pedang", "Cetakan Busur", 
				   "Cetakan Pisau",
				   "EXP Pemain", "Koin", "Kristal", "Poin Harian", "Poin Pencapaian", "Baterai Asam Timbal", "Baterai NiMH", "Baterai Li-Ion", "Baterai Li-Po",
				   "Simple Core", "Mediocre Core", "Advanced Core A", "Advanced Core B", "Advanced Core C", "Advanced Core D", "Advanced Core E", "Advanced Core F", "Expert Core A", "Expert Core B", "Expert Core C", "Expert Core D", "Elite Core A", "Elite Core B", "Elite Core C", "Elite Core D", 
				   "Simple Bearing", "Mediocre Bearing", "Advanced Bearing A", "Advanced Bearing B", "Advanced Bearing C", "Advanced Bearing D", "Advanced Bearing E", "Advanced Bearing F", "Expert Bearing A", "Expert Bearing B", "Expert Bearing C", "Expert Bearing D", "Elite Bearing A", "Elite Bearing B", "Elite Bearing C", "Elite Bearing D", 
				   "Simple Crust", "Mediocre Crust", "Advanced Crust A", "Advanced Crust B", "Advanced Crust C", "Advanced Crust D", "Advanced Crust E", "Advanced Crust F", "Expert Crust A", "Expert Crust B", "Expert Crust C", "Expert Crust D", "Elite Crust A", "Elite Crust B", "Elite Crust C", "Elite Crust D", 
				   "Old Sword", "Damascus Sword", "Spring Sword", "Carbon-steel: I", "Sky Blue", "High CS Sword", 
				   "Old Bow", "Longbow", "Recurve Bow", "Compound Bow", "Violet", "High CO Bow", 
				   "Old Knife", "Damascus Knife", "D2 Steel Knife", "Carbon-steel: II", "Viridian", "High CS Knife"
				   ];
	var desc_id = ["Sebuah bahan umum. Kamu bisa memilih oli umum yang tersedia menggunakan item ini. Bisa didapatkan dari hadiah.",
				   "Sebuah bahan tidak umum. Kamu bisa memilih oli tidak umum yang tersedia menggunakan item ini. Bisa didapatkan dari hadiah atau dari membongkar senjata atau peralatan.",
				   "Sebuah bahan langka. Kamu bisa memilih oli langka yang tersedia menggunakan item ini. Bisa didapatkan dari hadiah atau dari membongkar senjata atau peralatan.",
				   "Sebuah bahan epik. Kamu bisa memilih oli epik yang tersedia menggunakan item ini. Bisa didapatkan dari hadiah atau dari membongkar senjata atau peralatan.",
				   "Sebuah bahan legendaris. Kamu bisa memilih oli legendaris yang tersedia menggunakan item ini. Bisa didapatkan dari hadiah atau dari membongkar senjata atau peralatan.",
				   "Sebuah bahan umum. Biasa digunakan untuk menambah jumlah dari oli tertentu. Bisa didapatkan dari toko atau item jatuh dari musuh.",
				   "Sebuah bahan tidak umum. Kode hex-nya adalah #FF0000. Biasa digunakan sebagai bahan dasar untuk pencampuran oli. Bisa didapatkan dari toko atau item jatuh dari musuh.",
				   "Sebuah bahan tidak umum. Kode hex-nya adalah #00FF00. Biasa digunakan sebagai bahan dasar untuk pencampuran oli. Bisa didapatkan dari toko atau item jatuh dari musuh.",
				   "Sebuah bahan tidak umum. Kode hex-nya adalah #0000FF. Biasa digunakan sebagai bahan dasar untuk pencampuran oli. Bisa didapatkan dari toko atau item jatuh dari musuh.",
				   "Sebuah bahan langka dari campuran antara oli red dan lime. Kode hex-nya adalah #7F7F00. Biasa digunakan sebagai bahan progresif untuk pencampuran oli dan meningkatkan skill Bot. Bisa didapatkan dari toko, lab (pencampuran), atau item jatuh dari musuh.",
				   "Sebuah bahan langka dari campuran antara oli red dan lime. Kode hex-nya adalah #FF7F00. Biasa digunakan sebagai bahan progresif untuk pencampuran oli dan meningkatkan skill Bot. Bisa didapatkan dari toko, lab (pencampuran), atau item jatuh dari musuh.",
				   "Sebuah bahan langka dari campuran antara oli red dan lime. Kode hex-nya adalah #FFFF00. Biasa digunakan sebagai bahan progresif untuk pencampuran oli dan meningkatkan skill Bot. Bisa didapatkan dari toko, lab (pencampuran), atau item jatuh dari musuh.",
				   "Sebuah bahan langka dari campuran antara oli red dan lime. Kode hex-nya adalah #7FFF00. Biasa digunakan sebagai bahan progresif untuk pencampuran oli dan meningkatkan skill Bot. Bisa didapatkan dari toko, lab (pencampuran), atau item jatuh dari musuh.",
				   "Sebuah bahan langka dari campuran antara oli red dan blue. Kode hex-nya adalah #7F007F. Biasa digunakan sebagai bahan progresif untuk pencampuran oli dan meningkatkan skill Bot. Bisa didapatkan dari toko, lab (pencampuran), atau item jatuh dari musuh.",
				   "Sebuah bahan langka dari campuran antara oli red dan blue. Kode hex-nya adalah #FF007F. Biasa digunakan sebagai bahan progresif untuk pencampuran oli dan meningkatkan skill Bot. Bisa didapatkan dari toko, lab (pencampuran), atau item jatuh dari musuh.",
				   "Sebuah bahan langka dari campuran antara oli red dan blue. Kode hex-nya adalah #FF00FF. Biasa digunakan sebagai bahan progresif untuk pencampuran oli dan meningkatkan skill Bot. Bisa didapatkan dari toko, lab (pencampuran), atau item jatuh dari musuh.",
				   "Sebuah bahan langka dari campuran antara oli red dan blue. Kode hex-nya adalah #7F00FF. Biasa digunakan sebagai bahan progresif untuk pencampuran oli dan meningkatkan skill Bot. Bisa didapatkan dari toko, lab (pencampuran), atau item jatuh dari musuh.",
				   "Sebuah bahan langka dari campuran antara oli lime dan blue. Kode hex-nya adalah #007F7F. Biasa digunakan sebagai bahan progresif untuk pencampuran oli dan meningkatkan skill Bot. Bisa didapatkan dari toko, lab (pencampuran), atau item jatuh dari musuh.",
				   "Sebuah bahan langka dari campuran antara oli lime dan blue. Kode hex-nya adalah #00FF7F. Biasa digunakan sebagai bahan progresif untuk pencampuran oli dan meningkatkan skill Bot. Bisa didapatkan dari toko, lab (pencampuran), atau item jatuh dari musuh.",
				   "Sebuah bahan langka dari campuran antara oli lime dan blue. Kode hex-nya adalah #00FFFF. Biasa digunakan sebagai bahan progresif untuk pencampuran oli dan meningkatkan skill Bot. Bisa didapatkan dari toko, lab (pencampuran), atau item jatuh dari musuh.",
				   "Sebuah bahan langka dari campuran antara oli lime dan blue. Kode hex-nya adalah #007FFF. Biasa digunakan sebagai bahan progresif untuk pencampuran oli dan meningkatkan skill Bot. Bisa didapatkan dari toko, lab (pencampuran), atau item jatuh dari musuh.",
				   "Sebuah bahan epik dari campuran antara oli orange dan red. Kode hex-nya adalah #FF4000. Biasa digunakan sebagai bahan lanjutan untuk pencampuran oli, meningkatkan skill Bot, dan membuat senjata atau peralatan. Bisa didapatkan dari lab (pencampuran).",
				   "Sebuah bahan epik dari campuran antara oli chartreuse dan lime. Kode hex-nya adalah #40FF00. Biasa digunakan sebagai bahan lanjutan untuk pencampuran oli, meningkatkan skill Bot, dan membuat senjata atau peralatan. Bisa didapatkan dari lab (pencampuran).",
				   "Sebuah bahan epik dari campuran antara oli bright pink dan red. Kode hex-nya adalah #FF0040. Biasa digunakan sebagai bahan lanjutan untuk pencampuran oli, meningkatkan skill Bot, dan membuat senjata atau peralatan. Bisa didapatkan dari lab (pencampuran).",
				   "Sebuah bahan epik dari campuran antara oli violet dan blue. Kode hex-nya adalah #4000FF. Biasa digunakan sebagai bahan lanjutan untuk pencampuran oli, meningkatkan skill Bot, dan membuat senjata atau peralatan. Bisa didapatkan dari lab (pencampuran).",
				   "Sebuah bahan epik dari campuran antara oli guppie green dan lime. Kode hex-nya adalah #00FF40. Biasa digunakan sebagai bahan lanjutan untuk pencampuran oli, meningkatkan skill Bot, dan membuat senjata atau peralatan. Bisa didapatkan dari lab (pencampuran).",
				   "Sebuah bahan epik dari campuran antara oli azure dan blue. Kode hex-nya adalah #0040FF. Biasa digunakan sebagai bahan lanjutan untuk pencampuran oli, meningkatkan skill Bot, dan membuat senjata atau peralatan. Bisa didapatkan dari lab (pencampuran).",
				   "Sebuah bahan epik dari campuran antara oli olive dan yellow, atau oli orange dan chartreuse. Kode hex-nya adalah #BFBF00. Biasa digunakan sebagai bahan lanjutan untuk pencampuran oli, meningkatkan skill Bot, dan membuat senjata atau peralatan. Bisa didapatkan dari lab (pencampuran).",
				   "Sebuah bahan epik dari campuran antara oli patriarch dan fuchsia, atau oli bright pink dan violet. Kode hex-nya adalah #BF00BF. Biasa digunakan sebagai bahan lanjutan untuk pencampuran oli, meningkatkan skill Bot, dan membuat senjata atau peralatan. Bisa didapatkan dari lab (pencampuran).",
				   "Sebuah bahan epik dari campuran antara oli teal dan aqua, atau oli guppie green dan azure. Kode hex-nya adalah #00BFBF. Biasa digunakan sebagai bahan lanjutan untuk pencampuran oli, meningkatkan skill Bot, dan membuat senjata atau peralatan. Bisa didapatkan dari lab (pencampuran).",
				   "Sebuah bahan legendaris dari campuran antara oli acid green dan deep magenta. Kode hex-nya adalah #BF6060. Biasa digunakan sebagai bahan unggul untuk pencampuran oli, meningkatkan skill Bot, dan membuat senjata atau peralatan. Bisa didapatkan dari lab (pencampuran).",
				   "Sebuah bahan legendaris dari campuran antara oli acid green dan tiffany blue. Kode hex-nya adalah #60BF60. Biasa digunakan sebagai bahan unggul untuk pencampuran oli, meningkatkan skill Bot, dan membuat senjata atau peralatan. Bisa didapatkan dari lab (pencampuran).",
				   "Sebuah bahan legendaris dari campuran antara oli deep magenta dan tiffany blue. Kode hex-nya adalah #6060BF. Biasa digunakan sebagai bahan unggul untuk pencampuran oli, meningkatkan skill Bot, dan membuat senjata atau peralatan. Bisa didapatkan dari lab (pencampuran).",
				   "Sebuah bahan tidak umum. Biasa digunakan sebagai bahan dasar untuk membuat logam dengan tingkat yang lebih tinggi. Bisa didapatkan dari toko atau item jatuh dari musuh.", 
				   "Sebuah bahan langka. Biasa digunakan sebagai bahan progresif untuk membuat logam dengan tingkat yang lebih tinggi, meningkatkan skill Bot, dan membuat senjata atau peralatan. Bisa didapatkan dari toko, lab (kerajinan), atau item jatuh dari musuh.",
				   "Sebuah bahan epik. Biasa digunakan sebagai bahan lanjutan untuk membuat logam dengan tingkat yang lebih tinggi, meningkatkan skill Bot, dan membuat senjata atau peralatan. Bisa didapatkan dari lab (kerajinan).",
				   "Sebuah bahan legendaris. Biasa digunakan sebagai bahan unggul untuk membuat logam dengan tingkat yang lebih tinggi, meningkatkan skill Bot, dan membuat senjata atau peralatan. Bisa didapatkan dari lab (kerajinan).",
				   "Sebuah bahan epik. Biasa digunakan sebagai bahan lanjutan untuk membuat substansi dengan tingkat yang lebih tinggi atau untuk membuat senjata atau peralatan. Bisa didapatkan dari lab (kerajinan atau pembongkaran).",
				   "Sebuah bahan legendaris. Biasa digunakan sebagai bahan unggul untuk membuat substansi dengan tingkat yang lebih tinggi atau untuk membuat senjata atau peralatan. Bisa didapatkan dari lab (kerajinan atau pembongkaran).",
				   "Sebuah bahan mistis. Biasa digunakan sebagai bahan puncak untuk membuat senjata atau peralatan. Bisa didapatkan dari lab (kerajinan atau pembongkaran).",
				   "Sebuah bahan epik. Biasa digunakan sebagai bahan untuk membuat Core. Bisa didapatkan dari lab (pembongkaran).",
				   "Sebuah bahan epik. Biasa digunakan sebagai bahan untuk membuat Bearing. Bisa didapatkan dari lab (pembongkaran).",
				   "Sebuah bahan epik. Biasa digunakan sebagai bahan untuk membuat Crust. Bisa didapatkan dari lab (pembongkaran).",
				   "Sebuah bahan epik. Biasa digunakan sebagai bahan untuk membuat Pedang. Bisa didapatkan dari lab (pembongkaran).",
				   "Sebuah bahan epik. Biasa digunakan sebagai bahan untuk membuat Busur. Bisa didapatkan dari lab (pembongkaran).",
				   "Sebuah bahan epik. Biasa digunakan sebagai bahan untuk membuat Pisau. Bisa didapatkan dari lab (pembongkaran).",
				   "Sebuah poin pertumbuhan umum. Digunakan untuk menaikkan level pemainmu. Hampir dapat diperoleh dari mana saja di dalam permainan.",
				   "Sebuah mata uang umum. Digunakan untuk membuka kelas Bot, membuka dan meningkatkan senjata atau peralatan, atau membeli sesuatu di toko. Mudah didapatkan dan tentu saja mudah dihabiskan.",
				   "Sebuah mata uang berharga. Digunakan untuk membuka kelas Bot, membuka senjata atau peralatan tingkat tinggi, atau membeli sesuatu yang bernilai. Bisa didapatkan dari penyelesaian pertama dari stage atau item jatuh dari musuh yang kuat.",
				   "Sebuah poin pertumbuhan umum. Digunakan untuk menambahkan poin tugas harianmu, sehingga kamu bisa mendapatkan hadiah tugas harian. Bisa didapatkan dari menyelesaikan tugas harian.",
				   "Sebuah poin pertumbuhan umum. Digunakan untuk menambahkan poin pencapaianmu. Bisa didapatkan dari menyelesaikan pencapaian.",
				   "Sebuah item pertumbuhan tidak umum. Digunakan untuk menambah EXP Bot sebanyak 500 poin. Bisa didapatkan dari hadiah.",
				   "Sebuah item pertumbuhan langka. Digunakan untuk menambah EXP Bot sebanyak 2,000 poin. Bisa didapatkan dari hadiah.",
				   "Sebuah item pertumbuhan epik. Digunakan untuk menambah EXP Bot sebanyak 5,000 poin. Bisa didapatkan dari hadiah.",
				   "Sebuah item pertumbuhan legendaris. Digunakan untuk menambah EXP Bot sebanyak 15,000 poin. Bisa didapatkan dari hadiah.",
				   "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
				   "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
				   "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
				   "", "", "", "", "", "",
				   "", "", "", "", "", "",
				   "", "", "", "", "", ""];
	#endregion
	
	var db = ds_grid_create(14, array_length(item_name));
	file_text_decrypt("general.txt");
	for (var i = 0; i < array_length(item_name); i++) {
		var cnt = 0;
		var frame = i;
		var spr = noone;
		var itemspr = noone;
		var link_num = 0;
		switch (item_type[i]) {
			case 1:
			case 2:
			case 3:
			case 4:
				if (item_name[i] == "daily_point") {
					cnt = ini_read_real("task", item_name[i], 0);
				} else if (item_name[i] == "total_point") {
					cnt = ini_read_real("achievement", item_name[i], 0);
				} else {
					cnt = ini_read_real("main", item_name[i], 0);
				}
				break;
		}
		if (is_struct(item_attr[i])) {
			item_attr[i].limit.bought = ini_read_real("shop", "item"+string(i+1), 0);
		}
		
		var type_sum = 0;
		for (var j = 0; j < array_length(all_type); j++) {
		    var prev_sum = type_sum;
		    type_sum += all_type[j];

		    if (frame > prev_sum - 1 && frame <= type_sum - 1) {
		        frame -= prev_sum;
				spr = item_spr[j];
				itemspr = item_itemspr[j];
				if (item_type[i] >= 5) {
					link_num = frame + 1;
				}
		        break;
		    }
		}
		
		var val = [i+1, item_type[i], item_name[i], spr, frame, item_grade[i], cnt, name_en[i], desc_en[i], name_id[i], desc_id[i], link_num, item_attr[i], itemspr];
		for (var j = 0; j < array_length(val); j++) {
			ds_grid_set(db, j, i, val[j]);
		}
	}
	file_text_encrypt("general.txt");
	return db;
}

function db_create_talent(db_bots, class_num) {
	// class_num mulai dari 1
	var name = [];
	var type = [];
	var title = "";
	var desc = "";
	var active_req = [];
	var cost = [];
	var main_talent = 0;
	var sprite = [];
	var frame = 0;
	
	switch (class_num) {
		case 1:			// Warrior
			name = ["m1", "m2", "m3", "m4", "m5", "m6", "m7", "m8", "m9", "m10", "m11", "m12", "m13", 
					"s1", "s2", "s3", "s4", "s5", "s6", "s7", "s8", "s9", "s10", "s11", "s12", "s13", "s14", "s15", "s16", "s17", "s18", "s19", "s20"];
			type = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 
					1, 1, 2, 3, 3, 1, 1, 2, 3, 3, 1, 1, 2, 3, 3, 1, 1, 2, 3, 3];
			active_req = [[], [1], [1], [1], [2, 3, 4], [5], [], [7], [8], [8], [8], [8], [6, 9, 10, 11, 12],
						  [], [14], [15], [16], [17], [], [19], [20], [21], [22], [], [24], [25], [26], [27], [], [29], [30], [31], [32]];
			cost = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 
					2, 2, 3, 4, 4, 2, 2, 3, 4, 4, 2, 2, 3, 4, 4, 2, 2, 3, 4, 4];
			main_talent = 13;
			sprite = [sBotsTalentWarriorM, sBotsTalentWarriorS];
			switch (global.language) {
				case 1:
					title = ["Health Points Up", "Critical Damage Up", "Physical Damage Reduction Up", "Crowd-Control Power Up", "Team Synergy", "Tactical Dash", "Slash V", "Slash VI", "Double Slash III", "Charged Slash Phase 3", "Smash III", "Counter Attack", "Retaliate: Doom Drop",
							 "Boosted Self Repair", "Periodic Super-Armor", "Super Blow", "Long-Lasting Determination", "Hyperbody", "Triple Thrust", "Focused Chase", "Rapid Cut", "Hyper-Reflex", "Terminal Trounce",
							 "Energy Accumulation", "Elevated Slam", "Accelerated Swing", "Broad Counter Attack", "Reprisal", "Quick Draw", "Wide Slash", "Backstab", "Sequence Breaker Module", "Dreadful Shockwave"];
					desc = ["Meningkatkan Health Points milik Warrior sebesar 10%.", 
							"Meningkatkan Critical Damage milik Warrior sebesar 20%.", 
							"Meningkatkan Physical Damage Reduction milik Warrior sebesar 10%.", 
							"Meningkatkan Crowd-Control Power milik Warrior sebesar 15%.", 
							"Mengaktifkan bonus sinergi tim milik Warrior: kalau Archer berada di dalam tim, Warrior mendapatkan 10% Damage Output.", 
							"Warrior bisa menyimpan rangkaian serangan dasarnya setelah menggunakan dash. Tekan tombol dash untuk melakukan dash dan menyimpan rangkaian serangan dasarnya, atau tahan tombol dash untuk melakukan dash dan memulai ulang rangkaian serangan dasarnya.", 
							"Membuka Rantai V pada \"Slash\" milik Warrior.",
							"Membuka Rantai VI pada \"Slash\" milik Warrior.", 
							"Membuka Rantai III pada \"Souble Slash\" milik Warrior.", 
							"Membuka Tahap 3 dari \"Charged Slash\" milik Warrior.", 
							"Membuka Rantai III pada \"Smash\" milik Warrior.", 
							"Mengganti gerakan spesial \"Parry\" milik Warrior menjadi \"Parry & Counter\".", 
							"Setelah Warrior menghindari serangan ultimate musuh, Warrior lompat kepada musuh dan melakukan tusukan ke bawah, menyebabkan kerusakan 10% dari HP maksimal musuh sebagai kerusakan absolut.", 
							"Mengalikan jumlah penyembuhan dari skill turunan \"Self Repair\" sebanyak 1,5x.", 
							"Kalau Warrior tidak dalam Form Bertahan pada skill unik \"ADS Adaptation\", Warrior mendapatkan Super-Armor sebesar 5% dari HP maksimalnya selama 5 detik, setiap 10 detik.", 
							"Meningkatkan basis kerusakan \"Blow\" sebesar 50% dan kekuatan knockback-nya sebesar 25%. \"Blow\" juga mengurangi Ignore Interruption musuh sebesar 10% selama 3 detik.", 
							"Memperpanjang durasi gerakan spesial \"Determination\" sebesar 50%. \"Determination\" juga akan meningkatkan Ignore Interruption semua Bot yang terpengaruh sebesar 20%.", 
							"Modul \"Superbody\" akan terpicu setiap kali Warrior kehilangan 30% HP, bukan 40%. Super-Armor dari \"Superbody\" juga tidak akan memudar.",
							"Menggunakan \"Twin-Thrust\" setelah \"Thrust\" akan mengubah \"Twin-Thrust\" ini menjadi \"Triple Thrust\".", 
							"Efek skill integral \"Chase\" juga akan mengurangi Critical Protection milik musuh yang ditargetkan sebesar 10% selama 10 detik.", 
							"Warrior akan menghindari semua serangan musuh saat menggunakan \"Side Cut\". Warrior juga mendapatkan 1 tumpukan \"Side Cut\" setiap 5 musuh yang diserang menggunakan serangan ini.", 
							"Mengakhiri cooldown skill turunan \"Redirect\" ketika serangan musuh berhasil dialihkan. Juga akan mengakhiri cooldown gerakan spesial \"Parry & Counter\" ketika memicu tangkisan sempurna.", 
							"Mengurangi poin pengisian maksimum \"Devastating  Ambush\" dari 300 ke 200. Serangan terkaman akan selalu menyebabkan serangan kritis, dan akan selalu menyebabkan serangan maut kalau poin critical buildup milik Warrior diatas 75%.",
							"Menggunakan \"Dash\" saat mengisi \"Charged Slash\" tidak akan lagi mengulang poin pengingisian daya.", 
							"Serangan \"Slam\" sekarang berskala dengan jarak antara Warrior dengan tanah, mendapatkan 1% Physical Damage Output setiap 5 pixel, tidak ada batasan.", 
							"Meningkatkan jumlah target dari skill ultimate \"Whirlwind Cut\" sebanyak 5. Juga akan meningkatkan jumlah ayunannya sebanyak 3.", 
							"Gerakan spesial \"Parry & Counter\" juga akan meningkatkan Acid Damage Reduction milik Warrior setara dengan Physical Damage Reduction pada \"Parry & Counter\". Serangan baliknya bisa memukul 3 musuh sekaligus.", 
							"Kalau Warrior diserang saat mengisi daya, Warrior akan mendapatkan Damage Output ekstra sebesar 10% untuk serangan berikutnya. Bisa ditumpuk sampai 5 tumpukan. Warrior juga akan mendapatkan 3 tumpukan ini setelah menghindari serangan musuh.",
							"Meningkatkan kecepatan mengeluarkan dan menyarungkan senjata milik Warrior sebesar 50%.", 
							"Meningkatkan jumlah target dari \"Slash\", \"Double Slash\", dan \"Charged Slash\" sebanyak 2.", 
							"Meningkatkan basis kerusakan \"Stab\" sebesar 100%. Menyerang musuh dari belakang menggunakan \"Stab\" akan membuat musuh tersentak.", 
							"Menambah modul \"Sequence Breaker\" skill integral milik Warrior. \"Sequence Breaker\" ini akan meningkatkan ATK milik Warrior dan memulihkan 2% HP dari HP maksimalnya selama 5 seconds saat modul ini aktif.", 
							"Meningkatkan kerusakan dari skill ultimate \"Daunting Shockwaves\" sebesar 15%. Setiap gelombang kejut bisa menyerang 2 musuh sekaligus. Setelah 10 detik penggunaan skill ini, Warrior akan mengeluarkan serangan pamungkas, menyebabkan 500% ATK sebagai kerusakan fisik. Kerusakan ini meningkat sebesar 25% untuk setiap gelombang kejut yang dikeluarkan, tanpa batas."];
				break;
				case 2:
					title = ["Health Point Up", "Critical Damage Up", "Physical Damage Reduction Up", "Crowd-Control Power Up", "Team Synergy", "Tactical Dash", "Slash V", "Slash VI", "Double Slash III", "Charged Slash Phase 3", "Smash III", "Counter Attack", "Retaliate: Doom Drop",
							 "Boosted Self Repair", "Periodic Super-Armor", "Super Blow", "Long-Lasting Determination", "Hyperbody", "Triple Thrust", "Focused Chase", "Rapid Cut", "Hyper-Reflex", "Terminal Trounce",
							 "Energy Accumulation", "Elevated Slam", "Accelerated Swing", "Broad Counter Attack", "Reprisal", "Quick Draw", "Wide Slash", "Backstab", "Sequence Breaker Module", "Dreadful Shockwave"];
					desc = ["Increases Warrior's Health Points by 10%.", 
							"Increases Warrior's Critical Damage by 20%.", 
							"Increases Warrior's Physical Damage Reduction by 10%.", 
							"Increases Warrior's Crowd-Control Power by 15%.", 
							"Activates Warrior's team synergy bonus: if Archer is also on the team, Warrior gains 10% Damage Output.", 
							"Warrior can save its current basic attack sequence after dashing. Press the dash button to dash and preserve the current sequence, or hold it to dash and reset the sequence instead.", 
							"Unlocks Chain V of Warrior's \"Slash.\"",
							"Unlocks Chain VI of Warrior's \"Slash.\"", 
							"Unlocks Chain III of Warrior's \"Double Slash.\"", 
							"Unlocks Phase 3 of Warrior's \"Charged Slash.\"", 
							"Unlock Chain III of Warrior's \"Smash.\"", 
							"Changes Warrior's \"Parry\" special move into \"Parry & Counter.\"", 
							"When Warrior dodges an enemy's ultimate, it leap in and strike down, dealing 10% of the enemy's max HP as absolute damage.", 
							"Multiply the healing amount of the \"Self Repair\" derivative skill by 1,5x.", 
							"If Warrior isn't in Defense Form while using the \"ADS Adaptation\" unique skill, it gain Super-Armor equal to 5% of max HP for 5 seconds, every 10 seconds.", 
							"Increases \"Blow\" base damage by 50% and knockback power by 25%. Also makes enemies 10% more vulnerable to interruption for 3 seconds.", 
							"Extends the duration of \"Determination\" special move by 50% and boosts Ignore Interruption on all affected bots by 20%.", 
							"\"Superbody\" module now triggers at 30% HP instead of 40%, and its Super-Armor no longer decays.",
							"Using \"Twin-Thrust\" right after \"Thrust\" transforms it into \"Triple Thrust.\"", 
							"The \"Chase\" effect also lowers the target's Critical Protection by 10% for 10 seconds.", 
							"While using \"Side Cut,\" Warrior dodges all incoming attacks. Hitting 5 or more enemies with \"Side Cut\" grants 1 stack of \"Side Cut.\"", 
							"Successfully redirecting an attack instantly resets \"Redirect's\" cooldown. A perfect parry also resets \"Parry's\" cooldown.", 
							"Reduces \"Devastating Ambush\" ultimate skill's max charge from 300 to 200. The attack always crits, and lands a grave hit if critical buildup is over 75%.",
							"Using \"Dash\" while charging \"Charged Slash\" no longer resets charge progress.", 
							"\"Slam\" now scales with distance to the ground, gaining 1% extra damage per 5 pixels, no cap.", 
							"\"Whirlwind Cut\" ultimate skill gains 5 extra targets and 3 additional swings.", 
							"\"Parry\" also boosts Warrior's Acid Damage Reduction based on its Physical Damage Reduction. \"Counter Attack\" now hits up to 3 enemies.", 
							"Getting hit while charging boosts Warrior next attack's Damage Output by 10%. Stacks up to 5 times. Dodging also adds 3 stacks.",
							"Speeds up weapon draw and sheath animations by 50%.", 
							"Increases max targets for \"Slash,\" \"Double Slash,\" and \"Charged Slash\" by 2 each.", 
							"Increases \"Stab\" base damage by 100%. Attacking from behind with \"Stab\" always crits and causes flinching.", 
							"Adds the \"Sequence Breaker\" module to Warrior's integral skill. When activated, it boosts ATK Scale by 15% and regenerates 2% HP every second for 5 seconds.", 
							"\"Daunting Shockwave\" ultimate skill's damage is boosted by 15%. Each shockwave can now hit up to 2 enemies. After 10 seconds, Warrior unleashes a powerful blow, dealing 500% ATK as physical damage. Damage is boosted by 25% for every shockwave created, no cap."];
				break;
			}
		break;
		case 2:			// Archer
			name = ["m1", "m2", "m3", "m4", "m5", "m6", "m7", "m8", "m9", "m10", "m11", "m12", "m13", 
					"s1", "s2", "s3", "s4", "s5", "s6", "s7", "s8", "s9", "s10", "s11", "s12", "s13", "s14", "s15", "s16", "s17", "s18", "s19", "s20"];
			type = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 
					1, 1, 2, 3, 3, 1, 1, 2, 3, 3, 1, 1, 2, 3, 3, 1, 1, 2, 3, 3];
			active_req = [[], [1], [1], [2, 3], [4], [], [6], [], [8], [7, 9], [10], [10], [5, 11, 12],
						  [], [14], [15], [16], [17], [], [19], [20], [21], [22], [], [24], [25], [26], [27], [], [29], [30], [31], [32]];
			cost = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 
					2, 2, 3, 4, 4, 2, 2, 3, 4, 4, 2, 2, 3, 4, 4, 2, 2, 3, 4, 4];
			main_talent = 13;
			sprite = [sBotsTalentArcherM, sBotsTalentArcherS];
			switch (global.language) {
				case 1:
					title = ["Defense Up", "Critical Buildup Up", "Armor Penetration Up", "Team Synergy", "Tactical Dash", "Ordinary Shot V", "Ordinary Shot VI", "Acidic Shot V", "Acidic Shot VI", "Piercing Shot VI", "Charged Shot Phase 3", "Electric Shot", "Retaliate: Soulsnare Shot",
							 "Proper Reaction", "Skilled Spacekeeper", "Airborne Parallel Shot", "Echoes of Cloudbursts", "High-Impact Explosives", "Fast Arrows", "Sharp Arrowhead", "Double Acidic Shot", "A Fruit of Patience", "Shell Perforator",
							 "Ranger Smasher", "Crippling Impact", "Full-Powered Arrow", "Sturdy Stance", "Hypercharged Shot", "Sharp Flick Shot", "Steady Quasher", "Quick Repetition", "Half-Moon Barrage", "Flexible Framework"];
					desc = ["Meningkatkan Defense milik Archer sebesar 15%.", 
							"Meningkatkan Critical Buildup milik Archer sebesar 10%.", 
							"Meningkatkan Armor Penetration milik Archer sebesar 15%.", 
							"Mengaktifkan bonus sinergi tim milik Archer: kalau Warrior berada di dalam tim, Archer mendapatkan 10% Damage Output.", 
							"Archer bisa menyimpan rangkaian serangan dasarnya setelah menggunakan dash. Tekan tombol dash untuk melakukan dash dan menyimpan rangkaian serangan dasarnya, atau tahan tombol dash untuk melakukan dash dan memulai ulang rangkaian serangan dasarnya.", 
							"Membuka Rantai V pada \"Ordinary Shot\" milik Archer.", 
							"Membuka Rantai VI pada \"Ordinary Shot\" milik Archer.",
							"Membuka Rantai V pada \"Acidic Shot\" milik Archer.", 
							"Membuka Rantai VI pada \"Acidic Shot\" milik Archer.", 
							"Membuka Rantai V dan VI pada \"Piercing Shot\" milik Archer.", 
							"Membuka Tahap 3 dari \"Charged Shot\" dan Tahap 4 dari \"Supercharged Shot\" milik Archer.", 
							"Mengganti gerakan spesial \"Spike Shot\" milik Archer menjadi \"Electric Shot\".", 
							"Setelah Archer menghindari serangan ultimate musuh, Archer melakukan tembakan jarak dekat, menyebabkan kerusakan 10% dari HP maksimal musuh sebagai kerusakan absolut.",
							"Meningkatkan basis kerusakan dari skill turunan \"Follow-Up Shot\" sebesar 10%.", 
							"Menggandakan jumlah musuh yang bisa dipengaruhi pada skill integral \"Spatial Precision\".", 
							"\"Parallel Shot\" mendapatkan 1 anak panah tambahan ketika digunakan saat mengudara. Juga menambah Multi-Projectile Angle Reduction pada \"Parallel Shot\" sebesar 30% ketika digunakan saat mengudara.", 
							"Skill ultimate \"Cloudburst Volley\" mendapatkan 2 anak panah tambahan setiap 1 level penyesuaian \"Waktu Tunda\". Setiap anak panah \"Cloudburst Volley\" juga bisa menembus 2 musuh sekaligus.", 
							"Meningkatkan radius ledakan \"Explosive Shot\" dan gerakan spesial \"Electric Shot\" sebesar 35%, atau 75% kalau serangan ini mengenai tanah. Kalau gerakan spesial \"Electric Shot\" meledak di tanah, ia akan meninggalkan medan listrik selama 5 detik, menyebabkan kerusakan asam kecil dari ATK per detik dan melambatkan semua Bot di dalam area ini sebesar 35%.",
							"Mempercepat semua anak panah milik Archer sebesar 30%.", 
							"Meningkatkan basis kerusakan pada \"Ordinary Shot\", \"Double Shot\", \"Acidic Shot\", dan \"Piercing Shot\" sebesar 20%.", 
							"Mengganti jenis kerusakan \"Double Shot\" menjadi kerusakan asam kalau posisi rangkaiannya diapit oleh 2 \"Acidic Shot\". Meningkatkan kekuatan interupsi dari serangan ini sebesar 10%.", 
							"Kerusakan dari ledakan skill ultimate \"Harpoon Breakout\" mendapatkan 15% Damage Output dan akan dikalikan dengan jumlah harpun yang ada (berlaku untuk setiap harpun). Juga meningkatkan Critical Damage milik Archer sebesar 10% untuk setiap harpun yang ada.", 
							"Archer mendapatkan 1 tumpukan Multiplex Arrowhead setelah melakukan 4 serangan dasar, serta mendapatkan 2 tumpukan setelah melakukan gerakan spesial atau ultimate. Menambah tumpukan maksimal dari skill unik ini sebanyak 3.",
							"Menyerang musuh yang berjarak jauh akan meningkatkan ATK milik Archer sebesar 5% selama 5 detik. Bisa ditumpuk sampai 5 tumpukan.", 
							"\"Heavy Shot\" akan melambatkan musuh sebesar 35% selama 3 detik.", 
							"Serangan pertama dari \"Charged Shot\" tahap 3 akan selalu menyebabkan serangan kritis.", 
							"Mendapatkan kekebalan terhadap knockback dan knock-airborne saat menggunakan skill ultimate \"Pinpoint Shooting\". Juga mendapatkan efek \"Auto Dodge\" setiap 2 detik pada serangan jarak dekat musuh saat dalam mode Pinpoint.", 
							"Gerakan spesial \"Supercharged Shot\" akan mengonsumsi 2 tumpukan Multiplex Arrowhead setiap tahap pengisian (atau 1 tumpukan kalau tumpukan Multiplex Arrowhead habis). Ketika \"Supercharged Shot\" terisi sampai tahap 4 atau lebih dan mengenai tanah, akan memicu \"Explosion Shot\" dengan setengah dari basis kerusakan dan radius ledakan aslinya.",
							"Meningkatkan kekuatan dash dari gerakan spesial \"Flick Shot\" sebesar sebesar 50%. Serangannya bisa menembus 2 musuh sekaligus.", 
							"Mengurangi cooldown gerakan spesial sebanyak 1 detik setelah mengalahkan musuh.", 
							"\"Multi Shot\" mendapatkan 3% Attack Speed untuk setiap jumlah anak panah. Juga meningkatkan Attack Speed pada gerakan spesial \"Half-Moon Shot\" sebesar 15% sampai Archer mendarat di tanah.", 
							"Mengurangi basis kerusakan dari gerakan spesial \"Half-Moon Shot\" sebesar 25%, tetapi akan menembak 2 anak panah sekaligus. Setiap anak panah bisa menembus 2 musuh sekaligus.", 
							"Acher bisa melakukan serangan dasar dan serangan turunan sambil bergerak. Menggunakan \"Dash\" saat mengisi daya akan menambah poin pengisian sebesar 25% dan tidak akan menghilangkan poin pengisian."];
				break;
				case 2:
					title = ["Defense Up", "Critical Buildup Up", "Armor Penetration Up", "Team Synergy", "Tactical Dash", "Ordinary Shot V", "Ordinary Shot VI", "Acidic Shot V", "Acidic Shot VI", "Piercing Shot VI", "Charged Shot Phase 3", "Electric Shot", "Retaliate: Soulsnare Shot",
							 "Proper Reaction", "Skilled Spacekeeper", "Airborne Parallel Shot", "Echoes of Cloudbursts", "High-Impact Explosives", "Fast Arrows", "Sharp Arrowhead", "Double Acidic Shot", "A Fruit of Patience", "Shell Perforator",
							 "Ranger Smasher", "Crippling Impact", "Full-Powered Arrow", "Sturdy Stance", "Hypercharged Shot", "Sharp Flick Shot", "Steady Quasher", "Quick Repetition", "Half-Moon Barrage", "Flexible Framework"];
					desc = ["Increases Archer's Defense by 15%.", 
							"Increases Archer's Critical Buildup by 10%.", 
							"Increases Archer's Armor Penetration by 15%.", 
							"Activates Archer's team synergy bonus: if Warrior is also on the team, Archer gains 10% Damage Output.", 
							"Archer can save its current basic attack sequence after dashing. Press the dash button to dash and preserve the current sequence, or hold it to dash and reset the sequence instead.", 
							"Unlock Chain V of Archer's \"Ordinary Shot.\"", 
							"Unlock Chain VI of Archer's \"Ordinary Shot.\".",
							"Unlock Chain V of Archer's Acidic Shot.\"", 
							"Unlock Chain VI of Archer's \"OrdAcidicinary Shot.\"", 
							"Unlock Chain V and VI of Archer's Piercing Shot.\"", 
							"Unlock Phase 3 of Archer's \"Charged Shot\" and Phase 4 of Archer's \"Supercharged Shot\" special move.", 
							"Change Archer's \"Spike Shot\" special move into \"Electric Shot\".", 
							"When Archer dodges an enemy's ultimate, it drag the enemy in and fire a close-range shot, dealing 10% of the enemy's max HP as absolute damage.",
							"Increases the base damage of \"Follow-Up Shot\" derivative skill by 10%.", 
							"Doubles the max number of enemies affected by the \"Spacekeeper\" integral skill.", 
							"\"Parallel Shot\" fires 1 extra arrow when used mid-air. Also boosts its Multi-Projectile Angle Reduction by 30% in the air.", 
							"\"Cloudburst Volley\" ultimate skill fires 2 extra arrows for every 1 level of \"Delay\" adjustment. Each arrow can hit up to 2 enemies at once.", 
							"Increases the explosion radius of \"Explosive Shot\" and \"Electric Shot\" by 35%, or by 75% if they explode on the ground. If \"Electric Shot\" explodes on the ground, it leaves behind an electric field for 5 seconds that deals low acid damage over time and slows all Bots within range by 35%.",
							"Boosts the speed of all Archer arrows by 30%.", 
							"Increases base damage of \"Ordinary Shot,\" \"Double Shot,\" \"Acidic Shot,\" and \"Piercing Shot\" by 20%.", 
							"Changes \"Double Shot\" to deal acid damage if used between two \"Acidic Shot\" attacks. Also increases its interruption power by 10%.", 
							"Burst damage of \"Harpoon Breakout\" ultimate skill gains 15% Damage Output and is multiplied by the number of harpoons present (applies per harpoon). Also increases Archer's Critical Damage by 10% per harpoon.", 
							"Archer gains 1 stack of \"Multiplex Arrowhead\" after 4 basic attacks, and 2 stacks after using a special or ultimate move. The max stack of this unique skill is increased by 3.",
							"Attacking ranged enemies boosts Archer's ATK Scale by 5% for 5 seconds. Stacks up to 5 times.", 
							"\"Heavy Shot\" slows hit enemies by 35% for 3 seconds.", 
							"The first hit of a Phase 3 charged \"Charged Shot\" always lands a critical hit.", 
							"Grants knockback and knock-airborne immunity while using \"Pinpoint Shooting.\" Also triggers an \"Auto Dodge\" against melee attacks every 2 seconds while active.", 
							"\"Supercharged Shot\" special move consumes 2 stacks of \"Multiplex Arrowhead\" per charge phase (or 1 stack if fewer remain). When charged to Phase 4 or higher and hits the ground, it triggers an \"Explosion Shot\" with 50% base damage and explosion radius.",
							"Boosts dash power of \"Flick Shot\" special move by 50%. It can also pierce up to 2 enemies.", 
							"Reduces special move cooldowns by 1 second after defeating an enemy.", 
							"\"Multi Shot\" gains 3% Attack Speed per arrow. Also increases \"Half-Moon Shot\" Attack Speed by 15% until landing.", 
							"Reduces \"Half-Moon Shot\" base damage by 25%, but now fires 2 arrows per shot. Each arrow can pierce up to 2 enemies.", 
							"Archer can now perform basic and derivative attacks while moving. Using \"Dash\" while charging any chargeable attack increases charge points by 25% and no longer resets charge progress."];
				break;
			}
		break;
		case 3:			// Medic
			name = ["m1", "m2", "m3", "m4", "m5", "m6", "m7", "m8", "m9", "m10", "m11", "m12", "m13", 
					"s1", "s2", "s3", "s4", "s5", "s6", "s7", "s8", "s9", "s10", "s11", "s12", "s13", "s14", "s15", "s16", "s17", "s18", "s19", "s20"];
			type = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 
					1, 1, 2, 3, 3, 1, 1, 2, 3, 3, 1, 1, 2, 3, 3, 1, 1, 2, 3, 3];
			active_req = [[], [1], [1], [1], [2, 3, 4], [5], [], [7], [8], [8], [9], [8], [5, 11, 12],
						  [], [14], [15], [16], [17], [], [19], [20], [21], [22], [], [24], [25], [26], [27], [], [29], [30], [31], [32]];
			cost = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 
					2, 2, 3, 4, 4, 2, 2, 3, 4, 4, 2, 2, 3, 4, 4, 2, 2, 3, 4, 4];
			main_talent = 13;
			sprite = [sBotsTalentMedicM, sBotsTalentMedicS];
			switch (global.language) {
				case 1:
					title = ["Defense Up", "Critical Protection Up", "Acid DMG OTP Up", "Buff Power Up", "Team Synergy", "Tactical Dash", "Slash IV", "Slash V", "Spinning Slash II", "Stab II", "Stab III", "Serum Throw", "Retaliate: Abyss Flask",
							 "Decelerator Thrust", "Powerful Blow", "Hyper Spinner", "Bearing Shredder", "Corrosive Dart", "Healing Field", "Smart Healing", "Express Serums", "Pure Medical Kit", "Near-Death Experience",
							 "Stronger Liquid", "Double Injection", "Heal and Corrode", "Lethal Acid Gas", "Gas of Destruction", "Simplified Decoder", "Paired Serums", "Honed Knife", "Micro Booster", "CBC Overbooster"];
					desc = ["Meningkatkan Defense milik Medic sebesar 10%.", 
							"Meningkatkan Critical Protection milik Medic sebesar 15%.", 
							"Meningkatkan Acid Damage Output milik Medic sebesar 10%.", 
							"Meningkatkan Buff Power milik Medic sebesar 10%.", 
							"Mengaktifkan bonus sinergi tim milik Medic: kalau ******** atau ********* berada di dalam tim, Medic mendapatkan 10% Special Move Effectiveness.", 
							"Medic bisa menyimpan rangkaian serangan dasarnya setelah menggunakan dash. Tekan tombol dash untuk melakukan dash dan menyimpan rangkaian serangan dasarnya, atau tahan tombol dash untuk melakukan dash dan memulai ulang rangkaian serangan dasarnya.", 
							"Membuka Rantai IV pada \"Slash\" dan \"Acidic Slash\" milik Medic.",
							"Membuka Rantai V pada \"Slash\" dan \"Acidic Slash\" milik Medic.", 
							"Membuka Rantai II pada \"Spinning Slash\" milik Medic.", 
							"Membuka Rantai II pada \"Stab\" dan \"Drag Out\" milik Medic.", 
							"Membuka Rantai III pada \"Stab\" dan \"Drag Out\" milik Medic.", 
							"Mengganti skill turunan \"Dart Throw\" milik Medic menjadi \"Serum Throw.\"", 
							"Setelah Medic menghindari serangan ultimate musuh, Medic melempar ramuan korosif spesial kepada musuh, menyebabkan kerusakan 10% dari HP maksimal musuh sebagai kerusakan absolut.",
							"\"Thrust\" akan melambatkan musuh sebesar 20% selama 2 detik.", 
							"Meningkatkan CC Power pada \"Blow\" dan \"Uppercut\" sebesar 15%.", 
							"Meningkatkan target maksimal \"Spinning Slash\" sebanyak 1. \"Spinning Slash II\" juga akan menyerang 2 kali untuk setiap perulangan dengan angka ganjil, tetapi basis kerusakannya akan menjadi setengah.", 
							"\"Drag Out\" akan menghilangkan Ignore Interruption musuh sebesar 20% selama 3 detik. Kalau serangan ini digunakan pada urutan terakhir di rangkaian serangan dasar, maka serangan ini akan selalu menyebabkan serangan kritis.", 
							"Mengganti skill menghindar \"Stasis Flask\" menjadi \"Stasis Bolt\". Serangan ini menyebabkan kerusakan asam sedang dari ATK per detik selama 5 detik. Efek slow dari skill ini juga akan tetap berlaku selama durasi dari skill ini.",
							"Ramuan penyembuh yang pecah di tanah akan meninggalkan area penyembuhan kecil yang menyembukan rekan tim di dalam radius selama setengah dari durasi gerakan ini.", 
							"Kalau skill unik \"Restoration Stream\" aktif dan HP milik Bot yang aktif sudah penuh, skill ini akan menyembuhkan Bot yang tidak aktif (memprioritaskan HP terendah).", 
							"Mengurangi cooldown pada skill turunan \"Serum Throw\" sebanyak 2 detik. Saat digunakan, serangan ini juga tidak akan mengulang rangkaian serangan dasar.", 
							"Skill ultimate \"Medical Kit\" akan mengakhiri semua debuff pengguna yang tidak permanen dan meningkatkan Debuff Resistance milik pengguna sebesar 35% selama durasi dari gerakan ini.", 
							"Menambah modul \"Fatalism Prevention\" ke skill integral milik Medic. Modul \"Fatalism Prevention\" ini juga akan membuat Medic melempar 3 \"Healing Potion\" ke Bot yang aktif.",
							"Memperpanjang durasi dari gerakan spesial \"Acid Vial\" sebesar 50%.", 
							"Saat skill unik \"Restoration Stream\" aktif, semua serangan Medic akan diinfus menjadi kerusakan asam.", 
							"Skill integral \"Guardian's Gift\" akan menginfus semua kerusakan pengguna menjadi kerusakan asam. Efek ini juga meningkatkan Acid Damage Output milik pengguna sebesar 15% selama durasi dari skill ini.", 
							"Meningkatkan radius dari skill ultimate \"Corrosive Concentration\" sebesar 50%. Kalau botolnya pecah di udara, skill ini akan mengeluarkan gas asam yang mematikan dalam area berbentuk kerucut yang menyebabkan kerusakan asam mengerikan ke semua Bot.", 
							"Menahan tombol ULTI sekarang bisa membuat Medic untuk memecahkan botol skill ultimate \"Corrosive Concentration\" secara otomatis. Ini akan menambah basis kerusakan dari gas asam ini sebesar 400% dan selalu menyebabkan kerusakan kritis pada musuh.",
							"Mengurangi jumlah kode dari semua perangkat sebanyak 1. Efek ini akan digandakan kalau level dari skill terkait sudah mencapai level 7 atau lebih tinggi.", 
							"Setiap tumpukan skill turunan \"Serum Throw\" yang dikonsumsi akan melempar 2 serum sekaligus.", 
							"Musuh yang terkena gerakan spesial \"Diminisher Knife\" akan menerima kerusakan kecil per detik sampai pisaunya dilepaskan. Kalau pisaunya dilepas secara manual, menyebabkan kerusakan 2x dari basis kerusakannya.", 
							"Kalau skill integral \"Guardian's Gift\" terpicu dan HP milik Bot yang aktif sudah penuh, Medic akan melempar \"Micro Booster\" sebagai gantinya. Booster ini memiliki efek yang sama dengan \"CBC Booster\", tetapi efektivitas dan durasinya akan dibagi dua.", 
							"Menambah penyesuaian \"Overboost\" ke skill ultimate \"CBC Booster\". Kondisi overboost akan mengubah pemulihan HP menjadi pengurangan HP, tetapi akan meningkatkan efektivitas buff sebesar 0.25x dan pengurangan HP sebesar 1% untuk setiap level penyesuaian. Efek ini juga akan mengganti efek talent \"Micro Booster\" menjadi \"Micro Overbooster\"."];
				break;
				case 2:
					title = ["Defense Up", "Critical Protection Up", "Acid DMG OTP Up", "Buff Power Up", "Team Synergy", "Tactical Dash", "Slash IV", "Slash V", "Spinning Slash II", "Stab II", "Stab III", "Serum Throw", "Retaliate: Abyss Flask",
							 "Decelerator Thrust", "Powerful Blow", "Hyper Spinner", "Bearing Shredder", "Corrosive Dart", "Healing Field", "Smart Healing", "Express Serums", "Pure Medical Kit", "Near-Death Experience",
							 "Stronger Liquid", "Double Injection", "Heal and Corrode", "Lethal Acid Gas", "Gas of Destruction", "Simplified Decoder", "Paired Serums", "Honed Knife", "Micro Booster", "CBC Overbooster"];
					desc = ["Increases Medic's Defense by 10%.", 
							"Increases Medic's Critical Protection by 15%.", 
							"Increases Medic's Acid Damage Output by 10%.", 
							"Increases Medic's Buff Power by 10%.", 
							"Activates Medic's team synergy bonus: if ******** or ********* is also on the team, Medic gains 10% Special Move Effectiveness.", 
							"Medic can save its current basic attack sequence after dashing. Press the dash button to dash and preserve the current sequence, or hold it to dash and reset the sequence instead.", 
							"Unlock Chain IV of Medic's \"Slash\" and \"Acidic Slash.\"",
							"Unlock Chain V of Medic's \"Slash\" and \"Acidic Slash.\"", 
							"Unlock Chain II of Medic's Spinning Slash.\"", 
							"Unlock Chain II of Medic's \"Stab\" and \"Drag Out.\"", 
							"Unlock Chain III of Medic's \"Stab\" and \"Drag Out.\"", 
							"Change Medic's \"Dart Throw\" derivative skill into \"Serum Throw\".", 
							"When Medic dodges an enemy's ultimate, it throw a special corrosive potion that deals 10% of the enemy's max HP as absolute damage.",
							"\"Thrust\" slows the enemy by 20% for 2 seconds.", 
							"Boosts CC Power of \"Blow\" and \"Uppercut\" by 15%.", 
							"Increases \"Spinning Slash\" max targets by 1. \"Spinning Slash II\" also strikes twice on every odd repetition, but base damage is halved.", 
							"\"Drag Out\" makes enemies 20% more vulnerable to interruption for 3 seconds. If used as the final hit in the basic attack sequence, it always crits.", 
							"Changes the \"Stasis Flask\" dodge skill to \"Stasis Bolt,\" which deals low acid damage over time. The slowing effect of this skill is also applied.",
							"A broken \"Healing Potion\" leaves behind a small healing field that restores HP to nearby teammates for half the duration of the skill.", 
							"If \"Restoration Stream\" unique skill is active and the active Bots is at full HP, it will instead heal the inactive Bots with the lowest HP percentage.", 
							"Reduces \"Serum Throw\" derivative skill cooldown by 2 seconds. This skill also no longer resets Medic's basic attack sequence when used.", 
							"\"Medical Kit\" ultimate skill clears all non-permanent debuffs and boosts the user's Debuff Resistance by 35% for the skill's duration.", 
							"Adds the \"Fatalism Prevention\" module to Medic's integral skill. When activated, it also throws 3 \"Healing Potions\" to the active Bots.",
							"Extends duration of \"Acid Vial\" special move by 50%.", 
							"If \"Restoration Stream\" unique skill is active, all of Medic's attacks deal acid damage.", 
							"The \"Guardian's Gift\" integral skill infuses all of user's attacks with acid damage and boosts Acid Damage Output by 15% for the duration.", 
							"Expands the radius of \"Corrosive Concentration\" ultimate skill by 50%. If the bottle breaks mid-air, it releases a cone of thick acid gas that deals massive damage to all Bots in its path.", 
							"Holding the ULTI button now lets Medic manually detonate \"Corrosive Concentration\" ultimate skill. This increases its base damage by 400% and guarantees critical hits.",
							"Reduces the code count for all Devices by 1. This reduction is doubled if the linked skill is level 7 or higher.", 
							"Each consumed \"Serum Throw\" derivative skill stack now throws 2 serums at once.", 
							"Enemies hit by \"Diminisher Knife\" special move take low damage over time until the knife is released. When released manually, it deals double base damage.", 
							"If \"Guardian's Gift\" integral skill triggers while the active Bots is at full HP, Medic throws a \"Micro Booster\" instead. It mimics the \"CBC Booster,\" but with halved effectiveness and duration.", 
							"Adds an \"Overboost\" adjustment to CBC Booster. While Overboosted, HP regen becomes HP loss. Each adjustment level adds 1% more HP loss and 0.25x more buff multiplier. \"Micro Booster\" is also converted into a \"Micro Overbooster.\""];
				break;
			}
		break;
	}
	
	var bots_name = db_get_value(db_bots, 2, class_num);
	var db = ds_grid_create(10, array_length(name));
	file_text_decrypt(string_lower(bots_name) + ".txt"); 
	for (var i = 0; i < array_length(name); i++) {
		var unlocked = ini_read_real("talents", name[i], 0);
		var spr = noone;
		if (i < main_talent) {
			spr = sprite[0];
			frame = i;
		} else {
			spr = sprite[1];
			frame = i - main_talent;
		}
		
		var val = [i+1, name[i], unlocked, spr, frame, type[i], title[i], desc[i], active_req[i], cost[i]];
		for (var j = 0; j < array_length(val); j++) {
			ds_grid_set(db, j, i, val[j]);
		}
	}
	file_text_encrypt(string_lower(bots_name) + ".txt"); 
	return db;
}

function db_create_control() {
	var name = [
		"Mouse-Left", "Mouse-Right", "Mouse-Middle", "Mouse-Side-1", "Mouse-Side-2", "Mouse-Wheel-Up", "Mouse-Wheel-Down", 
		"Key-A", "Key-B", "Key-C", "Key-D", "Key-E", "Key-F", "Key-G", "Key-H", "Key-I", "Key-J", "Key-K", "Key-L", "Key-M", 
		"Key-N", "Key-O", "Key-P", "Key-Q", "Key-R", "Key-S", "Key-T", "Key-U", "Key-V", "Key-W", "Key-X", "Key-Y", "Key-Z",
		"Key-1", "Key-2", "Key-3", "Key-4", "Key-5", "Key-6", "Key-7", "Key-8", "Key-9", "Key-0", "Key-Tab", "Key-Backspace",
		"Key-Enter", "Key-Shift", "Key-Ctrl", "Key-Alt", "Key-L-Shift", "Key-L-Ctrl", "Key-L-Alt", "Key-R-Shift", "Key-R-Ctrl", 
		"Key-R-Alt", "Key-Space", "Key-Arrow-Right", "Key-Arrow-Left", "Key-Arrow-Up", "Key-Arrow-Down", "Key-Insert", "Key-Delete", 
		"Key-Home", "Key-End", "Key-Page-Up", "Key-Page-Down", "Key-F1", "Key-F2", "Key-F3", "Key-F4", "Key-F5", "Key-F6", "Key-F7", 
		"Key-F8", "Key-F9", "Key-F10", "Key-F11", "Key-F12", "Numpad-0", "Numpad-1", "Numpad-2", "Numpad-3", "Numpad-4", "Numpad-5", 
		"Numpad-6", "Numpad-7", "Numpad-8", "Numpad-9", "Numpad-Decimal", "Numpad-Plus", "Numpad-Minus", "Numpad-Multiply", "Numpad-Divide"
	];
	var val = [
		mb_left, mb_right, mb_middle, mb_side1, mb_side2, mouse_wheel_up(), mouse_wheel_down(),
		ord("A"), ord("B"), ord("C"), ord("D"), ord("E"), ord("F"), ord("G"), ord("H"), ord("I"), ord("J"), ord("K"), ord("L"), 
		ord("M"), ord("N"), ord("O"), ord("P"), ord("Q"), ord("R"), ord("S"), ord("T"), ord("U"), ord("V"), ord("W"), ord("X"),
		ord("Y"), ord("Z"), ord("1"), ord("2"), ord("3"), ord("4"), ord("5"), ord("6"), ord("7"), ord("8"), ord("9"), ord("0"),
		vk_tab, vk_backspace, vk_enter, vk_shift, vk_control, vk_alt, vk_lshift, vk_lcontrol, vk_lalt, vk_rshift, vk_rcontrol, 
		vk_ralt, vk_space, vk_right, vk_left, vk_up, vk_down, vk_insert, vk_delete, vk_home, vk_end, vk_pageup, vk_pagedown, vk_f1, 
		vk_f2, vk_f3, vk_f4, vk_f5, vk_f6, vk_f7, vk_f8, vk_f9, vk_f10, vk_f11, vk_f12, vk_numpad0, vk_numpad1, vk_numpad2, vk_numpad3, 
		vk_numpad4, vk_numpad5, vk_numpad6, vk_numpad7, vk_numpad8, vk_numpad9, vk_decimal, vk_add, vk_subtract, vk_multiply, vk_divide
	];

	var db = ds_grid_create(3, array_length(name));
	for (var i = 0; i < array_length(name); i++) {
		var to_add = [i+1, name[i], val[i]];
		for (var j = 0; j < array_length(to_add); j++) {
			ds_grid_set(db, j, i, to_add[j]);
		}
	}
	return db;
}

function db_create_settings() {
	// type = 1-general, 2-display, 3-visual, 4-camera, 5-sound, 6-control, 7-gameplay
	// change_type = 1-dropdown, 2-checkbox, 3-slider, 4-input
	var control_type = ["desktop"];
	var type1_cnt = 7;
	var type2_cnt = 3;
	var type3_cnt = 6;
	var type4_cnt = 3;
	var type5_cnt = 2;
	var type6_cnt = 18 * array_length(control_type);
	var type7_cnt = 3;
	var all_type = [type1_cnt, type2_cnt, type3_cnt, type4_cnt, type5_cnt, type6_cnt, type7_cnt];

	var title = [];
	var desc = [];
	var val = [];
	var name = [
		"language", "show_dmg", "show_cc", "show_debuff", "show_spfx", "auto_cloud", "bots_preload",
		"fullscreen", "resolution", "framerate", 
		"vfx", "particle", "shadow", "lighting", "bloom", "fxaa", 
		"cam_dis", "cam_spd", "cam_shake", 
		"sfx", "amb_volume", 
		"con_right", "con_left", "con_up", "con_down", "con_jump", "con_dash", "con_act", "con_wp", "con_batk", 
		"con_derv", "con_spmv", "con_ulti", "con_bot1", "con_bot2", "con_sobj", "con_ui", "con_zoomin", "con_zoomout", 
		"tg_mode", "tg_pref", "w_jump"
	];
	var default_val = [
		2, 2, 2, 2, 2, 0, 0,
		0, 2, 2, 
		1, 2, 1, 0, 0, 0, 
		2, 3, 2, 
		1, 1, 
		11, 8, 30, 26, 56, 47, 13, 24, 1, 2, 12, 25, 34, 35, 27, 67, 6, 7,
		1, 1, 1
	];
	var min_val = [
		1, 0, 0, 0, 0, 0, 0,
		0, 1, 1,
		1, 1, 1, 0, 0, 0,
		1, 1, 0,
		0, 0,
		1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
		1, 1, 0
	];
	var change_type = [
		1, 1, 1, 1, 1, 2, 2,
		1, 1, 1, 
		1, 1, 1, 2, 2, 1, 
		1, 1, 1, 
		3, 3, 
		4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, -4, -4, 
		1, 1, 2
	];

	file_text_decrypt("general.txt");
	switch (global.language) {
		case 1:					// ID
			title = [
				"Bahasa", "Teks Kerusakan", "Teks CC", "Teks Debuff", "Teks Efek", "Cloud Otomatis", "Preload Menu Bots", 
				"Mode Jendela", "Resolusi", "Kecepatan Bingkai", 
				"Efek Visual", "Partikel", "Bayangan", "Pencahayaan", "Bloom", "FXAA", 
				"Jarak", "Kecepatan Pelacakan", "Intensitas Getaran", 
				"Efek Suara", "Suara Ambiens", 
				"Gerak ke Kanan", "Gerak ke Kiri", "Gerak ke Atas", "Gerak ke Bawah", "Lompat", "Dash / Sprint", "Aksi", "Tukar Status Senjata", "Serangan Dasar", 
				"Skill Turunan", "Gerakan Spesial", "Skill Ultimate", "Ganti Bot 1", "Ganti Bot 2", "Tukar Tujuan Stage", "Tukar HUD", "Perbesar", "Perkecil", 
				"Mode Penargetan", "Preferensi Target", "Lompat Side-scroller"
			];
			desc = [
				"Bahasa yang ditampilkan dalam game.", "Ukuran teks yang muncul setelah merusak musuh.", "Ukuran teks yang muncul setelah menimbulkan/terkena crowd-control.", "Ukuran teks yang muncul setelah menimbulkan/terkena debuff.", "Ukuran teks yang muncul setelah memicu efek tertentu.", "Simpan kemajuan secara otomatis ke penyimpanan awan secara otomatis sebelum keluar dari game.", "Muat aset grafis dalam menu Bots saat memulai game. Ini akan lebih membebani VRAM pada kartu grafismu, tetapi ini akan membuat game tidak memuat lagi saat kamu masuk ke menu Bots. Perubahan akan efektif setelah kamu memulai ulang game.",
				"Jenis jendela game.", "Lebar dan tinggi dari jendela game.", "Kecepatan maksimum dari bingkai per detik (FPS). Opsi ini masih eksperimental, harap pilih ke 60 FPS untuk performa yang lebih stabil.", 
				"Kualitas dari semua efek visual secara umum.", "Banyaknya partikel yang digambar dari efek visual.", "Jenis bayangan dari objek yang krusial.", "Pencahayaan dinamis terdahap semua objek.", "Efek bersinar pada objek dan efek visual tertentu.", "Mengurangi garis tepi yang kasar dari tampilan grafis.", 
				"Jarak antara kamera dengan objek yang dilacak.", "Cepat-lambatnya kamera dalam mengikuti objek yang dilacak.", "Kekuatan getaran kamera.", 
				"Volume dari semua efek suara dan suara UI.", "Volume dari semua suara lingkungan.", 
				"", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", 
				"Jenis pelacakan dalam menargetkan musuh.", "Target yang lebih diutamakan dari semua target yang tersedia.\nArah = Menargetkan musuh berdasarkan arah pergerakan Bot.\nKursor = Menargetkan musuh yang terdekat dengan posisi kursor.\nTerdekat = Menargetkan musuh yang terdekat dengan posisi Bot.", "Ganti kontrol \"Gerak ke Atas\" menjadi \"Lompat\" saat dalam mode side-scroller atau platformer."
			];
			val = [
				{list: ["Indonesia", "Inggris"]}, {list: ["Sembunyikan", "Kecil", "Sedang", "Besar"]}, {list: ["Sembunyikan", "Kecil", "Sedang", "Besar"]}, {list: ["Sembunyikan", "Kecil", "Sedang", "Besar"]}, {list: ["Sembunyikan", "Kecil", "Sedang", "Besar"]}, {list: ["Tidak Aktif", "Aktif"]}, {list: ["Tidak Aktif", "Aktif"]}, 
				{list: ["Jendela", "Tidak Berbingkai", "Layar Penuh"]}, {list: ["640 x 360", "1280 x 720", "1366 x 768", "1600 x 900", "1920 x 1080", "2560 x 1440", "Fullscreen"]}, {list: ["30", "60", "75", "90", "120", "144", "165", "240"]}, 
				{list: ["Rendah", "Sedang", "Tinggi"]}, {list: ["Rendah", "Sedang", "Tinggi"]}, {list: ["Sederhana", "Dinamis"]}, {list: ["Tidak Aktif", "Aktif"]}, {list: ["Tidak Aktif", "Aktif"]}, {list: ["Tidak Aktif", "2x", "4x", "8x"]}, 
				{list: ["Dekat", "Sedang", "Jauh"]}, {list: ["Sangat Lambat", "Lambat", "Sedang", "Cepat", "Sangat Cepat", "Instan"]}, {list: ["Tidak Ada", "Lemah", "Sedang", "Kuat"]}, 
				{}, {}, 
				{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, 
				{list: ["Manual", "Otomatis"]}, {list: ["Arah", "Kursor", "Terdekat"]}, {list: ["Tidak Aktif", "Aktif"]}
			];
		break;
		case 2:					// EN
			title = [
				"Language", "Damage Text", "CC Text", "Debuff Text", "Effect Text", "Auto Cloud", "Preload Bots Menu",
				"Window Mode", "Resolution", "Framerate", 
				"Visual Effect", "Particle", "Shadow", "Lighting", "Bloom", "FXAA", 
				"Distance", "Tracking Speed", "Shake Intensity", 
				"Sound Effect", "Ambience Sound", 
				"Move Right", "Move Left", "Move Up", "Move Down", "Jump", "Dash / Sprint", "Action", "Toggle Weapon State", "Basic Attacks", "Derivative Skill", 
				"Special Moves", "Ultimate Skill", "Switch Bots 1", "Switch Bots 2", "Toggle Stage Objectives", "Toggle HUD", "Zoom In", "Zoom Out", 
				"Targeting Mode", "Target Preference", "Side-scroller Jump"
			];
			desc = [
				"The language displayed in the game.", "The text size that appears after damaging an enemy.", "The text size that appears after inflicting/being inflicted by crowd-control.", "The text size that appears after inflicting/being inflicted by a debuff.", "The text size that appears after triggering certain effects.", "Auto-save progress to cloud storage automatically before exiting the game.", "Load graphics assets in the Bots menu when starting the game. This will put more VRAM strain on your graphics card, but it will prevent the game from loading again when you enter the Bots menu. The change will be effective once you restart the game.",
				"Game window type.", "Width and height of the game window.", "Maximum speed of frames per second (FPS). This option is still experimental, please select to 60 FPS for more stable performance.", 
				"The overall quality of all visual effects.", "The number of drawn particles of visual effects.", "The type of shadows of crucial objects.", "The dynamic lighting of all objects.", "The glowing effect of certain objects and visual effects.", "Reduce the rough outlines of the graphic display.", 
				"The distance between the camera and the object being tracked.", "The camera's speed in following the tracked object.", "The strength of camera shake.", 
				"Volume of all sound effects and UI sounds.", "Volume of all environment sounds.", 
				"", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", 
				"Tracking type in targeting enemies.", "Preferred target of all available targets.\nDirection = Targeting enemies based on the direction of the Bots' movement.\nCursor = Targeting enemies closest to the cursor position.\nNearest = Targeting enemies closest to the Bots' position.", "Change the \"Move Up\" control to \"Jump\" when in side-scroller or platformer mode."
			];
			val = [
				{list: ["Indonesian", "English"]}, {list: ["Hide", "Small", "Medium", "Large"]}, {list: ["Hide", "Small", "Medium", "Large"]}, {list: ["Hide", "Small", "Medium", "Large"]}, {list: ["Hide", "Small", "Medium", "Large"]}, {list: ["Inactive", "Active"]}, {list: ["Inactive", "Active"]}, 
				{list: ["Windowed", "Borderless", "Fullscreen"]}, {list: ["640 x 360", "1280 x 720", "1366 x 768", "1600 x 900", "1920 x 1080", "2560 x 1440", "Fullscreen"]}, {list: ["30", "60", "75", "90", "120", "144", "165", "240"]}, 
				{list: ["Low", "Medium", "High"]}, {list: ["Low", "Medium", "High"]}, {list: ["Simple", "Dynamic"]}, {list: ["Inactive", "Active"]}, {list: ["Inactive", "Active"]}, {list: ["Inactive", "2x", "4x", "8x"]}, 
				{list: ["Close", "Medium", "Far"]}, {list: ["Sluggish", "Slow", "Medium", "Fast", "Swift", "Instant"]}, {list: ["None", "Weak", "Medium", "Strong"]}, 
				{}, {}, 
				{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, 
				{list: ["Manual", "Automatic"]}, {list: ["Direction", "Cursor", "Nearest"]}, {list: ["Inactive", "Active"]}
			];
		break;
	}
	var db = ds_grid_create(7, array_length(name));
	for (var i = 0; i < array_length(name); i++) {
		var current_type = 0;
		var type_sum = 0;
		var last_type_sum = 0;
		for (var j = 0; j < array_length(all_type); j++) {
			type_sum += all_type[j];
			if (j > 0) {
				last_type_sum += all_type[j-1];
			}
			if (i + 1 <= type_sum) {
				current_type = j + 1;
				break;
			}
		}
		val[i].min = min_val[i];
		val[i].def = default_val[i];
		val[i].selected = ini_read_real("setting", name[i], default_val[i]);
		if (current_type == 6) {
			val[i].group = ceil((i+1 - last_type_sum)/(type6_cnt/array_length(control_type)));
		}
		
		var to_add = [i+1, current_type, name[i], title[i], desc[i], val[i], change_type[i]];
		for (var j = 0; j < array_length(to_add); j++) {
			ds_grid_set(db, j, i, to_add[j]);
		}
	}
	file_text_encrypt("general.txt");
	return db;
}

function db_create_lab(type, group = 0, class_num = 0) {
	// type = 1-oil_mix, 2-crafting, 3-dismantling, 4-transfer
	// group = num di dropdown kategori
	// class_num buat selain mixing
	var item_num = [];
	var list = [];
	var out_val = [];
	
	switch (type) {
		case 1:		// Mixing
			item_num = [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 
						17, 18, 19, 20, 21, 22, 23, 24, 25, 
						26, 27, 28, 29, 30, 31, 32, 33];
			list = [{num: [7, 6], cnt: [2, 3]}, {num: [8, 6], cnt: [2, 3]}, {num: [9, 6], cnt: [2, 3]}, {num: [7, 8], cnt: [5, 5]}, {num: [7, 8], cnt: [10, 5]}, 
				    {num: [7, 8], cnt: [10, 10]}, {num: [7, 8], cnt: [5, 10]}, {num: [7, 9], cnt: [5, 5]}, {num: [7, 9], cnt: [10, 5]}, {num: [7, 9], cnt: [10, 10]}, 
				    {num: [7, 9], cnt: [5, 10]}, {num: [8, 9], cnt: [5, 5]}, {num: [8, 9], cnt: [10, 5]}, {num: [8, 9], cnt: [10, 10]}, {num: [8, 9], cnt: [5, 10]}, 
				    {num: [11, 7], cnt: [4, 20]}, {num: [13, 8], cnt: [4, 20]}, {num: [15, 7], cnt: [4, 20]}, {num: [17, 9], cnt: [4, 20]}, {num: [19, 8], cnt: [4, 20]}, 
				    {num: [21, 9], cnt: [4, 20]}, {method1: {num: [10, 12], cnt: [3, 3]}, method2: {num: [11, 13], cnt: [3, 3]}}, {method1: {num: [14, 16], cnt: [3, 3]}, method2: {num: [15, 17], cnt: [3, 3]}}, 
					{method1: {num: [18, 20], cnt: [3, 3]}, method2: {num: [19, 21], cnt: [3, 3]}}, {num: [28, 29], cnt: [3, 3]}, {num: [28, 30], cnt: [3, 3]}, {num: [29, 30], cnt: [3, 3]}];
			out_val = [4, 4, 4, 1, 1, 1, 1, 1, 1, 1, 
					   1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 
					   1, 1, 1, 1, 1, 1, 1];
		break;
		case 2:		// Crafting
			switch (group) {
				case 1:		// Materials
					item_num = [35, 36, 37, 39];
					list = [{num: [34], cnt: [5]}, {num: [35], cnt: [4]}, {num: [36], cnt: [3]}, {num: [38], cnt: [3]}];
				break;
			}
		break;
		case 3:		// Dismantling
			switch (group) {
				case 1:		// Materials
					item_num = [35, 36, 37, 39];
					list = [{num: [35], cnt: [1]}, {num: [36], cnt: [1]}, {num: [37], cnt: [1]}, {num: [39], cnt: [1]}];
					out_val = [{num: [34], cnt: [4]}, {num: [35], cnt: [3]}, {num: [36], cnt: [2]}, {num: [38], cnt: [3]}];
				break;
				default:
					var db_bots = db_create_bots();
					var index_start = 0;
					switch (group) {
						case 2:		// Weapons
							var db_wp = db_create_weapon(db_bots, class_num);
							var mold_num = [0, 44, 45, 46];
							index_start = 103;

							for (var i = 0; i < ds_grid_height(db_wp); i++) {
								if (db_get_value(db_wp, 4, i+1) && db_get_value(db_wp, 5, i+1) > 1) {
									var item_num_db = 0;
									for (var j = index_start; j < ds_grid_height(global.db_items); j++) {
										if ((db_get_value(global.db_items, 2, j+1) == class_num+7) && (db_get_value(global.db_items, 12, j+1) == i+1)) {
											item_num_db = j+1;
											break;
										}
									}
									
									if (item_num_db > 0) {
										array_push(item_num, item_num_db);
										array_push(list, {num: [item_num_db], cnt: [1]});
									
										var enhance_cost = 0;
										var to_lv = 1;
										switch (db_get_value(db_wp, 5, i+1)) {
											case 2: 
												array_push(out_val, {num: [2, 34, 48], cnt: [3, 5]}); 
												while (to_lv < db_get_value(db_wp, 6, i+1)) {
													to_lv += 1;
													enhance_cost += (to_lv * 75) - ((to_lv - 1) * 40);
												}
												break;
											case 3: 
												array_push(out_val, {num: [3, 35, 48], cnt: [3, 2]}); 
												while (to_lv < db_get_value(db_wp, 6, i+1)) {
													to_lv += 1;
													enhance_cost += (to_lv * 100) - ((to_lv - 1) * 55) + ((to_lv % 10 == 0) * 1000);
												}
												break;
											case 4: 
												array_push(out_val, {num: [mold_num[class_num], 38, 4, 36, 48], cnt: [1, 5, 7, 3]}); 
												while (to_lv < db_get_value(db_wp, 6, i+1)) {
													to_lv += 1;
													enhance_cost += (to_lv * 135) - ((to_lv - 1) * 50) + ((to_lv % 5 == 0) * 500) + ((to_lv % 10 == 0) * 1000);
												}
												break;
											case 5: 
												array_push(out_val, {num: [mold_num[class_num], 39, 5, 37, 48], cnt: [3, 5, 5, 8]}); 
												while (to_lv < db_get_value(db_wp, 6, i+1)) {
													to_lv += 1;
													enhance_cost += (to_lv * 200) - ((to_lv - 1) * 10) + 250 + ((to_lv % 5 == 0) * 1000);
												}
												break;
										}
										
										if (enhance_cost > 0) {
											array_push(out_val[array_length(out_val)-1].cnt, enhance_cost * 0.8);
										} else {
											array_pop(out_val[array_length(out_val)-1].num);
										}
									}
								}
							}
							ds_grid_destroy(db_wp);
						break;
						default:	// Equipments
							var db_eq = db_create_equipment(db_bots, class_num, group-2);
							var mold_num = [0, 41, 42, 43];
							index_start = 55 + (ds_grid_height(db_eq) * (group-3));

							for (var i = 0; i < ds_grid_height(db_eq); i++) {
								if (db_get_value(db_eq, 6, i+1) && db_get_value(db_eq, 7, i+1) > 1) {
									var item_num_db = 0;
									for (var j = index_start; j < (index_start + ds_grid_height(db_eq)); j++) {
										if ((db_get_value(global.db_items, 2, j+1) == group+2) && (db_get_value(global.db_items, 12, j+1) == i+1)) {
											item_num_db = j+1;
											break;
										}
									}
									
									if (item_num_db > 0) {
										array_push(item_num, item_num_db);
										array_push(list, {num: [item_num_db], cnt: [1]});
									
										var enhance_cost = 0;
										var to_lv = 1;
										switch (db_get_value(db_eq, 7, i+1)) {
											case 2: 
												array_push(out_val, {num: [2, 34, 48], cnt: [3, 5]}); 
												while (to_lv < db_get_value(db_eq, 8, i+1)) {
													to_lv += 1;
													enhance_cost += (to_lv * 45) - ((to_lv - 1) * 25);
												}
												break;
											case 3: 
												array_push(out_val, {num: [3, 35, 48], cnt: [3, 2]}); 
												while (to_lv < db_get_value(db_eq, 8, i+1)) {
													to_lv += 1;
													enhance_cost += (to_lv * 65) - ((to_lv - 1) * 20) + ((to_lv % 10 == 0) * 1000);
												}
												break;
											case 4: 
												array_push(out_val, {num: [mold_num[group-2], 38, 4, 36, 48], cnt: [1, 5, 7, 3]}); 
												while (to_lv < db_get_value(db_eq, 8, i+1)) {
													to_lv += 1;
													enhance_cost += (to_lv * 95) - ((to_lv - 1) * 25) + ((to_lv % 5 == 0) * 500) + ((to_lv % 10 == 0) * 1000);
												}
												break;
											case 5: 
												array_push(out_val, {num: [mold_num[group-2], 39, 5, 37, 48], cnt: [3, 5, 5, 8]}); 
												while (to_lv < db_get_value(db_eq, 8, i+1)) {
													to_lv += 1;
													enhance_cost += (to_lv * 140) - ((to_lv - 1) * 20) + 200 + ((to_lv % 5 == 0) * 1000);
												}
												break;
										}
										
										if (enhance_cost > 0) {
											array_push(out_val[array_length(out_val)-1].cnt, enhance_cost * 0.8);
										} else {
											array_pop(out_val[array_length(out_val)-1].num);
										}
									}
								}
							}
							ds_grid_destroy(db_eq);
					}
			}
		break;
		case 4:		// Transfer
			var db_bots = db_create_bots();
			var db_eq = db_create_equipment(db_bots, class_num, group);
			var index_start = 55 + (ds_grid_height(db_eq) * (group-1));

			for (var i = 0; i < ds_grid_height(db_eq); i++) {
				if (db_get_value(db_eq, 6, i+1) && db_get_value(db_eq, 7, i+1) > 1) {
					var item_num_db = 0;
					for (var j = index_start; j < (index_start + ds_grid_height(db_eq)); j++) {
						if ((db_get_value(global.db_items, 2, j+1) == group+4) && (db_get_value(global.db_items, 12, j+1) == i+1)) {
							item_num_db = j+1;
							break;
						}
					}
					
					if (item_num_db > 0) {
						array_push(item_num, item_num_db);
						array_push(list, {num: [item_num_db], cnt: [1]});
						array_push(out_val, {num: [item_num_db], cnt: [1]});
					}
				}
			}

			ds_grid_destroy(db_eq);
			ds_grid_destroy(db_bots);
		break;
	}
	
	var db = ds_grid_create(6, array_length(item_num));
	for (var i = 0; i < array_length(item_num); i++) {
		var to_add = [i+1, type, item_num[i], class_num, list[i], (array_length(out_val) > 0) ? out_val[i] : 1];
		for (var j = 0; j < array_length(to_add); j++) {
			ds_grid_set(db, j, i, to_add[j]);
		}
		//show_debug_message(to_add)
	}
	return db;
}

function db_create_stage() {
	// tambah stage -> edit stage_unlock_prev kalo butuh
	var type1_cnt = 4;
	var type2_cnt = 5;
	var type8_cnt = 2;
	var all_type = [type1_cnt, type2_cnt, type8_cnt];

	var title = [
		{idn: "Bab I", en: "Chapter I"}, {idn: "Bab II", en: "Chapter II"}, {idn: "Bab III", en: "Chapter III"}, {idn: "Bab IV", en: "Chapter IV"},
		{idn: "Produksi Koin", en: "Coin Production"}, {idn: "Ekstraksi Minyak", en: "Oil Extraction"}, {idn: "Pertambangan Logam", en: "Metal Mining"}, {idn: "Delineasi Cetakan", en: "Mold Delineation"}, {idn: "Pembersihan Kristal", en: "Crystal Cleansing"},
		{idn: "Permainan Bebas", en: "Freeplay"}, {idn: "Latihan", en: "Tutorials"}
	];
	var desc = [
		{idn: "Angin sepoi-sepoi menyapa tanaman di lingkungan ini. Mereka tidak terganggu oleh kehadirannya.", en: "A gentle breeze greets the plants in this environment. They are not bothered by its presence."}, 
		{idn: "Jalan yang halus pun tidak akan terlepas dari bebatuan. Lantas, bagaimana dengan jalan yang rusak?", en: "Even a smooth road will not be free from rocks. So, what about damaged roads?"},
		{idn: "Mengapa lingkungan ini harus mengemban benda asing yang dihempaskan oleh manusia?", en: "Why does this environment have to carry foreign objects thrown up by humans?"},
		{idn: "Hanya karena alam sudah menyediakan berbagai hal untuk manusia, bukan berarti manusia bebas mengekploitasinya sesuka hatinya.", en: "Just because nature has provided various things for humans, it doesn't mean that humans are free to exploit it as they please."},
		{idn: "Sepertinya, hampir semua hal membutuhkan alat tukar.", en: "It seems like almost everything requires a currency."},
		{idn: "Di game ini, minyak adalah tinta berwarna. Apa yang kamu harapkan?", en: "In this game, oil is a colored ink. What do you expect?"},
		{idn: "Logam sangat penting untuk membuat dan meningkatkan sesuatu.", en: "Metal is essential for making and improving things."},
		{idn: "Membuat sesuatu tanpa petunjuk hanya akan membuatmu kebingungan.", en: "Creating something without instructions will only leave you confused."},
		{idn: "Semua orang suka sesuatu yang bersih, bukan?", en: "Everyone loves something clean, right?"},
		{idn: "Siapa yang langsung terjun ke lapangan tanpa melakukan pengujian terlebih dahulu?", en: "Who goes straight into the field without testing first?"},
		{idn: "\"Aku tidak takut pada orang yang bisa memegang 100 senjata sekaligus. Tetapi yang aku takutkan adalah mereka yang bisa menguasai 1 senjata.\"", en: "\"I'm not afraid of people who can hold 100 weapons at once. But what I fear are those who can master 1 weapon.\""}
	];
	var name = [
		"chapter1", "chapter2", "chapter3", "chapter4",
		"weekly_challenge", "weekly_challenge", "weekly_challenge", "weekly_challenge", "weekly_challenge",
		"training", "training"
	];
	var init_unlocked = [
		{type: stage_unlock_level, val: 1}, {type: stage_unlock_prev, val: "chapter1", num: 1}, {type: stage_unlock_prev, val: "chapter2", num: 2}, {type: stage_unlock_prev, val: "chapter3", num: 3}, 
		{type: stage_unlock_level, val: 5}, {type: stage_unlock_level, val: 5}, {type: stage_unlock_level, val: 5}, {type: stage_unlock_level, val: 20}, {type: stage_unlock_level, val: 20}, 
		{type: stage_unlock_level, val: 1}, {type: stage_unlock_level, val: 1}
	];
	var bg_list = [noone, sPlayBG1, sPlayBG2, sPlayBG3];
	var entry = [
		{name: "", max_val: 0, val: 0}, {name: "", max_val: 0, val: 0}, {name: "", max_val: 0, val: 0}, {name: "", max_val: 0, val: 0},
		{name: "cp_ticket", max_val: 15, val: 0}, {name: "oe_ticket", max_val: 10, val: 0}, {name: "mm_ticket", max_val: 10, val: 0}, {name: "md_ticket", max_val: 5, val: 0}, {name: "cc_ticket", max_val: 5, val: 0}, 
		{name: "", max_val: 0, val: 0}, {name: "", max_val: 0, val: 0}
	];

	var db = ds_grid_create(8, array_length(title));
	file_text_decrypt("general.osq");
	for (var i = 0; i < array_length(title); i++) {	
		var current_type = 0;
		var type_sum = 0;
		var last_type_sum = 0;
		for (var j = 0; j < array_length(all_type); j++) {
			type_sum += all_type[j];
			if (j > 0) {
				last_type_sum += all_type[j-1];
			}
			if (i + 1 <= type_sum) {
				current_type = j + 1;
				break;
			}
		}
		
		switch(init_unlocked[i].type) {
			case stage_unlock_level: init_unlocked[i].status = (global.player_level >= init_unlocked[i].val); break;
			case stage_unlock_prev: 
				var stg_cnt = [
					10, 10, 10, 10, 
					3, 3, 3, 6, 3, 
					1, 10
				];
				var stg_name = [
					"stage", "stage", "stage", "stage", 
					"cp", "oe", "mm", "md", "cc",
					"fp", "tr"
				];
				var clear_cnt = 0;
				for (var j = 0; j < stg_cnt[i]; j++) {
					clear_cnt = sign(ini_read_real(init_unlocked[i].val, stg_name[i]+string(j+1), 0));
				}
				init_unlocked[i].status = (clear_cnt == stg_cnt); 
				break;
		}
		if (entry[i].name != "") {
			entry[i].val = clamp(ini_read_real("main", entry[i].name, 0), 0, entry[i].max_val);
		}
		
		var to_add = [i+1, current_type, name[i], title[i], desc[i], init_unlocked[i], bg_list[current_type], entry[i]];
		for (var j = 0; j < array_length(to_add); j++) {
			ds_grid_set(db, j, i, to_add[j]);
		}
		//show_debug_message(to_add)
	}
	file_text_encrypt("general.osq");
	return db;
}

function db_create_stage_list(db_stage, tab, menu) {
	// tab = 1-campaign, 2-resources, 3-training
	// menu = 1-chapterN, 2-cp/oe/mm/md/cc, 3-freeplay/tutorials
	var stage_type = [];
	var name = [];
	var title = [];
	var desc = [];
	var icon = [];
	var diff = [];
	var reward = [];
	var detail = [];
	var all_group = [];
	var title_ovw = [];
	var desc_ovw = [];
	var bg = [];
	var bots = [];
	var tex = [];
	var tg_room = [];
	var parallax = [];
	var last_name = "";

	var lang_str = ["", "idn", "en"];
	switch (tab) {
		case 1:		// Campaign
			//diff = 0;
			name = "stage";
			last_name = name;

			switch (menu) {
				case 1:		// Chapter 1
					tex = "StgChapter1";
					stage_type = [1, 1, 1, 1, 1, 1, 1, 1, 1, 2];
					tg_room = [-4, -4, -4, -4, -4, -4, -4, -4, -4, -4];
					parallax = [{layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, 
								{layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}];
					reward = [
						{first: {num: [[49], [49], [49], [48, 49], [48, 52, 49]], cnt: [[3], [3], [4], [1000, 5], [2000, 1, 5]]}, perm: {num: [47, 48], cnt: [50, 250]}}, 
						{first: {num: [[49], [49], [49], [48, 49], [48, 52, 49]], cnt: [[3], [3], [4], [1250, 5], [2500, 1, 5]]}, perm: {num: [47, 48], cnt: [75, 375]}}, 
						{first: {num: [[49], [49], [49], [48, 49], [48, 52, 49]], cnt: [[3], [3], [4], [1500, 5], [3000, 1, 5]]}, perm: {num: [47, 48], cnt: [100, 500]}}, 
						{first: {num: [[49], [49], [49], [48, 49], [48, 52, 49]], cnt: [[3], [3], [4], [1750, 5], [3500, 1, 5]]}, perm: {num: [47, 48], cnt: [125, 625]}}, 
						{first: {num: [[49], [49], [49], [48, 49], [48, 52, 49]], cnt: [[3], [3], [4], [2000, 5], [4000, 1, 5]]}, perm: {num: [47, 48], cnt: [150, 750]}}, 
						{first: {num: [[49], [49], [49], [48, 49], [48, 52, 49]], cnt: [[3], [3], [4], [2250, 5], [4500, 1, 5]]}, perm: {num: [47, 48], cnt: [175, 875]}}, 
						{first: {num: [[49], [49], [49], [48, 49], [48, 52, 49]], cnt: [[3], [3], [4], [2500, 5], [5000, 1, 5]]}, perm: {num: [47, 48], cnt: [200, 1000]}}, 
						{first: {num: [[49], [49], [49], [48, 49], [48, 52, 49]], cnt: [[3], [3], [4], [2750, 5], [5500, 1, 5]]}, perm: {num: [47, 48], cnt: [225, 1125]}}, 
						{first: {num: [[49], [49], [49], [48, 49], [48, 52, 49]], cnt: [[3], [3], [4], [3000, 5], [6000, 1, 5]]}, perm: {num: [47, 48], cnt: [250, 1250]}}, 
						{first: {num: [[49], [6, 49], [6, 49], [48, 2, 49], [34, 52, 49]], cnt: [[4], [7, 4], [10, 5], [10000, 3, 7], [3, 3, 10]]}, perm: {num: [47, 48, 6], cnt: [325, 2500, 3]}} 
					];
					detail = [
						{code: "01-01", lvl: 1, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_botcntless, desc: -1, val: 1}, tips: switch_lang("Stage yang mudah untuk permainan awal. Tidak ada rekomendasi untuk stage ini.", "Easy stage for early game. No recommendation for this stage.")}, 
						{code: "01-02", lvl: 1, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_botcntless, desc: -1, val: 1}, tips: switch_lang("Stage yang mudah untuk permainan awal. Tidak ada rekomendasi untuk stage ini.", "Easy stage for early game. No recommendation for this stage.")}, 
						{code: "01-03", lvl: 2, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_botcntless, desc: -1, val: 1}, tips: switch_lang("Stage yang mudah untuk permainan awal. Tidak ada rekomendasi untuk stage ini.", "Easy stage for early game. No recommendation for this stage.")}, 
						{code: "01-04", lvl: 2, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 600}, star3: {type: stage_star_bothp, desc: -1, val: 25}, tips: switch_lang("Stage yang mudah untuk permainan awal. Tidak ada rekomendasi untuk stage ini.", "Easy stage for early game. No recommendation for this stage.")}, 
						{code: "01-05", lvl: 2, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 600}, star3: {type: stage_star_bothp, desc: -1, val: 25}, tips: switch_lang("Stage yang mudah untuk permainan awal. Tidak ada rekomendasi untuk stage ini.", "Easy stage for early game. No recommendation for this stage.")}, 
						{code: "01-06", lvl: 3, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 600}, star3: {type: stage_star_bothp, desc: -1, val: 25}, tips: switch_lang("Stage yang mudah untuk permainan awal. Tidak ada rekomendasi untuk stage ini.", "Easy stage for early game. No recommendation for this stage.")}, 
						{code: "01-07", lvl: 3, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 600}, star3: {type: stage_star_bothp, desc: -1, val: 25}, tips: switch_lang("Stage yang mudah untuk permainan awal. Tidak ada rekomendasi untuk stage ini.", "Easy stage for early game. No recommendation for this stage.")}, 
						{code: "01-08", lvl: 4, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 600}, star3: {type: stage_star_bothp, desc: -1, val: 25}, tips: switch_lang("Stage yang mudah untuk permainan awal. Tidak ada rekomendasi untuk stage ini.", "Easy stage for early game. No recommendation for this stage.")}, 
						{code: "01-09", lvl: 4, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 25}, tips: switch_lang("Stage yang mudah untuk permainan awal. Tidak ada rekomendasi untuk stage ini.", "Easy stage for early game. No recommendation for this stage.")}, 
						{code: "01-10", lvl: 5, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 35, tips: switch_lang("Kamu akan bertemu dengan musuh elit untuk pertama kalinya dalam game ini. Mereka memiliki status dan kemampuan yang lebih tinggi daripada musuh normal dan enhanced. Bersiaplah untuk menghadapi mereka.", "You will encounter elite enemies for the first time in this game. They have higher stats and abilities than normal and enhanced enemies. Be prepared to face them.")}} 
					];
					bots = [
						{slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}
					];
					break;
				case 2:		// Chapter 2
					tex = "StgChapter2";
					stage_type = [1, 1, 1, 1, 2, 1, 1, 1, 1, 3];
					tg_room = [-4, -4, -4, -4, -4, -4, -4, -4, -4, -4];
					parallax = [{layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, 
								{layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}];
					reward = [
						{first: {num: [[49], [49], [49], [48, 49], [48, 52, 49]], cnt: [[3], [3], [4], [3250, 5], [6500, 1, 5]]}, perm: {num: [47, 48, 6], cnt: [250, 1250, 2]}}, 
						{first: {num: [[49], [49], [49], [48, 49], [48, 52, 49]], cnt: [[3], [3], [4], [3500, 5], [7000, 1, 5]]}, perm: {num: [47, 48, 6], cnt: [275, 1375, 2]}}, 
						{first: {num: [[49], [49], [49], [48, 49], [48, 52, 49]], cnt: [[3], [3], [4], [3750, 5], [7500, 1, 5]]}, perm: {num: [47, 48, 6], cnt: [300, 1500, 2]}}, 
						{first: {num: [[49], [49], [49], [48, 49], [48, 52, 49]], cnt: [[3], [3], [4], [4000, 5], [8000, 1, 5]]}, perm: {num: [47, 48, 6], cnt: [325, 1625, 2]}}, 
						{first: {num: [[49], [6, 49], [2, 49], [48, 2, 49], [34, 53, 49]], cnt: [[4], [15, 4], [5, 5], [12500, 10, 7], [5, 1, 10]]}, perm: {num: [47, 48, 2], cnt: [400, 3250, 2]}}, 
						{first: {num: [[49], [49], [48, 49], [48, 49], [34, 52, 49]], cnt: [[3], [3], [4250, 4], [8500, 5], [4, 2, 5]]}, perm: {num: [47, 48, 6], cnt: [325, 1625, 4]}}, 
						{first: {num: [[49], [49], [48, 49], [48, 49], [34, 52, 49]], cnt: [[3], [3], [4500, 4], [9000, 5], [4, 2, 5]]}, perm: {num: [47, 48, 6], cnt: [350, 1750, 4]}}, 
						{first: {num: [[49], [49], [48, 49], [48, 49], [34, 52, 49]], cnt: [[3], [3], [4750, 4], [9500, 5], [4, 2, 5]]}, perm: {num: [47, 48, 6], cnt: [375, 1875, 4]}}, 
						{first: {num: [[49], [49], [48, 49], [48, 49], [34, 52, 49]], cnt: [[3], [3], [5000, 4], [10000, 5], [4, 2, 5]]}, perm: {num: [47, 48, 6], cnt: [400, 2000, 4]}}, 
						{first: {num: [[49], [6, 49], [2, 49], [48, 3, 49], [34, 53, 49]], cnt: [[5], [15, 10], [15, 10], [20000, 5, 10], [15, 1, 15]]}, perm: {num: [47, 48, 2, 34], cnt: [600, 5500, 5, 3]}}
					];
					detail = [
						{code: "02-01", lvl: 6, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 25}, tips: switch_lang("", "")}, 
						{code: "02-02", lvl: 7, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 25}, tips: switch_lang("", "")}, 
						{code: "02-03", lvl: 8, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 25}, tips: switch_lang("", "")}, 
						{code: "02-04", lvl: 8, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 25}, tips: switch_lang("", "")}, 
						{code: "02-05", lvl: 9, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_noloss, desc: -1, val: 0}, tips: switch_lang("", "")}, 
						{code: "02-06", lvl: 9, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 25}, tips: switch_lang("", "")}, 
						{code: "02-07", lvl: 10, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 25}, tips: switch_lang("", "")}, 
						{code: "02-08", lvl: 10, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 25}, tips: switch_lang("", "")}, 
						{code: "02-09", lvl: 11, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 25}, tips: switch_lang("", "")}, 
						{code: "02-10", lvl: 12, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_botcntless, desc: -1, val: 2}, tips: switch_lang("", "")}
					];
					bots = [
						{slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}
					];
					break;
				case 3:		// Chapter 3
					tex = "StgChapter3";
					stage_type = [1, 1, 1, 1, 2, 1, 1, 1, 2, 3];
					tg_room = [-4, -4, -4, -4, -4, -4, -4, -4, -4, -4];
					parallax = [{layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, 
								{layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}];
					reward = [
						{first: {num: [[2, 49], [48, 49], [48, 49], [2, 34, 49], [52, 3, 49]], cnt: [[5, 3], [5000, 3], [10000, 4], [10, 4, 5], [3, 3, 5]]}, perm: {num: [47, 48, 6], cnt: [400, 2000, 10]}}, 
						{first: {num: [[2, 49], [48, 49], [48, 49], [2, 34, 49], [52, 3, 49]], cnt: [[5, 3], [5000, 3], [10000, 4], [10, 4, 5], [3, 3, 5]]}, perm: {num: [47, 48, 6], cnt: [425, 2125, 10]}}, 
						{first: {num: [[2, 49], [48, 49], [48, 49], [2, 34, 49], [52, 3, 49]], cnt: [[5, 3], [5000, 3], [10000, 4], [10, 4, 5], [3, 3, 5]]}, perm: {num: [47, 48, 6], cnt: [450, 2250, 10]}}, 
						{first: {num: [[2, 49], [48, 49], [48, 49], [2, 34, 49], [52, 3, 49]], cnt: [[5, 3], [5000, 3], [10000, 4], [10, 4, 5], [3, 3, 5]]}, perm: {num: [47, 48, 6], cnt: [475, 2375, 10]}}, 
						{first: {num: [[2, 49], [48, 49], [34, 49], [34, 3, 49], [3, 53, 49]], cnt: [[15, 4], [15000, 4], [10, 5], [15, 3, 7], [7, 2, 10]]}, perm: {num: [47, 48, 2, 34], cnt: [550, 4750, 4, 2]}}, 
						{first: {num: [[2, 49], [48, 49], [48, 49], [2, 34, 49], [52, 3, 49]], cnt: [[7, 3], [5000, 3], [10000, 4], [15, 5, 5], [4, 3, 5]]}, perm: {num: [47, 48, 6], cnt: [475, 2375, 10]}}, 
						{first: {num: [[2, 49], [48, 49], [48, 49], [2, 34, 49], [52, 3, 49]], cnt: [[7, 3], [5000, 3], [10000, 4], [15, 5, 5], [4, 3, 5]]}, perm: {num: [47, 48, 6], cnt: [500, 2500, 10]}}, 
						{first: {num: [[2, 49], [48, 49], [48, 49], [2, 34, 49], [52, 3, 49]], cnt: [[7, 3], [5000, 3], [10000, 4], [15, 5, 5], [4, 3, 5]]}, perm: {num: [47, 48, 6], cnt: [525, 2625, 10]}}, 
						{first: {num: [[2, 49], [48, 49], [34, 49], [34, 3, 49], [3, 53, 49]], cnt: [[15, 4], [17500, 4], [10, 5], [15, 5, 7], [10, 2, 10]]}, perm: {num: [47, 48, 2, 34], cnt: [600, 5250, 6, 3]}}, 
						{first: {num: [[48, 49], [34, 3, 49], [3, 35, 49], [35, 4, 49], [36, 54, 49]], cnt: [[25000, 5], [15, 7, 10], [15, 5, 10], [10, 3, 10], [3, 2, 15]]}, perm: {num: [47, 48, 3, 35], cnt: [800, 10000, 2, 1]}}
					];
					detail = [
						{code: "03-01", lvl: 12, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 40}, tips: switch_lang("", "")}, 
						{code: "03-02", lvl: 13, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 40}, tips: switch_lang("", "")}, 
						{code: "03-03", lvl: 13, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 40}, tips: switch_lang("", "")}, 
						{code: "03-04", lvl: 14, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 40}, tips: switch_lang("", "")}, 
						{code: "03-05", lvl: 14, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_protect, desc: "", val: {target: undefined, min_val: 0}}, tips: switch_lang("", "")}, 
						{code: "03-06", lvl: 15, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 40}, tips: switch_lang("", "")}, 
						{code: "03-07", lvl: 15, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 40}, tips: switch_lang("", "")}, 
						{code: "03-08", lvl: 16, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 40}, tips: switch_lang("", "")}, 
						{code: "03-09", lvl: 17, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_bothp, desc: -1, val: 40}, tips: switch_lang("", "")}, 
						{code: "03-10", lvl: 18, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 15}, star3: {type: stage_star_noloss, desc: -1, val: 0}, tips: switch_lang("", "")}
					];
					bots = [
						{slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}
					];
					break;
				case 4:		// Chapter 4
					tex = "StgChapter4";
					stage_type = [1, 1, 1, 1, 2, 1, 1, 1, 2, 3];
					tg_room = [-4, -4, -4, -4, -4, -4, -4, -4, -4, -4];
					parallax = [{layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, 
								{layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}];
					reward = [
						{first: {num: [[2, 49], [48, 49], [48, 49], [2, 34, 49], [52, 3, 49]], cnt: [[10, 3], [5000, 3], [10000, 4], [15, 7, 5], [4, 4, 5]]}, perm: {num: [47, 48, 2], cnt: [550, 2750, 3]}}, 
						{first: {num: [[2, 49], [48, 49], [48, 49], [2, 34, 49], [52, 3, 49]], cnt: [[10, 3], [5000, 3], [10000, 4], [15, 7, 5], [4, 4, 5]]}, perm: {num: [47, 48, 2], cnt: [575, 2875, 3]}}, 
						{first: {num: [[2, 49], [48, 49], [48, 49], [2, 34, 49], [52, 3, 49]], cnt: [[10, 3], [5000, 3], [10000, 4], [15, 7, 5], [4, 4, 5]]}, perm: {num: [47, 48, 2], cnt: [600, 3000, 3]}}, 
						{first: {num: [[2, 49], [48, 49], [48, 49], [2, 34, 49], [52, 3, 49]], cnt: [[10, 3], [5000, 3], [10000, 4], [15, 7, 5], [4, 4, 5]]}, perm: {num: [47, 48, 2], cnt: [625, 3125, 3]}}, 
						{first: {num: [[2, 49], [48, 49], [34, 49], [3, 49], [35, 53, 49]], cnt: [[15, 4], [15000, 4], [20, 5], [5, 7], [3, 2, 10]]}, perm: {num: [47, 48, 2, 34], cnt: [700, 6250, 10, 3]}}, 
						{first: {num: [[2, 49], [48, 49], [48, 49], [34, 3, 49], [52, 3, 49]], cnt: [[15, 3], [5000, 3], [10000, 4], [7, 2, 5], [4, 4, 5]]}, perm: {num: [47, 48, 2], cnt: [625, 3125, 3]}}, 
						{first: {num: [[2, 49], [48, 49], [48, 49], [34, 3, 49], [52, 3, 49]], cnt: [[15, 3], [5000, 3], [10000, 4], [7, 2, 5], [4, 4, 5]]}, perm: {num: [47, 48, 2], cnt: [650, 3250, 3]}}, 
						{first: {num: [[2, 49], [48, 49], [48, 49], [34, 3, 49], [52, 3, 49]], cnt: [[15, 3], [5000, 3], [10000, 4], [7, 2, 5], [4, 4, 5]]}, perm: {num: [47, 48, 2], cnt: [675, 3375, 3]}}, 
						{first: {num: [[2, 49], [48, 49], [34, 49], [3, 49], [35, 53, 49]], cnt: [[15, 4], [17500, 4], [20, 5], [7, 7], [5, 3, 10]]}, perm: {num: [47, 48, 2, 34], cnt: [750, 6750, 15, 1]}}, 
						{first: {num: [[48, 49], [34, 3, 49], [3, 35, 49], [35, 4, 49], [36, 54, 49]], cnt: [[25000, 5], [20, 10, 10], [15, 7, 10], [15, 5, 10], [5, 3, 15]]}, perm: {num: [47, 48, 3, 35], cnt: [950, 12500, 3, 2]}}
					];
					detail = [
						{code: "04-01", lvl: 18, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 20}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "04-02", lvl: 19, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 20}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "04-03", lvl: 19, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 20}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "04-04", lvl: 20, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 20}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "04-05", lvl: 20, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 20}, star3: {type: stage_star_destroy, desc: -1, val: {target: undefined, cnt: 3}}, tips: switch_lang("", "")}, 
						{code: "04-06", lvl: 21, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 20}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "04-07", lvl: 22, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 20}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "04-08", lvl: 22, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 20}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "04-09", lvl: 23, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 20}, star3: {type: stage_star_escort, desc: -1, val: {target: undefined, cnt: 1, xrange: [0, 0], yrange: [0, 0]}}, tips: switch_lang("", "")}, 
						{code: "04-10", lvl: 24, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_bothp, desc: -1, val: 20}, star3: {type: stage_star_botnotype, desc: -1, val: 4}, tips: switch_lang("", "")}
					];
					bots = [
						{slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}
					];
					break;
			}
			all_group = [array_length(stage_type)];
			break;
		case 2:		// Resource Nodes
			name = ["", "cp", "oe", "mm", "md", "cc"];

			switch (menu) {
				case 1:		// Coin Production
					tex = "StgChapter2";
					tg_room = [-4, -4, -4];
					parallax = [{layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}];
					stage_type = [1, 1, 2];
					diff = [1, 2, 3];
					all_group = [array_length(stage_type)];
					reward = [
						{first: {num: [48, 49], cnt: [7500, 10]}, perm: {num: [47, 48], cnt: [575, 5000]}}, 
						{first: {num: [48, 49], cnt: [7500, 15]}, perm: {num: [47, 48], cnt: [1400, 7500]}}, 
						{first: {num: [48, 49], cnt: [7500, 25]}, perm: {num: [47, 48], cnt: [3200, 15000]}}
					];
					detail = [
						{code: "CP-01", lvl: 10, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "CP-02", lvl: 20, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "CP-03", lvl: 35, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}
					];
					bots = [
						{slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}
					];
					break;
				case 2:		// Oil Extraction
					tex = "StgChapter2";
					tg_room = [-4, -4, -4];
					parallax = [{layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}];
					stage_type = [1, 1, 2];
					diff = [1, 2, 3];
					all_group = [array_length(stage_type)];
					reward = [
						{first: {num: [3, 49], cnt: [5, 10]}, perm: {num: [47, 2], cnt: [575, 10]}}, 
						{first: {num: [3, 49], cnt: [15, 15]}, perm: {num: [47, 3], cnt: [1400, 5]}}, 
						{first: {num: [4, 49], cnt: [5, 25]}, perm: {num: [47, 3], cnt: [3200, 10]}}
					];
					detail = [
						{code: "OE-01", lvl: 10, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "OE-02", lvl: 20, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "OE-03", lvl: 35, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}
					];
					bots = [
						{slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}
					];
					break;
				case 3:		// Metal Mining
					tex = "StgChapter3";
					tg_room = [-4, -4, -4];
					parallax = [{layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}];
					stage_type = [1, 1, 2];
					diff = [1, 2, 3];
					all_group = [array_length(stage_type)];
					reward = [
						{first: {num: [35, 49], cnt: [5, 10]}, perm: {num: [47, 34], cnt: [575, 10]}}, 
						{first: {num: [35, 49], cnt: [10, 15]}, perm: {num: [47, 34], cnt: [1400, 20]}}, 
						{first: {num: [36, 49], cnt: [5, 25]}, perm: {num: [47, 35], cnt: [3200, 7]}}
					];
					detail = [
						{code: "MM-01", lvl: 10, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "MM-02", lvl: 20, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "MM-03", lvl: 35, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}
					];
					bots = [
						{slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}
					];
					break;
				case 4:		// Mold Delineation
					tex = "StgChapter3";
					tg_room = [-4, -4, -4, -4, -4, -4];
					parallax = [{layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, 
								{layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}];
					stage_type = [2, 2, 2, 2, 2, 2];
					diff = [3, 3, 3, 3, 3, 3];
					all_group = [1, 1, 1, 1, 1, 1];
					reward = [
						{first: {num: [41, 49], cnt: [2, 20]}, perm: {num: [47, 41], cnt: [3200, 1]}}, 
						{first: {num: [42, 49], cnt: [2, 20]}, perm: {num: [47, 42], cnt: [3200, 1]}}, 
						{first: {num: [43, 49], cnt: [2, 20]}, perm: {num: [47, 43], cnt: [3200, 1]}},
						{first: {num: [44, 49], cnt: [2, 20]}, perm: {num: [47, 44], cnt: [3200, 1]}},
						{first: {num: [45, 49], cnt: [2, 20]}, perm: {num: [47, 45], cnt: [3200, 1]}},
						{first: {num: [46, 49], cnt: [2, 20]}, perm: {num: [47, 46], cnt: [3200, 1]}}
					];
					detail = [
						{code: "MD-01", lvl: 35, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "MD-02", lvl: 35, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "MD-03", lvl: 35, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")},
						{code: "MD-04", lvl: 35, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_botonlyclass, desc: -1, val: 1}, tips: switch_lang("", "")},
						{code: "MD-05", lvl: 35, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_botonlyclass, desc: -1, val: 2}, tips: switch_lang("", "")},
						{code: "MD-06", lvl: 35, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_botonlyclass, desc: -1, val: 3}, tips: switch_lang("", "")}
					];
					title_ovw = [ "",
						switch_lang("Cetakan Core", "Core Mold"), 
						switch_lang("Cetakan Bearing", "Bearing Mold"), 
						switch_lang("Cetakan Crust", "Crust Mold"), 
						switch_lang("Cetakan Pedang", "Sword Mold"), 
						switch_lang("Cetakan Busur", "Bow Mold"), 
						switch_lang("Cetakan Pisau", "Knife Mold"), 
					];
					desc_ovw = [ "",
						switch_lang("Kekuatan inti akan meningkatkan kekuatan seseorang secara menyeluruh", "Core strength will increase one's overall strength"),
						switch_lang("Apa gunanya kekuatan yang besar kalau seseorang tidak hidup?", "What good is great power if one is not alive?"),
						switch_lang("Cangkang yang keras akan melindungi organ yang lemah", "A hard shell will protect weak organs"),
						switch_lang("Pedang yang tajam tidak hanya bergantung pada kekuatan ayunan, tetapi juga pada keahlian penggunanya", "A sharp sword depends not only on the power of the swing, but also on the skill of the wielder"),
						switch_lang("Panah yang dilepaskan tanpa arah hanya akan tersesat dalam angin", "An arrow released without direction will only get lost in the wind"),
						switch_lang("Pisau yang kecil sekalipun dapat menjadi alat yang mematikan", "Even a small knife can be a deadly tool")
					];
					bots = [
						{slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}
					];
					break;
				case 5:		// Crystal Cleansing
					tex = "StgChapter3";
					tg_room = [-4, -4, -4];
					parallax = [{layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}];
					stage_type = [1, 2, 2];
					diff = [2, 3, 4];
					all_group = [array_length(stage_type)];
					reward = [
						{first: {num: [49], cnt: [10]}, perm: {num: [47, 49], cnt: [575, 5]}}, 
						{first: {num: [49], cnt: [15]}, perm: {num: [47, 49], cnt: [1900, 10]}}, 
						{first: {num: [49], cnt: [25]}, perm: {num: [47, 49], cnt: [3200, 15]}}
					];
					detail = [
						{code: "CC-01", lvl: 10, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "CC-02", lvl: 20, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}, 
						{code: "CC-03", lvl: 35, desc: "", star1: {type: stage_star_survive, desc: -1, val: 1}, star2: {type: stage_star_intime, desc: -1, val: 300}, star3: {type: stage_star_bothp, desc: -1, val: 35}, tips: switch_lang("", "")}
					];
					bots = [
						{slot: [0, 0, 0]}, {slot: [0, 0, 0]}, {slot: [0, 0, 0]}
					];
					break;
			}
			break;
		case 3:		// Training Grounds
			name = ["", "fp", "tr"];

			switch (menu) {
				case 1:		// Freeplay
					tex = "StgTraining";
					tg_room = [rStgTraining];
					parallax = [{layer_name: ["BgAcc", "BgAccShdS", "BgTop", "BgTile"], spd_x: [0.1, 0.1, 0.1, 0.1], spd_y: [1, 1, 1, 1]}];
					stage_type = [1];
					diff = [0];
					all_group = [array_length(stage_type)];
					reward = [{first: -1, perm: -1}];
					detail = [{code: "TR-FP", lvl: 1, desc: "", star1: -1, star2: -1, star3: -1, tips: switch_lang("", "")}];
					bots = [{slot: [0, 0, 0]}];
					break;
				case 2:		// Tutorials
					tex = "StgTraining";
					tg_room = [rStgTraining, rStgTraining, rStgTraining, rStgTraining, rStgTraining, 
							   rStgTraining, rStgTraining, rStgTraining, rStgTraining, rStgTraining];
					parallax = [{layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, 
								{layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}, {layer_name: [], spd_x: [], spd_y: []}];
					stage_type = [1, 1, 1, 1, 1, 1, 3, 1, 1, 1];
					diff = [0, 0, 2, 0, 0, 0, 4, 2, 2, 2];
					all_group = [3, 4, 3];
					reward = [
						{first: -1, perm: -1}, 
						{first: -1, perm: -1}, 
						{first: {num: [48, 52, 53], cnt: [5000, 7, 3]}, perm: -1}, 
						{first: -1, perm: -1}, 
						{first: -1, perm: -1}, 
						{first: -1, perm: -1}, 
						{first: {num: [48, 53, 54], cnt: [15000, 15, 5]}, perm: -1}, 
						{first: {num: [48, 53], cnt: [5000, 3]}, perm: -1}, 
						{first: {num: [48, 53], cnt: [5000, 3]}, perm: -1}, 
						{first: {num: [48, 53], cnt: [5000, 3]}, perm: -1}
					];
					detail = [
						{code: "TR-B1", lvl: 1, desc: "", star1: -1, star2: -1, star3: -1, tips: switch_lang("", "")},
						{code: "TR-B2", lvl: 1, desc: "", star1: -1, star2: -1, star3: -1, tips: switch_lang("", "")},
						{code: "TR-B3", lvl: 1, desc: "", star1: -1, star2: -1, star3: -1, tips: switch_lang("", "")},
						{code: "TR-A1", lvl: 1, desc: "", star1: -1, star2: -1, star3: -1, tips: switch_lang("", "")},
						{code: "TR-A2", lvl: 1, desc: "", star1: -1, star2: -1, star3: -1, tips: switch_lang("", "")},
						{code: "TR-A3", lvl: 1, desc: "", star1: -1, star2: -1, star3: -1, tips: switch_lang("", "")},
						{code: "TR-A4", lvl: 1, desc: "", star1: -1, star2: -1, star3: -1, tips: switch_lang("", "")},
						{code: "TR-C1", lvl: 1, desc: "", star1: -1, star2: -1, star3: -1, tips: switch_lang("", "")},
						{code: "TR-C2", lvl: 1, desc: "", star1: -1, star2: -1, star3: -1, tips: switch_lang("", "")},
						{code: "TR-C3", lvl: 1, desc: "", star1: -1, star2: -1, star3: -1, tips: switch_lang("", "")}
					];
					title_ovw = [ "",
						switch_lang("Pemula", "Beginner"), 
						switch_lang("Lanjutan", "Advanced"), 
						switch_lang("Kelas", "Class")
					];
					desc_ovw = [ "",
						switch_lang("Tidak ada yang langsung menjadi ahli saat seseorang pertama kali melakukan suatu hal", "No one is an expert the first time they do something."),
						switch_lang("Tamparan berbagai pengalaman akan membuatmu menjadi lebih baik dari sebelumnya", "The slap of various experiences will make you better than before"),
						switch_lang("Jangan sampai menggunakan pancingan untuk memotong ikan", "Don't use a fishing rod to cut a fish")
					];
					bg = [ "",
						{spr: sPlayBG3, frame: 1},
						{spr: sPlayBG3, frame: 1},
						{spr: sPlayBG3, frame: 2}
					];
					bots = [
						{slot: [1]}, {slot: [2]}, {slot: [1]}, {slot: [2]}, {slot: [1]}, {slot: [1, 2]}, {slot: [1, 2, 3]}, {slot: [1]}, {slot: [2]}, {slot: [3]}
					];
					break;
			}
			break;
	}

	var db = ds_grid_create(19, array_length(stage_type));
	file_text_decrypt("general.osq");
	for (var i = 0; i < array_length(stage_type); i++) {
		var current_group = 0;
		var group_sum = 0;
		var last_group_sum = 0;
		for (var j = 0; j < array_length(all_group); j++) {
			group_sum += all_group[j];
			if (j > 0) {
				last_group_sum += all_group[j-1];
			}
			if (i + 1 <= group_sum) {
				current_group = j + 1;
				break;
			}
		}
		
		var rel_menu = ((tab == 1) ? menu : db_find_val(db_stage, 2, tab));
		var data = db_get_row(db_stage, db_type_stg, ((tab == 1) ? menu : rel_menu + menu-1));
		title = string($"{data.title[$ lang_str[global.language]]}");
		detail[i].title = string($"{title}: {detail[i].code}");
		desc = (array_length(all_group) == 1) ? string($"{data.desc[$ lang_str[global.language]]}") : desc_ovw[current_group];
		
		var current_name = (is_string(name) ? string($"{name}{i+1}") : string($"{name[menu]}{i+1}"));
		var stage_lost = ini_read_real(data.name, current_name + "_lost", 0);
		var stage_star = ini_read_real(data.name, current_name + "_star", 0);
		var stage_clear = ini_read_real(data.name, current_name + "_clear", -1);
		if ((stage_clear == -1 && i+1-last_group_sum == 1) || (stage_clear >= 1 && stage_star == 0)) {
			stage_clear = 0;
		}
		
		switch (tab) {
			case 1:		// Campaign
				icon = {spr: sPlayIconsMenu, frame: rel_menu};
				diff = ini_read_real(data.name, current_name + "_diff", 0);
				break;
			case 2:		// Resource Nodes
				var icon_offset = 3;
				icon = ((menu != 4) ? {spr: sPlayIconsMenu, frame: rel_menu + menu-1} : {spr: sPlayIconsSubmenu, frame: current_group + icon_offset});
				title = ((menu != 4) ? title : title_ovw[current_group]);
				break;
			case 3:		// Training
				icon = ((menu == 1) ? {spr: sPlayIconsMenu, frame: rel_menu + menu-1} : {spr: sPlayIconsSubmenu, frame: current_group});
				title = ((menu != 2) ? title : title_ovw[current_group]);
				break;
		}
		
		var to_add = [i+1, i+1-last_group_sum, current_group, stage_type[i], (is_array(name) ? name[menu] : name), (is_array(title) ? title[i] : title), (is_array(desc) ? desc[i] : desc), (is_array(icon) ? icon[i] : icon), stage_lost, stage_star, stage_clear, (is_array(diff) ? diff[i] : diff), 
					  reward[i], detail[i], ((array_length(bg) == 0) ? {spr: data.bg, frame: data.num - db_find_val(db_stage, 2, data.type)} : bg[current_group]), bots[i], (is_array(tex) ? tex[i] : [tex]), tg_room[i], parallax[i]];
		for (var j = 0; j < array_length(to_add); j++) {
			ds_grid_set(db, j, i, to_add[j]);
		}
		// show_debug_message($"baris {i+1} ({current_name}) = {to_add}")

		delete data;
		if (is_string(name)) {
			name = last_name;
		}
	}
	file_text_encrypt("general.osq");
	return db;
}


// DB Constants
#macro db_type_bots 1
#macro db_type_weapon 2
#macro db_type_equipment 3
#macro db_type_items 4
#macro db_type_talents 5
#macro db_type_sklist 6
#macro db_type_skdtl 7
#macro db_type_setting 8
#macro db_type_lab 9
#macro db_type_stg 10
#macro db_type_stglist 11

#macro setting_text_dmg 2
#macro setting_text_cc 3
#macro setting_text_dbf 4
#macro setting_text_fx 5
#macro setting_preload_bots 7
#macro setting_window 8
#macro setting_resolution 9
#macro setting_framerate 10
#macro setting_vfx 11
#macro setting_particle 12
#macro setting_shadow 13
#macro setting_lighting 14
#macro setting_bloom 15
#macro setting_aa 16
#macro setting_cam_dis 17
#macro setting_cam_spd 18
#macro setting_cam_shake 19
#macro setting_sfx 20
#macro setting_amb 21
#macro setting_move_right 22
#macro setting_move_left 23
#macro setting_move_up 24
#macro setting_move_down 25
#macro setting_jump 26
#macro setting_dash 27
#macro setting_action 28
#macro setting_wp_state 29
#macro setting_batk 30
#macro setting_derv 31
#macro setting_spmv 32
#macro setting_ulti 33
#macro setting_switch1 34
#macro setting_switch2 35
#macro setting_stg_obj 36
#macro setting_stg_ui 37
#macro setting_zoomin 38
#macro setting_zoomout 39
#macro setting_target_mode 40
#macro setting_target_pref 41
#macro setting_sidesc_jump 42

#macro stage_tab_campaign 1
#macro stage_tab_resources 2
#macro stage_tab_training 3

#macro stage_unlock_level 1
#macro stage_unlock_prev 2
#macro stage_unlock_custom 3

#macro stage_star_survive 1				// (int) Number of Bots survived 				[Default: 1]
#macro stage_star_intime 2				// (int) Completed under time limit (sec)
#macro stage_star_overtime 3			// (int) Completed over time limit (sec)
#macro stage_star_noloss 4				// (int) No Bots lost 							[Default: 0]
#macro stage_star_nodmg 5				// (int) No damage taken by Bots 				[Default: 0]
#macro stage_star_noskill 6				// (int) No active skill used by any Bots 		[1-batk, 2-derv, 3-spmv, 4-ulti, 7-ext]
#macro stage_star_botcntless 7			// (int) Number of Bots deployed (<=)			[Default: 1]
#macro stage_star_botcntmore 8			// (int) Number of Bots deployed (>=)			[Default: 1]
#macro stage_star_bothp 9				// (int) Average HP of all Bots
#macro stage_star_botclass 10			// ([int]) Bots Class deployed
#macro stage_star_botnoclass 11			// ([int]) Bots Class not deployed
#macro stage_star_botonlyclass 12		// (int) Bots Class that is only used
#macro stage_star_bottype 13			// ([int]) Roles of Bots deployed
#macro stage_star_botnotype 14			// ([int]) Roles of Bots not deployed			[1-all_rounder, 2-attacker, 3-defender, 4-healer, 5-supporter, 6-specialist]
#macro stage_star_botonlytype 15		// (int) Roles of Bots that is only used
#macro stage_star_enemydefeat 16		// (int) Number of enemies defeated
#macro stage_star_enemytarget 17		// ({target: asset_obj, cnt: int, current: int}) Targetted enemy to be defeated
#macro stage_star_protect 18			// ({target: asset_obj, min_val: int}) Object to be protected
#macro stage_star_destroy 19			// ({target: asset_obj, cnt: int}) Object to be destroyed
#macro stage_star_retrieve 20			// ({target: asset_obj, cnt: int}) Object to be retrieved
#macro stage_star_capture 21			// ({target: asset_obj, cnt: int, xrange: array, yrange: array}) Object to be captured and brought to a certain location
#macro stage_star_occupy 22				// ({xrange: array, yrange: array, time: int}) Location to be occupied and held for a certain time
#macro stage_star_escort 23				// ({target: asset_obj, cnt: int, xrange: array, yrange: array}) Object to be escorted to a certain location
#macro stage_star_rescue 24				// ({target: asset_obj, cnt: int, xrange: array, yrange: array}) Object to be rescued and brought to a certain location
