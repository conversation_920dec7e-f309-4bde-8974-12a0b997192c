function data_stg_get_star_desc(stg_data, star_num) {
	// stg_data = struct stage details
	
	var result = "";
	
	switch (stg_data.detail[$ string($"star{star_num}")].type) {
		case stage_star_survive:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang("Bertahan hidup", "Survive");
			break;
		case stage_star_intime:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Selesai dalam {stg_data.detail[$ string($"star{star_num}")].val / 60} menit"), string($"Cleared within {stg_data.detail[$ string($"star{star_num}")].val / 60} minutes"));
			break;
		case stage_star_overtime:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Selesai setelah {stg_data.detail[$ string($"star{star_num}")].val / 60} menit"), string($"Cleared after {stg_data.detail[$ string($"star{star_num}")].val / 60} minutes"));
			break;
		case stage_star_noloss:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(((stg_data.detail[$ string($"star{star_num}")].val == 0) ? "Tidak ada rekan tim yang dikalahkan" : string($"Tidak kehilangan lebih dari {stg_data.detail[$ string($"star{star_num}")].val} rekan tim")), 
									((stg_data.detail[$ string($"star{star_num}")].val == 0) ? "No teammates were defeated" : string($"Not losing more than {stg_data.detail[$ string($"star{star_num}")].val} teammates")));
			break;
		case stage_star_nodmg:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(((stg_data.detail[$ string($"star{star_num}")].val == 0) ? "Tidak menerima kerusakan HP dari musuh" : ("Tidak menerima kerusakan HP lebih dari " + formatted_number(stg_data.detail[$ string($"star{star_num}")].val))), 
									((stg_data.detail[$ string($"star{star_num}")].val == 0) ? "Take no HP damage from enemies" : ("Not getting more than " + formatted_number(stg_data.detail[$ string($"star{star_num}")].val) + " HP damage")));
			break;
		case stage_star_noskill:
			var skill_title = ["", switch_lang("Serangan Dasar", "Basic Attacks"), switch_lang("Skill Turunan", "Derivative Skills"), switch_lang("Gerakan Spesial", "Special Moves"), switch_lang("Skill Ultimate", "Ultimate Skills"), "", "", switch_lang("Skill Ekstra", "Extra Skills")];
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Tidak menggunakan {skill_title[stg_data.detail[$ string($"star{star_num}")].val]} apapun"), string($"Not using any {skill_title[stg_data.detail[$ string($"star{star_num}")].val]}"));
			break;
		case stage_star_botcntless:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(((stg_data.detail[$ string($"star{star_num}")].val == 1) ? "Hanya menggunakan 1 kelas Bot" : string($"Tidak menggunakan lebih dari {stg_data.detail[$ string($"star{star_num}")].val} kelas Bot")), 
									((stg_data.detail[$ string($"star{star_num}")].val == 1) ? "Only used 1 Bots class" : string($"Not using more than {stg_data.detail[$ string($"star{star_num}")].val} Bots classes")));
			break;
		case stage_star_botcntmore:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(((stg_data.detail[$ string($"star{star_num}")].val == 1) ? "Menggunakan setidaknya 1 kelas Bot" : string($"Menggunakan setidaknya {stg_data.detail[$ string($"star{star_num}")].val} kelas Bot")), 
									((stg_data.detail[$ string($"star{star_num}")].val == 1) ? "Have used at least 1 Bots class" : string($"Use at least {stg_data.detail[$ string($"star{star_num}")].val} Bots classes")));
			break;
		case stage_star_bothp:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Rata-rata HP rekan tim {stg_data.detail[$ string($"star{star_num}")].val}% atau lebih tinggi"), string($"Average HP of teammates is {stg_data.detail[$ string($"star{star_num}")].val}% or higher"));
			break;
		case stage_star_botclass:
			var class_arr = ["", switch_lang(string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]} berada di dalam tim"), string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]} is in the team")), 
									switch_lang(string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]} dan {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]} berada di dalam tim"), string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]} and {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]} are in the team")), 
									switch_lang(string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]}, {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]}, dan {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]} berada di dalam tim"), string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]}, {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]}, and {class_name[stg_data.detail[$ string($"star{star_num}")].val[2]]} are in the team"))];
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : class_arr[array_length(stg_data.detail[$ string($"star{star_num}")].val)];
			break;
		case stage_star_botnoclass:
			var class_arr = ["", switch_lang(string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]} tidak berada di dalam tim"), string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]} isn't in the team")), 
									switch_lang(string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]} dan {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]} tidak berada di dalam tim"), string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]} and {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]} aren't in the team")), 
									switch_lang(string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]}, {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]}, dan {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]} tidak berada di dalam tim"), string($"{class_name[stg_data.detail[$ string($"star{star_num}")].val[0]]}, {class_name[stg_data.detail[$ string($"star{star_num}")].val[1]]}, and {class_name[stg_data.detail[$ string($"star{star_num}")].val[2]]} aren't in the team"))];
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : class_arr[array_length(stg_data.detail[$ string($"star{star_num}")].val)];
			break;
		case stage_star_botonlyclass:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Selesai hanya dengan kelas {stg_data.detail[$ string($"star{star_num}")].val}"), string($"Cleared only by {stg_data.detail[$ string($"star{star_num}")].val} class"));
			break;
		case stage_star_bottype:
			var title_arr = ["", switch_lang("Serba Bisa", "All-Rounder"), switch_lang("Penyerang", "Attacker"), switch_lang("Penjaga", "Defender"), switch_lang("Penyembuh", "Healer"), switch_lang("Pendukung", "Supporter"), switch_lang("Spesialis", "Specialist")];
			var class_arr = ["", switch_lang(string($"Bot dengan peran {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]} berada di dalam tim"), string($"Bots with {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]} role is in the team")), 
									switch_lang(string($"Bot dengan peran {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]} dan {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]} berada di dalam tim"), string($"Bots with {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]} and {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]} role are in the team")), 
									switch_lang(string($"Bot dengan peran {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]}, {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]}, dan {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]} berada di dalam tim"), string($"Bots with {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]}, {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]}, and {title_arr[stg_data.detail[$ string($"star{star_num}")].val[2]]} role are in the team"))];
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : class_arr[array_length(stg_data.detail[$ string($"star{star_num}")].val)];
			break;
		case stage_star_botnotype:
			var title_arr = ["", switch_lang("Serba Bisa", "All-Rounder"), switch_lang("Penyerang", "Attacker"), switch_lang("Penjaga", "Defender"), switch_lang("Penyembuh", "Healer"), switch_lang("Pendukung", "Supporter"), switch_lang("Spesialis", "Specialist")];
			var class_arr = ["", switch_lang(string($"Bot dengan peran {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]} tidak berada di dalam tim"), string($"Bots with {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]} role isn't in the team")), 
									switch_lang(string($"Bot dengan peran {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]} dan {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]} tidak berada di dalam tim"), string($"Bots with {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]} and {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]} role aren't in the team")), 
									switch_lang(string($"Bot dengan peran {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]}, {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]}, dan {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]} tidak berada di dalam tim"), string($"Bots with {title_arr[stg_data.detail[$ string($"star{star_num}")].val[0]]}, {title_arr[stg_data.detail[$ string($"star{star_num}")].val[1]]}, and {title_arr[stg_data.detail[$ string($"star{star_num}")].val[2]]} role aren't in the team"))];
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : class_arr[array_length(stg_data.detail[$ string($"star{star_num}")].val)];
			break;
		case stage_star_botonlytype:
			var title_arr = ["", switch_lang("Serba Bisa", "All-Rounder"), switch_lang("Penyerang", "Attacker"), switch_lang("Penjaga", "Defender"), switch_lang("Penyembuh", "Healer"), switch_lang("Pendukung", "Supporter"), switch_lang("Spesialis", "Specialist")];
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Selesai hanya dengan Bot yang berperan {title_arr[stg_data.detail[$ string($"star{star_num}")].val]}"), string($"Cleared only by Bots with {title_arr[stg_data.detail[$ string($"star{star_num}")].val]} role"));
			break;
		case stage_star_enemydefeat:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Selesai setelah mengalahkan {stg_data.detail[$ string($"star{star_num}")].val} musuh"), string($"Cleared after defeating {stg_data.detail[$ string($"star{star_num}")].val} enemies"));
			break;
		case stage_star_enemytarget:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Selesai setelah mengalahkan target"), string($"Cleared after defeating the target"));
			break;
		case stage_star_protect:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Lindungi target dari serangan musuh"), string($"Protect the target from enemy attacks"));
			break;
		case stage_star_destroy:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Hancurkan semua target"), string($"Destroy all targets"));
			break;
		case stage_star_retrieve:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Ambil semua target"), string($"Retrieve all targets"));
			break;
		case stage_star_capture:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Ambil target dan membawanya ke lokasi tujuan"), string($"Pick up the target and take it to the destination"));
			break;
		case stage_star_occupy:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Ambil alih dan pertahankan lokasi tujuan"), string($"Take over and defend the destination"));
			break;
		case stage_star_escort:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Kawal dan lindungi target sampai ke lokasi tujuan"), string($"Escort and protect the target to its destination"));
			break;
		case stage_star_rescue:
			result = (stg_data.detail[$ string($"star{star_num}")].desc != -1) ? stg_data.detail[$ string($"star{star_num}")].desc : 
						switch_lang(string($"Selamatkan target dan membawanya ke lokasi tujuan"), string($"Rescue the target and escort it to the destination"));
			break;
	}
	
	return result;
}

