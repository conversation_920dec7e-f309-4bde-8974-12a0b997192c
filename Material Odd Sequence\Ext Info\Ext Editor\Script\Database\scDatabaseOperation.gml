function db_find_val(db, find_col, find_val, return_col = 1, start_index = 0, max_index = 0) {
	// find_col & return_col mulai dari 1
	var result = 0;
	
	if (start_index >= 0) {
		for (var i = start_index; i < ((max_index == 0) ? ds_grid_height(db) : max_index); i++) {
			if (db_get_value(db, find_col, i+1) == find_val) {
				result = (return_col == 1) ? i+1 : ds_grid_get(db, return_col - 1, i);
				break;
			}
		}
	} else if (start_index == -1) {
		for (var i = ds_grid_height(db)-1; i >= ((max_index == 0) ? 0 : ds_grid_height(db)-max_index); i--) {
			if (db_get_value(db, find_col, i+1) == find_val) {
				result = (return_col == 1) ? i+1 : ds_grid_get(db, return_col - 1, i);
				break;
			}
		}
	}
	
	return result;
}

function db_find_val_func(db, func, return_col = 1, start_index = 0, max_index = 0) {
	// find_col & return_col mulai dari 1
	var result = 0;

	if (start_index >= 0) {
		for (var i = start_index; i < ((max_index == 0) ? ds_grid_height(db) : max_index); i++) {
			if (func(i+1)) {
				result = (return_col == 1) ? i+1 : ds_grid_get(db, return_col - 1, i);
				break;
			}
		}
	} else if (start_index == -1) {
		for (var i = ds_grid_height(db)-1; i >= ((max_index == 0) ? 0 : ds_grid_height(db)-max_index); i--) {
			if (func(i+1)) {
				result = (return_col == 1) ? i+1 : ds_grid_get(db, return_col - 1, i);
				break;
			}
		}
	}

	return result;
}

function db_sort(db, num_arr, col_num, asc = true, return_col = 0) {
	// num_arr = array num yang mau diurutkan
	// col_num mulai dari 1, buat patokan perbandingan nilai
	var result = num_arr;
	var val_arr = [];
	
	for (var i = 0; i < array_length(num_arr); i++) {
		array_push(val_arr, db_get_value(db, col_num, num_arr[i]));
	}
	
	for (var i = 0; i < array_length(num_arr); i++) {
		for (var j = 0; j < array_length(num_arr) - 1; j++) {
			var valid = (asc) ? (val_arr[j+1] < val_arr[j]) : (val_arr[j+1] > val_arr[j]);

	        if (valid) {
	            var temp_num = result[j + 1];
	            result[j + 1] = result[j];
	            result[j] = temp_num;

	            var temp_val = val_arr[j + 1];
	            val_arr[j + 1] = val_arr[j];
	            val_arr[j] = temp_val;
	        }
		}
	}
	
	for (var i = 0; i < array_length(num_arr); i++) {
		result[i] = db_get_value(db, (return_col > 0) ? return_col : 1, result[i]);
	}
	return result;
}
