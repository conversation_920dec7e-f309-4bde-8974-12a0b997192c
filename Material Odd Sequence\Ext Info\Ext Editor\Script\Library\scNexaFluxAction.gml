function NexaFluxAction(_type, _args = undefined) constructor {
	nexaflux_anim = other;		// NexaFluxAnim / NexaFluxPreset
	type = _type;
	args = _args;
}

function NexaFluxActionCall() constructor {
	static call = function(type, args, nexaflux_anim = noone) {
		switch (type) {
			case NEXAFLUX_ACTION.RESET_ORIGINAL:
				nexaflux_anim.set_anim_data(args[0]);
				break;

			case NEXAFLUX_ACTION.TOGGLE_STATE_DRAW:
				nexaflux_anim.nexaflux.ref.state.drawing = !nexaflux_anim.nexaflux.ref.state.drawing;
				nexaflux_anim.nexaflux.ref.drawn = !nexaflux_anim.nexaflux.ref.drawn;
				break;
			
			case NEXAFLUX_ACTION.DAMAGE:
				show_debug_message("Damage!")
				break;
		}
	}
	
	static call_struct = function(data) {
		call(data.type, data.args, data.nexaflux_anim ?? noone);
	}
}


enum NEXAFLUX_ACTION {
	RESET_ORIGINAL,
	TOGGLE_STATE_DRAW,
	DAMAGE
}
