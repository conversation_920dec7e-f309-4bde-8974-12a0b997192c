function debuff(class, hit_list, target, atk_type, debuff_type, debuff_atk_eff, debuff_duration, dot_dmg = 0, dot_dmg_type = 0, dot_interval = 0, act_type = 0, act_val = 0, show_text = global.show_debuff) {
	/*
	atk_type = 1-basic_atk, 2-special_mv, 3-ultimate
	debuff_type = 1-slow(-AGI), 2-weaken(-ATK AMP), 3-impair(-DEF AMP), 4-break/fragile(+DMG Input)
				  5-soften(-IGN INT), 6-enervate(-ATK SPD), 7-crack(-CRT PRT), 8-exhaust(+CD ADD)
				  9-devitalize(-Heal), 10-fracture(-Shield STR), 11-languish(-All RES), 12-erode(DoT)
				  minus-inv_buff
	*/
	if (class == 0) {
		class = owner;
	};
	else if (class >= 1) {
		var class_list = [0, oWarrior, oArcher, oMedic];
		class = class_list[class];
	};
	else if (class == -100) {
		class = id;
	};
	
	if (class >= 0 || class == -100) {
		if (atk_type == 1) {
			var debuff_power = (100 + class.basic_atk_power)/100;
			var debuff_eff = (100 + class.basic_atk_eff)/100;
		};
		else if (atk_type == 2) {
			var debuff_power = (100 + class.sp_move_power)/100;
			var debuff_eff = (100 + class.sp_move_eff)/100;
		};
		else if (atk_type == 3) {
			var debuff_power = (100 + class.ultimate_power)/100;
			var debuff_eff = (100 + class.ultimate_eff)/100;
		};
	};
	else {
		var debuff_power = 1;
		var debuff_eff = 1;
	};
	
	for (var i = 0; i < target; i++) {
		if (instance_exists(hit_list[| i])) {
			if (hit_list[| i].defeated == 0) {
				if (hit_list[| i].dashing == 0) {
					if (debuff_type == 1) {
						if (hit_list[| i].slow_immunity == 0) {
							if (hit_list[| i].slowed = 0) {
								hit_list[| i].slowed = 1;
								if (show_text >= 1) {
									var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
									debuff_text.debuff_type = debuff_type;
									debuff_text.vspeed -= random_range(0, 3);
									debuff_text.hspeed += random_range(-2, 2);
								};
								var dbf = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oDebuff);
								dbf.debuff_type = debuff_type;
								dbf.debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
								dbf.stick_to = hit_list[| i];
								dbf.atk_type = atk_type;
								dbf.sprite_index = hit_list[| i].sprite_index;
								if (debuff_duration > 0) {
									with (dbf) {
										alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
									};
								};
								else if (debuff_duration == 0) {
									ds_list_add(class.temp_debuff_list, dbf);
								};
								else if (debuff_duration == -1 && act_type > 0) {
									var single_debuff = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oSingleDebuff);
									single_debuff.stick_to = hit_list[| i];
									single_debuff.act_type = act_type;
									single_debuff.act_val = act_val;
									single_debuff.source = dbf;
									single_debuff.debuffer = id;
								};
							};
							else {
								var overwrite_list = ds_list_create();
								var overwrite = instance_place_list(x, y, oDebuff, overwrite_list, true);
								for (var j = 0; j < overwrite; j++) {
									if (overwrite_list[| j].debuff_type = debuff_type) {
										if (debuff_duration > 0) {
											if (debuff_atk_eff > overwrite_list[| j].debuff_atk_eff) {
												overwrite_list[| j].debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
												overwrite_list[| j].done = -1;
											};
											with (overwrite_list[| j]) {
												if ((room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100) > alarm_get(0)) {
													alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
												};
											};
										};
									};	
								};
								ds_list_destroy(overwrite_list);
							};
						};
						else if (show_text >= 1) {
							var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
							debuff_text.debuff_type = 0;
							debuff_text.vspeed -= random_range(0, 3);
							debuff_text.hspeed += random_range(-2, 2);
						};
					};
					else if (debuff_type == 2) {
						if (hit_list[| i].weaken_immunity == 0) {
							if (hit_list[| i].weakened = 0) {
								hit_list[| i].weakened = 1;
								if (show_text >= 1) {
									var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
									debuff_text.debuff_type = debuff_type;
									debuff_text.vspeed -= random_range(0, 3);
									debuff_text.hspeed += random_range(-2, 2);
								};
								var dbf = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oDebuff);
								dbf.debuff_type = debuff_type;
								dbf.debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
								dbf.stick_to = hit_list[| i];
								dbf.atk_type = atk_type;
								dbf.sprite_index = hit_list[| i].sprite_index;
								if (debuff_duration > 0) {
									with (dbf) {
										alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
									};
								};
								else if (debuff_duration == 0) {
									ds_list_add(class.temp_debuff_list, dbf);
								};
								else if (debuff_duration == -1 && act_type > 0) {
									var single_debuff = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oSingleDebuff);
									single_debuff.stick_to = hit_list[| i];
									single_debuff.act_type = act_type;
									single_debuff.act_val = act_val;
									single_debuff.source = dbf;
									single_debuff.debuffer = id;
								};
							};
							else {
								var overwrite_list = ds_list_create();
								var overwrite = instance_place_list(x, y, oDebuff, overwrite_list, true);
								for (var j = 0; j < overwrite; j++) {
									if (overwrite_list[| j].debuff_type = debuff_type) {
										if (debuff_duration > 0) {
											if (debuff_atk_eff > overwrite_list[| j].debuff_atk_eff) {
												overwrite_list[| j].debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
												overwrite_list[| j].done = -1;
											};
											with (overwrite_list[| j]) {
												if ((room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100) > alarm_get(0)) {
													alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
												};
											};
										};
									};	
								};
								ds_list_destroy(overwrite_list);
							};
						};
						else if (show_text >= 1) {
							var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
							debuff_text.debuff_type = 0;
							debuff_text.vspeed -= random_range(0, 3);
							debuff_text.hspeed += random_range(-2, 2);
						};
					};
					else if (debuff_type == 3) {
						if (hit_list[| i].impair_immunity == 0) {
							if (hit_list[| i].impaired = 0) {
								hit_list[| i].impaired = 1;
								if (show_text >= 1) {
									var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
									debuff_text.debuff_type = debuff_type;
									debuff_text.vspeed -= random_range(0, 3);
									debuff_text.hspeed += random_range(-2, 2);
								};
								var dbf = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oDebuff);
								dbf.debuff_type = debuff_type;
								dbf.debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
								dbf.stick_to = hit_list[| i];
								dbf.atk_type = atk_type;
								dbf.sprite_index = hit_list[| i].sprite_index;
								if (debuff_duration > 0) {
									with (dbf) {
										alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
									};
								};
								else if (debuff_duration == 0) {
									ds_list_add(class.temp_debuff_list, dbf);
								};
								else if (debuff_duration == -1 && act_type > 0) {
									var single_debuff = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oSingleDebuff);
									single_debuff.stick_to = hit_list[| i];
									single_debuff.act_type = act_type;
									single_debuff.act_val = act_val;
									single_debuff.source = dbf;
									single_debuff.debuffer = id;
								};
							};
							else {
								var overwrite_list = ds_list_create();
								var overwrite = instance_place_list(x, y, oDebuff, overwrite_list, true);
								for (var j = 0; j < overwrite; j++) {
									if (overwrite_list[| j].debuff_type = debuff_type) {
										if (debuff_duration > 0) {
											if (debuff_atk_eff > overwrite_list[| j].debuff_atk_eff) {
												overwrite_list[| j].debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
												overwrite_list[| j].done = -1;
											};
											with (overwrite_list[| j]) {
												if ((room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100) > alarm_get(0)) {
													alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
												};
											};
										};
									};	
								};
								ds_list_destroy(overwrite_list);
							};
						};
						else if (show_text >= 1) {
							var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
							debuff_text.debuff_type = 0;
							debuff_text.vspeed -= random_range(0, 3);
							debuff_text.hspeed += random_range(-2, 2);
						};
					};
					else if (debuff_type == 4) {
						if (hit_list[| i].break_immunity == 0) {
							if (hit_list[| i].broken = 0) {
								hit_list[| i].broken = 1;
								if (show_text >= 1) {
									var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
									debuff_text.debuff_type = debuff_type;
									debuff_text.vspeed -= random_range(0, 3);
									debuff_text.hspeed += random_range(-2, 2);
								};
								var dbf = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oDebuff);
								dbf.debuff_type = debuff_type;
								dbf.debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
								dbf.stick_to = hit_list[| i];
								dbf.atk_type = atk_type;
								dbf.sprite_index = hit_list[| i].sprite_index;
								if (debuff_duration > 0) {
									with (dbf) {
										alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
									};
								};
								else if (debuff_duration == 0) {
									ds_list_add(class.temp_debuff_list, dbf);
								};
								else if (debuff_duration == -1 && act_type > 0) {
									var single_debuff = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oSingleDebuff);
									single_debuff.stick_to = hit_list[| i];
									single_debuff.act_type = act_type;
									single_debuff.act_val = act_val;
									single_debuff.source = dbf;
									single_debuff.debuffer = id;
								};
							};
							else {
								var overwrite_list = ds_list_create();
								var overwrite = instance_place_list(x, y, oDebuff, overwrite_list, true);
								for (var j = 0; j < overwrite; j++) {
									if (overwrite_list[| j].debuff_type = debuff_type) {
										if (debuff_duration > 0) {
											if (debuff_atk_eff > overwrite_list[| j].debuff_atk_eff) {
												overwrite_list[| j].debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
												overwrite_list[| j].done = -1;
											};
											with (overwrite_list[| j]) {
												if ((room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100) > alarm_get(0)) {
													alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
												};
											};
										};
									};	
								};
								ds_list_destroy(overwrite_list);
							};
						};
						else if (show_text >= 1) {
							var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
							debuff_text.debuff_type = 0;
							debuff_text.vspeed -= random_range(0, 3);
							debuff_text.hspeed += random_range(-2, 2);
						};
					};
					else if (debuff_type == 5) {
						if (hit_list[| i].soften_immunity == 0) {
							if (hit_list[| i].softened = 0) {
								hit_list[| i].softened = 1;
								if (show_text >= 1) {
									var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
									debuff_text.debuff_type = debuff_type;
									debuff_text.vspeed -= random_range(0, 3);
									debuff_text.hspeed += random_range(-2, 2);
								};
								var dbf = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oDebuff);
								dbf.debuff_type = debuff_type;
								dbf.debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
								dbf.stick_to = hit_list[| i];
								dbf.atk_type = atk_type;
								dbf.sprite_index = hit_list[| i].sprite_index;
								if (debuff_duration > 0) {
									with (dbf) {
										alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
									};
								};
								else if (debuff_duration == 0) {
									ds_list_add(class.temp_debuff_list, dbf);
								};
								else if (debuff_duration == -1 && act_type > 0) {
									var single_debuff = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oSingleDebuff);
									single_debuff.stick_to = hit_list[| i];
									single_debuff.act_type = act_type;
									single_debuff.act_val = act_val;
									single_debuff.source = dbf;
									single_debuff.debuffer = id;
								};
							};
							else {
								var overwrite_list = ds_list_create();
								var overwrite = instance_place_list(x, y, oDebuff, overwrite_list, true);
								for (var j = 0; j < overwrite; j++) {
									if (overwrite_list[| j].debuff_type = debuff_type) {
										if (debuff_duration > 0) {
											if (debuff_atk_eff > overwrite_list[| j].debuff_atk_eff) {
												overwrite_list[| j].debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
												overwrite_list[| j].done = -1;
											};
											with (overwrite_list[| j]) {
												if ((room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100) > alarm_get(0)) {
													alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
												};
											};
										};
									};	
								};
								ds_list_destroy(overwrite_list);
							};
						};
						else if (show_text >= 1) {
							var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
							debuff_text.debuff_type = 0;
							debuff_text.vspeed -= random_range(0, 3);
							debuff_text.hspeed += random_range(-2, 2);
						};
					};
					else if (debuff_type == 6) {
						if (hit_list[| i].enervate_immunity == 0) {
							if (hit_list[| i].enervated = 0) {
								hit_list[| i].enervated = 1;
								if (show_text >= 1) {
									var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
									debuff_text.debuff_type = debuff_type;
									debuff_text.vspeed -= random_range(0, 3);
									debuff_text.hspeed += random_range(-2, 2);
								};
								var dbf = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oDebuff);
								dbf.debuff_type = debuff_type;
								dbf.debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
								dbf.stick_to = hit_list[| i];
								dbf.atk_type = atk_type;
								dbf.sprite_index = hit_list[| i].sprite_index;
								if (debuff_duration > 0) {
									with (dbf) {
										alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
									};
								};
								else if (debuff_duration == 0) {
									ds_list_add(class.temp_debuff_list, dbf);
								};
								else if (debuff_duration == -1 && act_type > 0) {
									var single_debuff = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oSingleDebuff);
									single_debuff.stick_to = hit_list[| i];
									single_debuff.act_type = act_type;
									single_debuff.act_val = act_val;
									single_debuff.source = dbf;
									single_debuff.debuffer = id;
								};
							};
							else {
								var overwrite_list = ds_list_create();
								var overwrite = instance_place_list(x, y, oDebuff, overwrite_list, true);
								for (var j = 0; j < overwrite; j++) {
									if (overwrite_list[| j].debuff_type = debuff_type) {
										if (debuff_duration > 0) {
											if (debuff_atk_eff > overwrite_list[| j].debuff_atk_eff) {
												overwrite_list[| j].debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
												overwrite_list[| j].done = -1;
											};
											with (overwrite_list[| j]) {
												if ((room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100) > alarm_get(0)) {
													alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
												};
											};
										};
									};	
								};
								ds_list_destroy(overwrite_list);
							};
						};
						else if (show_text >= 1) {
							var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
							debuff_text.debuff_type = 0;
							debuff_text.vspeed -= random_range(0, 3);
							debuff_text.hspeed += random_range(-2, 2);
						};
					};
					else if (debuff_type == 7) {
						if (hit_list[| i].crack_immunity == 0) {
							if (hit_list[| i].cracked = 0) {
								hit_list[| i].cracked = 1;
								if (show_text >= 1) {
									var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
									debuff_text.debuff_type = debuff_type;
									debuff_text.vspeed -= random_range(0, 3);
									debuff_text.hspeed += random_range(-2, 2);
								};
								var dbf = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oDebuff);
								dbf.debuff_type = debuff_type;
								dbf.debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
								dbf.stick_to = hit_list[| i];
								dbf.atk_type = atk_type;
								dbf.sprite_index = hit_list[| i].sprite_index;
								if (debuff_duration > 0) {
									with (dbf) {
										alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
									};
								};
								else if (debuff_duration == 0) {
									ds_list_add(class.temp_debuff_list, dbf);
								};
								else if (debuff_duration == -1 && act_type > 0) {
									var single_debuff = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oSingleDebuff);
									single_debuff.stick_to = hit_list[| i];
									single_debuff.act_type = act_type;
									single_debuff.act_val = act_val;
									single_debuff.source = dbf;
									single_debuff.debuffer = id;
								};
							};
							else {
								var overwrite_list = ds_list_create();
								var overwrite = instance_place_list(x, y, oDebuff, overwrite_list, true);
								for (var j = 0; j < overwrite; j++) {
									if (overwrite_list[| j].debuff_type = debuff_type) {
										if (debuff_duration > 0) {
											if (debuff_atk_eff > overwrite_list[| j].debuff_atk_eff) {
												overwrite_list[| j].debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
												overwrite_list[| j].done = -1;
											};
											with (overwrite_list[| j]) {
												if ((room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100) > alarm_get(0)) {
													alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
												};
											};
										};
									};	
								};
								ds_list_destroy(overwrite_list);
							};
						};
						else if (show_text >= 1) {
							var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
							debuff_text.debuff_type = 0;
							debuff_text.vspeed -= random_range(0, 3);
							debuff_text.hspeed += random_range(-2, 2);
						};
					};
					else if (debuff_type == 8) {
						if (hit_list[| i].exhaust_immunity == 0) {
							if (hit_list[| i].exhausted = 0) {
								hit_list[| i].exhausted = 1;
								if (show_text >= 1) {
									var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
									debuff_text.debuff_type = debuff_type;
									debuff_text.vspeed -= random_range(0, 3);
									debuff_text.hspeed += random_range(-2, 2);
								};
								var dbf = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oDebuff);
								dbf.debuff_type = debuff_type;
								dbf.debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
								dbf.stick_to = hit_list[| i];
								dbf.atk_type = atk_type;
								dbf.sprite_index = hit_list[| i].sprite_index;
								if (debuff_duration > 0) {
									with (dbf) {
										alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
									};
								};
								else if (debuff_duration == 0) {
									ds_list_add(class.temp_debuff_list, dbf);
								};
								else if (debuff_duration == -1 && act_type > 0) {
									var single_debuff = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oSingleDebuff);
									single_debuff.stick_to = hit_list[| i];
									single_debuff.act_type = act_type;
									single_debuff.act_val = act_val;
									single_debuff.source = dbf;
									single_debuff.debuffer = id;
								};
							};
							else {
								var overwrite_list = ds_list_create();
								var overwrite = instance_place_list(x, y, oDebuff, overwrite_list, true);
								for (var j = 0; j < overwrite; j++) {
									if (overwrite_list[| j].debuff_type = debuff_type) {
										if (debuff_duration > 0) {
											if (debuff_atk_eff > overwrite_list[| j].debuff_atk_eff) {
												overwrite_list[| j].debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
												overwrite_list[| j].done = -1;
											};
											with (overwrite_list[| j]) {
												if ((room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100) > alarm_get(0)) {
													alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
												};
											};
										};
									};	
								};
								ds_list_destroy(overwrite_list);
							};
						};
						else if (show_text >= 1) {
							var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
							debuff_text.debuff_type = 0;
							debuff_text.vspeed -= random_range(0, 3);
							debuff_text.hspeed += random_range(-2, 2);
						};
					};
					else if (debuff_type == 9) {
						if (hit_list[| i].devitalize_immunity == 0) {
							if (hit_list[| i].devitalized = 0) {
								hit_list[| i].devitalized = 1;
								if (show_text >= 1) {
									var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
									debuff_text.debuff_type = debuff_type;
									debuff_text.vspeed -= random_range(0, 3);
									debuff_text.hspeed += random_range(-2, 2);
								};
								var dbf = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oDebuff);
								dbf.debuff_type = debuff_type;
								dbf.debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
								dbf.stick_to = hit_list[| i];
								dbf.atk_type = atk_type;
								dbf.sprite_index = hit_list[| i].sprite_index;
								if (debuff_duration > 0) {
									with (dbf) {
										alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
									};
								};
								else if (debuff_duration == 0) {
									ds_list_add(class.temp_debuff_list, dbf);
								};
								else if (debuff_duration == -1 && act_type > 0) {
									var single_debuff = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oSingleDebuff);
									single_debuff.stick_to = hit_list[| i];
									single_debuff.act_type = act_type;
									single_debuff.act_val = act_val;
									single_debuff.source = dbf;
									single_debuff.debuffer = id;
								};
							};
							else {
								var overwrite_list = ds_list_create();
								var overwrite = instance_place_list(x, y, oDebuff, overwrite_list, true);
								for (var j = 0; j < overwrite; j++) {
									if (overwrite_list[| j].debuff_type = debuff_type) {
										if (debuff_duration > 0) {
											if (debuff_atk_eff > overwrite_list[| j].debuff_atk_eff) {
												overwrite_list[| j].debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
												overwrite_list[| j].done = -1;
											};
											with (overwrite_list[| j]) {
												if ((room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100) > alarm_get(0)) {
													alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
												};
											};
										};
									};	
								};
								ds_list_destroy(overwrite_list);
							};
						};
						else if (show_text >= 1) {
							var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
							debuff_text.debuff_type = 0;
							debuff_text.vspeed -= random_range(0, 3);
							debuff_text.hspeed += random_range(-2, 2);
						};
					};
					else if (debuff_type == 10) {
						if (hit_list[| i].fracture_immunity == 0) {
							if (hit_list[| i].fractured = 0) {
								hit_list[| i].fractured = 1;
								if (show_text >= 1) {
									var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
									debuff_text.debuff_type = debuff_type;
									debuff_text.vspeed -= random_range(0, 3);
									debuff_text.hspeed += random_range(-2, 2);
								};
								var dbf = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oDebuff);
								dbf.debuff_type = debuff_type;
								dbf.debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
								dbf.stick_to = hit_list[| i];
								dbf.atk_type = atk_type;
								dbf.sprite_index = hit_list[| i].sprite_index;
								if (debuff_duration > 0) {
									with (dbf) {
										alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
									};
								};
								else if (debuff_duration == 0) {
									ds_list_add(class.temp_debuff_list, dbf);
								};
								else if (debuff_duration == -1 && act_type > 0) {
									var single_debuff = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oSingleDebuff);
									single_debuff.stick_to = hit_list[| i];
									single_debuff.act_type = act_type;
									single_debuff.act_val = act_val;
									single_debuff.source = dbf;
									single_debuff.debuffer = id;
								};
							};
							else {
								var overwrite_list = ds_list_create();
								var overwrite = instance_place_list(x, y, oDebuff, overwrite_list, true);
								for (var j = 0; j < overwrite; j++) {
									if (overwrite_list[| j].debuff_type = debuff_type) {
										if (debuff_duration > 0) {
											if (debuff_atk_eff > overwrite_list[| j].debuff_atk_eff) {
												overwrite_list[| j].debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
												overwrite_list[| j].done = -1;
											};
											with (overwrite_list[| j]) {
												if ((room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100) > alarm_get(0)) {
													alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
												};
											};
										};
									};	
								};
								ds_list_destroy(overwrite_list);
							};
						};
						else if (show_text >= 1) {
							var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
							debuff_text.debuff_type = 0;
							debuff_text.vspeed -= random_range(0, 3);
							debuff_text.hspeed += random_range(-2, 2);
						};
					};
					else if (debuff_type == 11) {
						if (hit_list[| i].languish_immunity == 0) {
							if (hit_list[| i].languished = 0) {
								hit_list[| i].languished = 1;
								if (show_text >= 1) {
									var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
									debuff_text.debuff_type = debuff_type;
									debuff_text.vspeed -= random_range(0, 3);
									debuff_text.hspeed += random_range(-2, 2);
								};
								var dbf = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oDebuff);
								dbf.debuff_type = debuff_type;
								dbf.debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
								dbf.stick_to = hit_list[| i];
								dbf.atk_type = atk_type;
								dbf.sprite_index = hit_list[| i].sprite_index;
								if (debuff_duration > 0) {
									with (dbf) {
										alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
									};
								};
								else if (debuff_duration == 0) {
									ds_list_add(class.temp_debuff_list, dbf);
								};
								else if (debuff_duration == -1 && act_type > 0) {
									var single_debuff = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oSingleDebuff);
									single_debuff.stick_to = hit_list[| i];
									single_debuff.act_type = act_type;
									single_debuff.act_val = act_val;
									single_debuff.source = dbf;
									single_debuff.debuffer = id;
								};
							};
							else {
								var overwrite_list = ds_list_create();
								var overwrite = instance_place_list(x, y, oDebuff, overwrite_list, true);
								for (var j = 0; j < overwrite; j++) {
									if (overwrite_list[| j].debuff_type = debuff_type) {
										if (debuff_duration > 0) {
											if (debuff_atk_eff > overwrite_list[| j].debuff_atk_eff) {
												overwrite_list[| j].debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
												overwrite_list[| j].done = -1;
											};
											with (overwrite_list[| j]) {
												if ((room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100) > alarm_get(0)) {
													alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
												};
											};
										};
									};	
								};
								ds_list_destroy(overwrite_list);
							};
						};
						else if (show_text >= 1) {
							var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
							debuff_text.debuff_type = 0;
							debuff_text.vspeed -= random_range(0, 3);
							debuff_text.hspeed += random_range(-2, 2);
						};
					};
					else if (debuff_type == 12) {
						if (hit_list[| i].erode_immunity == 0) {
							if (hit_list[| i].eroded = 0) {
								hit_list[| i].eroded = 1;
								if (show_text >= 1) {
									var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
									debuff_text.debuff_type = debuff_type;
									debuff_text.vspeed -= random_range(0, 3);
									debuff_text.hspeed += random_range(-2, 2);
								};
								var dbf = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oDebuff);
								dbf.debuff_type = debuff_type;
								dbf.debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
								dbf.stick_to = hit_list[| i];
								dbf.class = class;
								dbf.atk_type = atk_type;
								dbf.dot_dmg = dot_dmg;
								dbf.dot_dmg_type = dot_dmg_type;
								dbf.dot_interval = dot_interval;
								dbf.sprite_index = hit_list[| i].sprite_index;
								if (debuff_duration > 0) {
									with (dbf) {
										alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
									};
								};
								else if (debuff_duration == 0) {
									ds_list_add(class.temp_debuff_list, dbf);
								};
								else if (debuff_duration == -1 && act_type > 0) {
									var single_debuff = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oSingleDebuff);
									single_debuff.stick_to = hit_list[| i];
									single_debuff.act_type = act_type;
									single_debuff.act_val = act_val;
									single_debuff.source = dbf;
									single_debuff.debuffer = id;
								};
							};
							else {
								var overwrite_list = ds_list_create();
								var overwrite = instance_place_list(x, y, oDebuff, overwrite_list, true);
								for (var j = 0; j < overwrite; j++) {
									if (overwrite_list[| j].debuff_type = debuff_type) {
										overwrite_list[| j].debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
										overwrite_list[| j].class = class;
										overwrite_list[| j].dot_dmg = dot_dmg;
										overwrite_list[| j].dot_dmg_type = dot_dmg_type;
										overwrite_list[| j].dot_interval = dot_interval;
										with (overwrite_list[| j]) {
											alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
										};
									};	
								};
								ds_list_destroy(overwrite_list);
							};
						};
						else if (show_text >= 1) {
							var debuff_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oDebuffText);
							debuff_text.debuff_type = 0;
							debuff_text.vspeed -= random_range(0, 3);
							debuff_text.hspeed += random_range(-2, 2);
						};
					};
					else if (debuff_type <= -1) {
						var dbf = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oDebuff);
						dbf.debuff_type = debuff_type;
						dbf.debuff_atk_eff = round(debuff_atk_eff * debuff_eff);
						dbf.stick_to = hit_list[| i];
						dbf.atk_type = atk_type;
						dbf.sprite_index = hit_list[| i].sprite_index;
						if (debuff_duration > 0) {
							with (dbf) {
								alarm_set(0, (room_speed * (debuff_duration * debuff_power))*((100-hit_list[| i].debuff_res)/100));
							};
						};
						else if (debuff_duration == 0) {
							ds_list_add(class.temp_debuff_list, dbf);
						};
						else if (debuff_duration == -1 && act_type > 0) {
							var single_debuff = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oSingleDebuff);
							single_debuff.stick_to = hit_list[| i];
							single_debuff.act_type = act_type;
							single_debuff.act_val = act_val;
							single_debuff.source = dbf;
							single_debuff.debuffer = id;
						};
					};
				};
			};
		};
	}
};