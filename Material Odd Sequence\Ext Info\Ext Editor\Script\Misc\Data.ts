type asset = {
    GMAudioGroup: any,
    GMAnimCurve: any,
    GMFont: any,
    GMObject: any,
    GMPath: any,
    GMRoom: any,
    GMScript: any,
    GMSequence: any,
    GMShader: any,
    GMSound: any,
    GMSprite: any,
    GMTileSet: any
    GMTimeline: any,
}

type id = {
    Instance: any,
    DsGrid: any,
    DsList: any,
    DsMap: any,
    DsPriority: any,
    DsQueue: any,
    DsStack: any,
}

type constant = {
    Colour: any,
    HAIign: any,
    VAIign: any,
    Cursor: any,
    EventType: any,
    EventNumber: any,
    PrimitiveType: any,
}


// Bots Data
type BotsData = {
    num: number,
    name: string,
    unlocked: boolean,
    level: number,
    xp: number,
    weapon: number,
    core: number,
    bearing: number,
    crust: number,
    sprite: asset["GMSprite"],
    sprite_w: number,
    derv: number,
    spmv: number,
    spmv_mod: number,
    ulti: number,
    batk_set: number,
    alt_batk_set: number,
    air_batk_seq: number
}


// Bots Stats
type StatData = {
    num: number,
    val: number,
}
type Stats = {
    name: StatData
}


// Bots BATK Info
type SkillAdj = {
    num1?: number,
    num2?: number
}

type Talent = {
    m1: boolean,
    s1: boolean
}

type SkillInfo = {
    num1: SeqInfo,
    num2?: SeqInfo
}

type SeqInfo = {
    type: number,
    chain_num: number,
    adj: SkillAdj,
    level: number,
    eff_level: number,
    upgraded: number,
    sprite: asset["GMSprite"],
    talent: Talent,
}


const bots_data = {
    num: 1,
    name: "Bot1",
    unlocked: true as boolean,
    level: 30,
    xp: 15000,
    weapon: 4,
    core: 13,
    bearing: 14,
    crust: 13,
    sprite: "sBot1" as asset["GMSprite"],
    sprite_w: 192,
    derv: 1,
    spmv: 2,
    spmv_mod: 2,
    ulti: 1,
    batk_set: 3,
    alt_batk_set: 0,
    air_batk_seq: 1
} satisfies BotsData;

const seq_data = {
    num1: {
        type: 1,
        chain_num: 0,
        adj: {
            num1: 1,
            num2: 3
        },
        level: 1,
        eff_level: 1,
        upgraded: 0,
        sprite: {} as asset["GMSprite"],
        talent: {
            m1: true as boolean,
            s1: false as boolean
        }
    },
} satisfies SkillInfo;

