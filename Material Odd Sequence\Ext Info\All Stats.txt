hp_flat : {num: 1, type: "flat", val: 0},
atk_flat : {num: 2, type: "flat", val: 0},
def_flat : {num: 3, type: "flat", val: 0},
agility : {num: 4, val: 100},
dmg_output : {num: 5, val: 100},
ignore_interruption : {num: 6, val: 0},     // -> ignore_int
cd_reduction : {num: 7, val: 0},            // -> cd_rdc
crit_buildup : {num: 8, val: 0},            // -> crit_bld
crit_damage : {num: 9, val: 50},            // -> crit_dmg
crit_protection : {num: 10, val: 0},        // -> crit_prt
healing_output : {num: 11, val: 100},       // -> heal_otp
atk_spd : {num: 12, val: 100},
melee_do : {num: 13, val: 0},
ranged_do : {num: 14, val: 0},
aoe_dmg_scale : {num: 15, val: 20},         // -> aoe_dmg
physical_dmg_bonus : {num: 16, val: 0},     // -> physical_db
physical_do : {num: 17, val: 0},            // -> physical_do
dmg_reduction : {num: 18, val: 0},          // -> physical_rdc
acidity_bonus : {num: 19, val: 0},          // -> acid_db
acid_do : {num: 20, val: 0},                // -> acid_do
tc : {num: 21, val: 0},                     // -> acid_rdc
def_penetration : {num: 22, val: 0},        // -> def_pen
ap : {num: 23, val: 0},                     // -> armor_pen
ab : {num: 24, val: 0},                     // -> armor_dmg
armor_burst : {num: 25, val: 50},           
armor_str : {num: 26, val: 100},
dmg_res : {num: 27, val: 40},
cc_power : {num: 28, val: 100},
cc_res : {num: 29, val: 0},
buff_power : {num: 30, val: 100},
debuff_res : {num: 31, val: 0},
charge_spd : {num: 32, val: 100},
batk_db : {num: 33, val: 0},
batk_do : {num: 34, val: 0},
batk_power : {num: 35, val: 0},
batk_eff : {num: 36, val: 0},
derv_db : {num: 37, val: 0},
derv_do : {num: 38, val: 0},
derv_power : {num: 39, val: 0},
derv_eff : {num: 40, val: 0},
spmv_db : {num: 41, val: 0},
spmv_do : {num: 42, val: 0},
spmv_power : {num: 43, val: 0},
spmv_eff : {num: 44, val: 0},
ulti_db : {num: 45, val: 0},
ulti_do : {num: 46, val: 0},
ulti_power : {num: 47, val: 0},
ulti_eff : {num: 48, val: 0},
intg_db : {num: 49, val: 0},
intg_do : {num: 50, val: 0},
intg_power : {num: 51, val: 0},
intg_eff : {num: 52, val: 0},
accuracy : {num: 53, val: 0},
recoil_reduction : {num: 54, val: 0},       // -> recoil_rdc
ammo_cap : {num: 55, val: 0},
mags_cap : {num: 56, val: 0},
reload_spd : {num: 57, val: 0}
