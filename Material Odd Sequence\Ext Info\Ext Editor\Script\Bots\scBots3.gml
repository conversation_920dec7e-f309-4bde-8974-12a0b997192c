function bots_skill_add_cd(skill_struct) {
	// skill_struct = [skill_struct, ...]
	if (!is_array(skill_struct)) {
		skill_struct = [skill_struct];
	}
	
	for (var i = 0; i < array_length(skill_struct); i++) {
		skill_struct[i].on_cd = 0;
	}
}


function Delta(step_cnt = 1/240) constructor {
    step_size = step_cnt;		 // Fixed time step in seconds (e.g., 1/240 for 240 steps per second)
    accumulator = 0;			 // Time accumulator to track elapsed time
    
    // Method to update and calculate a value based on delta time
    static calc = function(calc_callback) {
		var dt = delta_time / 1000000;
		var result = 0;
		
        accumulator += dt;
        
        while (accumulator >= step_size) {
            result += calc_callback(step_size) * 60;			// Add the callback's return value to the result
            accumulator -= step_size;							// Reduce accumulator by one step
        }
        
        return result; 
    }
}

function BotsState() constructor {
	ready = 0;
	dodge = 0;
	battle = 0;

	idle = 0;
	moving_right = 0;
	moving_left = 0;
	moving_up = 0;
	moving_down = 0;
	sprinting = 0;
	dashing = 0;
	sliding = 0;
	jumping = 0;
	falling = 0;
	drawing = 0;
	attacking = 0;
	hitting = 0;
	interrupting = 0;
	defeating = 0;
	healing = 0;
	charging = 0;
	slamming = 0;
	blocking = 0;
	parrying = 0;

	mid_air = 0;
	on_field = 0;
	off_field = 0;
	switching_in = 0;
	switching_out = 0;
	joint_attacking = 0;

	defeated = 0;
	interrupted = 0;
	attacked = 0;
	healed = 0;
	dodged = 0;
	switched_in = 0;
	switched_out = 0;

	slowed = 0;
	weakened = 0;
	impaired = 0;
	broken = 0;
	softened = 0;
	enervated = 0;
	cracked = 0;
	exhausted = 0;
	devitalized = 0;
	fractured = 0;
	languished = 0;
	eroded = 0;

	flinched = 0;
	bound = 0;
	knocked_back = 0;
	knocked_air = 0;
	knocked_down = 0;
	silenced = 0;
	stunned = 0;
	paralyzed = 0;
	neutralized = 0;
	
	static get = function(key) {
		return self[$ key];
	}

	static set = function(key, value) {
		self[$ key] = value;
	}

	static toggle = function(key) {
		self[$ key] = !self[$ key];
	}
}

function BotsAttr(bots_type = 0, atk_range = 0, bots_role = "") constructor {
	max_xp = 0;
	hp = 0;
	hp_amp = 0;
	atk_amp = 0;
	def_amp = 0;
	spd = 0;
	spd_amp = 0;
	last_spd = 0;
	charge_pt = 0;
	charge_lv = 0;
	crit_pt = 0;
	grave_pt = 0;
	super_armor = 0;
	max_super_armor = 1;
	barrier = 0;
	max_barrier = 1;
	
	type = bots_type;
	range = atk_range;
	role = bots_role;
	dir_x = 1;
	dir_y = -1;
	scale_x = 1;
	scale_y = 1;
	rot = 0;
	col = #FFFFFF;
	alpha = 1;
	
	move_step = 0;
	idle_step = 0;
	idle_alpha = -game_get_speed(gamespeed_fps);			// step
	land_block = 0;
	jump_y = 0;				// y sebelum lompat (top-down)
	last_dir_x = 0;
	last_dir_y = 0;
	nonzero_dir_x = 0;
	nonzero_dir_y = 0;
	last_x = 0;
	last_y = 0;
	add_x = 0;				// reset per step
	add_y = 0;				// reset per step
	diff_h = 0;				// Jarak y ke bayangan

	deal_hit = 0;
	deal_hit_normal = 0;
	deal_hit_crit = 0;
	deal_hit_grave = 0;
	take_hit = 0;
	take_hit_normal = 0;
	take_hit_crit = 0;
	take_hit_grave = 0;
	hit_missed = 0;
	
	/*dt_spd = new Delta();
	dt_spd_func = function(dt) {
		return spd * dt;
	}*/
}

function BotsStats(data = undefined) {
	// data: struct
	var stats = new bots_stats();
	stats = bots_stats_tohash(stats);

	if (is_struct(data)) {
		var names = struct_get_names(data);

		for (var i = 0; i < array_length(names); i++) {
			stats[$ names[i]] = data[$ names[i]];
		}
		
		delete data;
	}

	return stats;
}

function BotsGravity(_bots_parent = anim_speed_team, strength, max_vel = 5) constructor {
    // strength = gravity (px / frame^2) at 60 FPS (e.g., 0.3)
    // max_vel = max velocity (px / frame) at 60 FPS (e.g., 2)
    base_gravity = strength * 3600;  // px/s^2, converted from px/frame^2 at 60 FPS
    max_vel_s = max_vel * 60;        // px/s, converted from px/frame
    vel_y = 0;                       // Current velocity in px/s
    val = 0;                         // Displacement per frame in px
    phy_step_size = 1/240;           // Time step in seconds for physics updates
    accumulator = 0;                 // Time accumulator for frame-rate independence
    initial_jump_vel = 0;            // Store initial jump velocity for easing
	str_mul = 1;
	bots_parent = _bots_parent;

    static jump = function(jump_str) {
        // jump_str = initial velocity (px / frame) at 60 FPS (e.g., 7.5)
		//str_mul = mul;
        vel_y = -jump_str * 60 * str_mul;		 // Set initial upward velocity in px/s
        initial_jump_vel = -vel_y;				 // Store initial velocity for ease-out calculation
        val = 0;                    
        accumulator = 0;            
    }

    static update = function() {
        accumulator += delta_time / 1000000; 
        var displacement = 0;
		var spd_mul = ((bots_parent == anim_speed_team) ? gamespd_get_team() : gamespd_get_enemy());
		
        while (accumulator >= phy_step_size) {
            var gravity_acc;
            if (vel_y < 0) {        
                gravity_acc = base_gravity * 3;  
                vel_y += gravity_acc * phy_step_size * spd_mul;
                
                var ease_factor = power(1 - abs(vel_y) / initial_jump_vel, 2); 
                displacement += vel_y * phy_step_size * (1 + ease_factor * 0.2) * spd_mul;
            } else {                 
                gravity_acc = base_gravity;      
                vel_y += gravity_acc * phy_step_size * spd_mul;
                vel_y = min(vel_y, max_vel_s);          
                displacement += vel_y * phy_step_size * spd_mul;
            }
            accumulator -= phy_step_size;
        }
		
        val = displacement;  
    }
}

function BotsDash(_bots_parent = anim_speed_team, _data = undefined) constructor {
	// _data: struct
	max_gauge = 400;
	gauge = max_gauge;
	cost = 100;
	str = 1;
	dur = 0.4;
	delay = 1;
	step = 0;
	
	bots_parent = _bots_parent;

	if (is_struct(_data)) {
		var names = struct_get_names(_data);

		for (var i = 0; i < array_length(names); i++) {
			self[$ names[i]] = _data[$ names[i]];
		}
		
		delete _data;
	}

	static use = function(state) {
		if (gauge > 0 && !state.jumping) {
			state.dashing = 1;
			if (state.sprinting) {
				state.sprinting = -1;
				state.sliding = 1;
			}
			
			step = 1;
			gauge = (gauge < 100) ? 0 : (gauge - 100);
			delay = (gauge == 0) ? 3 : ((state.sliding) ? 2 : 1);

			return true;
		}
		
		return false;
	}
	
	static update = function(state) {
		if (step > 0) {
			step += 1 * ((bots_parent == anim_speed_team) ? gamespd_get_team() : gamespd_get_enemy());
			
			if (step >= delay * game_get_speed(gamespeed_fps) + 1) {			// start fill dash gauge
				step = 0;
			} else if (step >= dur * game_get_speed(gamespeed_fps) + 1) {		// end dodging time
				state.dashing = 0;
				if (state.sliding) {
					state.sliding = 0;
				}
			} else if (step >= dur/2 * game_get_speed(gamespeed_fps) + 1) {		// enable dash-cancel
				state.dashing = -1;
				if (state.sliding) {
					state.sliding = -1;
				}
			}
		} else if (!state.sprinting && gauge < max_gauge) {	
			gauge += delta_t(1.67, bots_parent);
			if (gauge > max_gauge) {
				gauge = max_gauge;
			}
		} else if (state.sprinting && gauge > 0) {	
			gauge -= delta_t(0.83, bots_parent);
			if (gauge <= 0) {
				state.sprinting = -1;
				gauge = 0;
				delay = 2;
				step = 1;
			}
		}
	}
}

function BotsWeapon(_bots_parent, _data, _attr, _state, _type = BOTS_PROPS.WEAPON) constructor {
	// _data = bots.data
	// _attr = bots.attr
	
	drawn = 0;
	sprite = -1;
	frame = 0;
	
	bots_parent = _bots_parent;
	data = _data;
	attr = _attr;
	state = _state;
	type = _type;
	
	x = 0;
	y = 0;
	image_angle = 0;
	image_xscale = 1;
	image_yscale = 1;
	image_alpha = 1;
	depth = 0;
	speed = 1;
	init_depth = 0;
	
	anim_type = {
		move : 0,
		move_draw : 0,
		draw : 0,
		sheath : 0,
		idle : 0,
		idle_draw : 0
	};
	
	anim = -1;
	//current_anim = -1;
	
	if (bots_parent = anim_speed_team) {
		//array_push(acc.frame, data.weapon - 1);
		delete anim_type;
		
		switch (_type) {
			case BOTS_PROPS.WEAPON:
				sprite = data.stg_spr.wp_spr;
				frame = (data.stg_spr.wp_frm == 0) ? (data.weapon - 1) : data.stg_spr.wp_frm;
				
				switch (data.num) {
					case bots_class_warrior: 
						anim_type = {
							def			: NEXAFLUX_PRESET.SWORD_DEF,
							def_draw	: NEXAFLUX_PRESET.SWORD_DEF_DRAWN,
							move		: NEXAFLUX_PRESET.SWORD_MOVE,
							move_draw	: NEXAFLUX_PRESET.SWORD_MOVE_DRAWN,
							draw1		: NEXAFLUX_PRESET.SWORD_DRAWING_1,
							sheath1		: NEXAFLUX_PRESET.SWORD_SHEATHING_1,
							idle1		: NEXAFLUX_PRESET.SWORD_IDLE,
							idle_draw1	: NEXAFLUX_PRESET.SWORD_IDLE_DRAWN
						};
						break;
				
					case bots_class_archer: 
						anim_type = {
							move		: NEXAFLUX_PRESET.SWORD_MOVE,
							move_draw	: NEXAFLUX_PRESET.SWORD_MOVE_DRAWN,
							draw1		: NEXAFLUX_PRESET.SWORD_DRAWING_1,
							sheath1		: NEXAFLUX_PRESET.SWORD_SHEATHING_1,
							idle1		: NEXAFLUX_PRESET.SWORD_IDLE,
							idle_draw1	: NEXAFLUX_PRESET.SWORD_IDLE_DRAWN
						};
						break;
				
					case bots_class_medic: 
						
						break;
				}
				break;
				
			case BOTS_PROPS.WEAPON_ACC1:
				sprite = data.stg_spr.wp_acc_spr[_type-1];
				frame = (data.stg_spr.wp_acc_frm[_type-1] == 0) ? (data.weapon - 1) : data.stg_spr.wp_acc_frm[_type-1];
				
				switch (data.num) {
					case bots_class_warrior: 
						anim_type = {
							def			: NEXAFLUX_PRESET.SWORD_DEF,
							def_draw	: NEXAFLUX_PRESET.SWORD_DEF,
							move		: NEXAFLUX_PRESET.SWORD_MOVE,
							move_draw	: NEXAFLUX_PRESET.SWORD_MOVE,
							idle1		: NEXAFLUX_PRESET.SWORD_IDLE,
							idle_draw1	: NEXAFLUX_PRESET.SWORD_IDLE
						};
						break;
				
					case bots_class_archer: 
						anim_type = {
							move		: NEXAFLUX_PRESET.SWORD_MOVE,
							move_draw	: NEXAFLUX_PRESET.SWORD_MOVE_DRAWN,
							idle1		: NEXAFLUX_PRESET.SWORD_IDLE,
							idle_draw1	: NEXAFLUX_PRESET.SWORD_IDLE_DRAWN
						};
						break;
				
					case bots_class_medic: 
						
						break;
				}
				break;
		}
		
		if (struct_exists(anim_type, "move")) {
			anim = nexaflux_create(new NexaFluxAnim(self, false, anim_type.move));
			current_anim = anim_type.move;
		}
	}
}

function BotsSkill(data = undefined) constructor {
	// data: struct
	multiplier = "atk";
	dmg_type = skill_dmg_type.physical;
	cd_type = skill_cd_type.timeout;
	target = skill_target_type.enemy;
	target_cnt = 1;

	if (is_struct(data)) {
		var names = struct_get_names(data);

		for (var i = 0; i < array_length(names); i++) {
			self[$ names[i]] = data[$ names[i]];
		}
		
		delete data;
	}
}

enum BOTS_PROPS {
	WEAPON,
	WEAPON_ACC1
}
