Normal
------
  Roller
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 350 + (level*50) + (floor(level/5)*5)*(level*10)
      Level 1 = 400
      Level 50 = 27,850
    Base ATK = 260 + (level*10) + (floor(level/5)*5)*level
      Level 1 = 270
      Level 50 = 3,260
    Base DEF = 35 + (level*5) + (floor(level/5)*6)*level
      Level 1 = 40
      Level 50 = 3,285
    Attack Pattern = Avoid, Jump, Dash, Crash
    Attack Base Damage = 0, 0, 0, 100%
    Attack CD = 0, 0, 3, 2
    Attack Distance = 0, 0, 0, 200
    XP = (ceil((level+1)/5)*10)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 red_oil), Lv25(2 red_oil), Lv35(3 red_oil), Lv50(1 orange_oil)

  Smasher
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 500 + (level*50) + (floor(level/5)*6)*(level*10)
      Level 1 = 550
      Level 50 = 33,000
    Base ATK = 310 + (level*10) + (floor(level/5)*5.5)*level
      Level 1 = 320
      Level 50 = 3,560
    Base DEF = 45 + (level*5) + (floor(level/5)*6.2)*level
      Level 1 = 50
      Level 50 = 3,395
    Attack Pattern = Avoid, Jump, Dash, Smash
    Attack Base Damage = 0, 0, 0, 150%
    Attack CD = 0, 0, 3, 3
    Attack Distance = 0, 0, 0, 200
    XP = (ceil((level+1)/5)*15)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 blue_oil), Lv25(2 blue_oil), Lv35(3 blue_oil), Lv50(1 dodgerblue_oil)

  Chaser
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 1000 + (level*60) + (floor(level/5)*7)*(level*10)
      Level 1 = 1,060
      Level 50 = 39,000
    Base ATK = 285 + (level*10) + (floor(level/5)*5.25)*level
      Level 1 = 295
      Level 50 = 3,410
    Base DEF = 35 + (level*5) + (floor(level/5)*6)*level
      Level 1 = 40
      Level 50 = 3,285
    Attack Pattern = Avoid, Jump, Dash, Crash
    Attack Base Damage = 0, 0, 0, 100%
    Attack CD = 0, 0, 3, 2
    Attack Distance = 0, 0, 0, 200
    Passive Skill = Chase (800)
    XP = (ceil((level+1)/5)*15)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 lime_oil), Lv25(2 lime_oil), Lv35(3 lime_oil), Lv50(1 olive_oil)

  Rescuer
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 300 + (level*40) + (floor(level/5)*4)*(level*10)
      Level 1 = 340
      Level 50 = 22,300
    Base ATK = 160 + (level*10) + (floor(level/5)*3.5)*level
      Level 1 = 165
      Level 50 = 2,410
    Base DEF = 35 + (level*4) + (floor(level/5)*4)*level
      Level 1 = 39
      Level 50 = 2,235
    Attack Pattern = Avoid, Jump, Dash
    Attack Base Damage = 0, 0, 0
    Attack CD = 0, 0, 3
    Attack Distance = 0, 0, 0
    Skill List = Healing Potion (2%~5s)
    Skill Damage = 0
    Skill CD = 12
    Skill Distance = 9999
    XP = (ceil((level+1)/5)*15)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 lime_oil), Lv25(2 lime_oil), Lv35(3 lime_oil), Lv50(1 chartreuse_oil)

  Battler
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 750 + (level*55) + (floor(level/5)*6.5)*(level*10)
      Level 1 = 805
      Level 50 = 36,000
    Base ATK = 310 + (level*10) + (floor(level/5)*5.5)*level
      Level 1 = 320
      Level 50 = 3,560
    Base DEF = 50 + (level*7) + (floor(level/5)*6)*level
      Level 1 = 57
      Level 50 = 3,400
    Weapon = Damascus Sword
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow
    Attack Base Damage = 0, 0, 0, 80%, 200%
    Attack CD = 0, 0, 3, 2, 3
    Attack Distance = 0, 0, 0, 250, 350
    XP = (ceil((level+1)/5)*20)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 red_oil), Lv25(2 red_oil), Lv35(3 red_oil), Lv50(1 yellow_oil)

  Shooter
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 350 + (level*50) + (floor(level/5)*5)*(level*10)
      Level 1 = 390
      Level 50 = 23,600
    Base ATK = 275 + (level*11) + (floor(level/5)*5.25)*level
      Level 1 = 286
      Level 50 = 3,450
    Base DEF = 35 + (level*4) + (floor(level/5)*4.5)*level
      Level 1 = 39
      Level 50 = 2,485
    Weapon = Longbow
    Attack Pattern = Avoid, Jump, Dash, O-Shot I, Heavy Shot
    Attack Base Damage = 0, 0, 0, 70%, 200%
    Attack CD = 0, 0, 3, 2, 3
    Attack Distance = 0, 0, 0, 500, 500
    XP = (ceil((level+1)/5)*20)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 blue_oil), Lv25(2 blue_oil), Lv35(3 blue_oil), Lv50(1 deeppink_oil)

  Roller Type-II
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 1000 + (level*60) + (floor(level/5)*6.5)*(level*10)
      Level 1 = 1,060
      Level 50 = 36,500
    Base ATK = 300 + (level*12) + (floor(level/5)*5.2)*level
      Level 1 = 312
      Level 50 = 3,500
    Base DEF = 50 + (level*7) + (floor(level/5)*6.2)*level
      Level 1 = 57
      Level 50 = 3,510
    Attack Pattern = Avoid, Jump, Dash, Crash
    Attack Base Damage = 0, 0, 0, 100%
    Attack CD = 0, 0, 2.5, 1.5
    Attack Distance = 0, 0, 0, 200
    XP = (ceil((level+1)/5)*15)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 red_oil), Lv25(2 red_oil), Lv35(3 red_oil), Lv50(1 orange_oil)

  Smasher Type-II
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 1500 + (level*70) + (floor(level/5)*7)*(level*10)
      Level 1 = 1,570
      Level 50 = 40,000
    Base ATK = 350 + (level*15) + (floor(level/5)*5.5)*level
      Level 1 = 365
      Level 50 = 3,850
    Base DEF = 60 + (level*7) + (floor(level/5)*6.5)*level
      Level 1 = 67
      Level 50 = 3,660
    Attack Pattern = Avoid, Jump, Dash, Smash
    Attack Base Damage = 0, 0, 0, 150%
    Attack CD = 0, 0, 2.5, 2.5
    Attack Distance = 0, 0, 0, 200
    XP = (ceil((level+1)/5)*20)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 blue_oil), Lv25(2 blue_oil), Lv35(3 blue_oil), Lv50(1 dodgerblue_oil)

  Chaser Type-II
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 3000 + (level*100) + (floor(level/5)*8)*(level*10)
      Level 1 = 3,060
      Level 50 = 48,000
    Base ATK = 320 + (level*12) + (floor(level/5)*5.34)*level
      Level 1 = 332
      Level 50 = 3,590
    Base DEF = 60 + (level*7) + (floor(level/5)*6.2)*level
      Level 1 = 67
      Level 50 = 3,510
    Attack Pattern = Avoid, Jump, Dash, Crash
    Attack Base Damage = 0, 0, 0, 100%
    Attack CD = 0, 0, 2.5, 1.5
    Attack Distance = 0, 0, 0, 200
    Passive Skill = Chase Lv2 (1200)
    XP = (ceil((level+1)/5)*20)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 lime_oil), Lv25(2 lime_oil), Lv35(3 lime_oil), Lv50(1 olive_oil)

  Rescuer Type-II
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 1000 + (level*40) + (floor(level/5)*6)*(level*10)
      Level 1 = 1,040
      Level 50 = 33,000
    Base ATK = 250 + (level*10) + (floor(level/5)*4.5)*level
      Level 1 = 260
      Level 50 = 3,000
    Base DEF = 50 + (level*7) + (floor(level/5)*4.8)*level
      Level 1 = 57
      Level 50 = 2,800
    Attack Pattern = Avoid, Jump, Dash, Crash
    Attack Base Damage = 0, 0, 0, 100
    Attack CD = 0, 0, 2.5, 1.5
    Attack Distance = 0, 0, 0, 200
    Skill List = Healing Potion (2%~5s)
    Skill Damage = 0
    Skill CD = 10
    Skill Distance = 9999
    XP = (ceil((level+1)/5)*20)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 lime_oil), Lv25(2 lime_oil), Lv35(3 lime_oil), Lv50(1 chartreuse_oil)

  Battler Type-II
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 2500 + (level*80) + (floor(level/5)*7.3)*(level*10)
      Level 1 = 2,580
      Level 50 = 43,000
    Base ATK = 350 + (level*13) + (floor(level/5)*5.5)*level
      Level 1 = 363
      Level 50 = 3,750
    Base DEF = 100 + (level*8) + (floor(level/5)*6.3)*level
      Level 1 = 108
      Level 50 = 3,650
    Weapon = Damascus Sword
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow, Thrust
    Attack Base Damage = 0, 0, 0, 80%, 200%, 100%
    Attack CD = 0, 0, 2.5, 1.75, 2.5, 2
    Attack Distance = 0, 0, 0, 250, 300, 450
    XP = (ceil((level+1)/5)*25)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 red_oil), Lv25(2 red_oil), Lv35(3 red_oil), Lv50(1 yellow_oil)

  Shooter Type-II
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 850 + (level*60) + (floor(level/5)*6.5)*(level*10)
      Level 1 = 910
      Level 50 = 36,350
    Base ATK = 320 + (level*13) + (floor(level/5)*6)*level
      Level 1 = 333
      Level 50 = 3,970
    Base DEF = 50 + (level*6) + (floor(level/5)*4.8)*level
      Level 1 = 56
      Level 50 = 2,750
    Weapon = Longbow
    Attack Pattern = Avoid, Jump, Dash, O-Shot I, Heavy Shot, Multi Shot
    Attack Base Damage = 0, 0, 0, 70%, 200%, 2x50%
    Attack CD = 0, 0, 2.5, 1.75, 2.5, 2
    Attack Distance = 0, 0, 0, 500, 500, 500
    XP = (ceil((level+1)/5)*25)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 blue_oil), Lv25(2 blue_oil), Lv35(3 blue_oil), Lv50(1 deeppink_oil)

  Dew Smasher
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 500 + (level*50) + (floor(level/5)*6)*(level*10)
      Level 1 = 550
      Level 50 = 33,000
    Base ATK = 310 + (level*10) + (floor(level/5)*5.5)*level
      Level 1 = 320
      Level 50 = 3,560
    Base DEF = 45 + (level*5) + (floor(level/5)*6.2)*level
      Level 1 = 50
      Level 50 = 3,395
    Attack Pattern = Avoid, Jump, Dash, Smash
    Attack Base Damage = 0, 0, 0, 150%
    Attack CD = 0, 0, 3, 3
    Attack Distance = 0, 0, 0, 200
    Passive Skill = Acid Infusion (place_meeting--water, -1)
    XP = (ceil((level+1)/5)*15)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 blue_oil), Lv25(2 blue_oil), Lv35(3 blue_oil), Lv50(1 aqua_oil)

  Dew Chaser
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 1000 + (level*60) + (floor(level/5)*7)*(level*10)
      Level 1 = 1,060
      Level 50 = 39,000
    Base ATK = 285 + (level*10) + (floor(level/5)*5.25)*level
      Level 1 = 295
      Level 50 = 3,410
    Base DEF = 35 + (level*5) + (floor(level/5)*6)*level
      Level 1 = 40
      Level 50 = 3,285
    Attack Pattern = Avoid, Jump, Dash, Crash
    Attack Base Damage = 0, 0, 0, 100%
    Attack CD = 0, 0, 3, 2
    Attack Distance = 0, 0, 0, 200
    Passive Skill = Chase (800), Acid Infusion (place_meeting--water, -1)
    XP = (ceil((level+1)/5)*15)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 blue_oil), Lv25(2 blue_oil), Lv35(3 blue_oil), Lv50(1 dodgerblue_oil)

  Dew Battler
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 750 + (level*55) + (floor(level/5)*6.5)*(level*10)
      Level 1 = 805
      Level 50 = 36,000
    Base ATK = 310 + (level*10) + (floor(level/5)*5.5)*level
      Level 1 = 320
      Level 50 = 3,560
    Base DEF = 50 + (level*7) + (floor(level/5)*6)*level
      Level 1 = 57
      Level 50 = 3,400
    Weapon = Damascus Sword
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow
    Attack Base Damage = 0, 0, 0, 80%, 200%
    Attack CD = 0, 0, 3, 2, 3
    Attack Distance = 0, 0, 0, 250, 350
    Passive Skill = Acid Infusion (place_meeting--water, -1)
    XP = (ceil((level+1)/5)*20)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 lime_oil), Lv25(2 lime_oil), Lv35(3 lime_oil), Lv50(1 springgreen_oil)

  Dew Shooter
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 350 + (level*50) + (floor(level/5)*5)*(level*10)
      Level 1 = 390
      Level 50 = 23,600
    Base ATK = 275 + (level*11) + (floor(level/5)*5.25)*level
      Level 1 = 286
      Level 50 = 3,450
    Base DEF = 35 + (level*4) + (floor(level/5)*4.5)*level
      Level 1 = 39
      Level 50 = 2,485
    Weapon = Longbow
    Attack Pattern = Avoid, Jump, Dash, O-Shot I, Heavy Shot
    Attack Base Damage = 0, 0, 0, 70%, 200%
    Attack CD = 0, 0, 3, 2, 3
    Attack Distance = 0, 0, 0, 500, 500
    Passive Skill = Acid Infusion (place_meeting--water, -1)
    XP = (ceil((level+1)/5)*20)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 lime_oil), Lv25(2 lime_oil), Lv35(3 lime_oil), Lv50(1 teal_oil)

  Dew Smasher v2
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 1500 + (level*70) + (floor(level/5)*7)*(level*10)
      Level 1 = 1,570
      Level 50 = 40,000
    Base ATK = 350 + (level*15) + (floor(level/5)*5.5)*level
      Level 1 = 365
      Level 50 = 3,850
    Base DEF = 60 + (level*7) + (floor(level/5)*6.5)*level
      Level 1 = 67
      Level 50 = 3,660
    Attack Pattern = Avoid, Jump, Dash, Smash
    Attack Base Damage = 0, 0, 0, 150%
    Attack CD = 0, 0, 2.5, 2.5
    Attack Distance = 0, 0, 0, 200
    Passive Skill = Acid Infusion (place_meeting--water, -1)
    XP = (ceil((level+1)/5)*20)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 blue_oil), Lv25(2 blue_oil), Lv35(3 blue_oil), Lv50(1 aqua_oil)

  Dew Chaser v2
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 3000 + (level*100) + (floor(level/5)*8)*(level*10)
      Level 1 = 3,060
      Level 50 = 48,000
    Base ATK = 320 + (level*12) + (floor(level/5)*5.34)*level
      Level 1 = 332
      Level 50 = 3,590
    Base DEF = 60 + (level*7) + (floor(level/5)*6.2)*level
      Level 1 = 67
      Level 50 = 3,510
    Attack Pattern = Avoid, Jump, Dash, Crash
    Attack Base Damage = 0, 0, 0, 100%
    Attack CD = 0, 0, 2.5, 1.5
    Attack Distance = 0, 0, 0, 200
    Passive Skill = Chase Lv2 (1200), Acid Infusion (place_meeting--water, -1)
    XP = (ceil((level+1)/5)*20)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 blue_oil), Lv25(2 blue_oil), Lv35(3 blue_oil), Lv50(1 dodgerblue_oil)

  Dew Battler v2
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 2500 + (level*80) + (floor(level/5)*7.3)*(level*10)
      Level 1 = 2,580
      Level 50 = 43,000
    Base ATK = 350 + (level*13) + (floor(level/5)*5.5)*level
      Level 1 = 363
      Level 50 = 3,750
    Base DEF = 100 + (level*8) + (floor(level/5)*6.3)*level
      Level 1 = 108
      Level 50 = 3,650
    Weapon = Damascus Sword
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow, Thrust
    Attack Base Damage = 0, 0, 0, 80%, 200%, 100%
    Attack CD = 0, 0, 2.5, 1.75, 2.5, 2
    Attack Distance = 0, 0, 0, 250, 300, 450
    Passive Skill = Acid Infusion (place_meeting--water, -1)
    XP = (ceil((level+1)/5)*25)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 lime_oil), Lv25(2 lime_oil), Lv35(3 lime_oil), Lv50(1 springgreen_oil)

  Dew Shooter v2
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 850 + (level*60) + (floor(level/5)*6.5)*(level*10)
      Level 1 = 910
      Level 50 = 36,350
    Base ATK = 320 + (level*13) + (floor(level/5)*6)*level
      Level 1 = 333
      Level 50 = 3,970
    Base DEF = 50 + (level*6) + (floor(level/5)*4.8)*level
      Level 1 = 56
      Level 50 = 2,750
    Weapon = Longbow
    Attack Pattern = Avoid, Jump, Dash, O-Shot I, Heavy Shot, Multi Shot
    Attack Base Damage = 0, 0, 0, 70%, 200%, 2x50%
    Attack CD = 0, 0, 2.5, 1.75, 2.5, 2
    Attack Distance = 0, 0, 0, 500, 500, 500
    Passive Skill = Acid Infusion (place_meeting--water, -1)
    XP = (ceil((level+1)/5)*25)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 lime_oil), Lv25(2 lime_oil), Lv35(3 lime_oil), Lv50(1 teal_oil)

  Poacher
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 750 + (level*55) + (floor(level/5)*6.5)*(level*10)
      Level 1 = 805
      Level 50 = 36,000
    Base ATK = 310 + (level*10) + (floor(level/5)*5.5)*level
      Level 1 = 320
      Level 50 = 3,560
    Base DEF = 50 + (level*7) + (floor(level/5)*6)*level
      Level 1 = 57
      Level 50 = 3,400
    Weapon = Damascus Sword
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow
    Attack Base Damage = 0, 0, 0, 80%, 200%
    Attack CD = 0, 0, 3, 2, 3
    Attack Distance = 0, 0, 0, 250, 350
    Passive Skill = Laceration (0)
    XP = (ceil((level+1)/5)*20)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 blue_oil), Lv25(2 blue_oil), Lv35(3 blue_oil), Lv50(1 teal_oil)

  Forest Trespasser
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 1000 + (level*60) + (floor(level/5)*7)*(level*10)
      Level 1 = 1,060
      Level 50 = 39,000
    Base ATK = 285 + (level*10) + (floor(level/5)*5.25)*level
      Level 1 = 295
      Level 50 = 3,410
    Base DEF = 40 + (level*6) + (floor(level/5)*6)*level
      Level 1 = 46
      Level 50 = 3,340
    Weapon = Damascus Knife
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow
    Attack Base Damage = 0, 0, 0, 40%, 160%
    Attack CD = 0, 0, 3, 2, 3
    Attack Distance = 0, 0, 0, 250, 300
    Passive Skill = Laceration (0)
    XP = (ceil((level+1)/5)*20)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 red_oil), Lv25(2 red_oil), Lv35(3 red_oil), Lv50(1 orange_oil)

  Illegal Logger
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 900 + (level*60) + (floor(level/5)*6.7)*(level*10)
      Level 1 = 960
      Level 50 = 37,400
    Base ATK = 305 + (level*11) + (floor(level/5)*5.25)*level
      Level 1 = 316
      Level 50 = 3,480
    Base DEF = 40 + (level*5) + (floor(level/5)*5.7)*level
      Level 1 = 45
      Level 50 = 3,140
    Weapon = Crosscut Saw
    Attack Pattern = Avoid, Jump, Dash, Crash, Slash
    Attack Base Damage = 0, 0, 0, 100%, 100%
    Attack CD = 0, 0, 3, 2, 3
    Attack Distance = 0, 0, 0, 250, 250
    Passive Skill = Laceration (0-1)
    XP = (ceil((level+1)/5)*20)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 red_oil), Lv25(2 red_oil), Lv35(3 red_oil), Lv50(1 olive_oil)

  Illegal Hunter
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 350 + (level*50) + (floor(level/5)*5)*(level*10)
      Level 1 = 390
      Level 50 = 23,600
    Base ATK = 275 + (level*11) + (floor(level/5)*5.25)*level
      Level 1 = 286
      Level 50 = 3,450
    Base DEF = 35 + (level*4) + (floor(level/5)*4.5)*level
      Level 1 = 39
      Level 50 = 2,485
    Weapon = Air Rifle
    Attack Pattern = Avoid, Jump, Dash, O-Shot
    Attack Base Damage = 0, 0, 0, 100%
    Attack CD = 0, 0, 3, 3
    Attack Distance = 0, 0, 0, 600
    Passive Skill = Laceration (1)
    XP = (ceil((level+1)/5)*20)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 lime_oil), Lv25(2 lime_oil), Lv35(3 lime_oil), Lv50(1 yellow_oil)

  Poacher v2
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 2500 + (level*80) + (floor(level/5)*7.3)*(level*10)
      Level 1 = 2,580
      Level 50 = 43,000
    Base ATK = 350 + (level*13) + (floor(level/5)*5.5)*level
      Level 1 = 363
      Level 50 = 3,750
    Base DEF = 100 + (level*8) + (floor(level/5)*6.3)*level
      Level 1 = 108
      Level 50 = 3,650
    Weapon = Damascus Sword
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow, Thrust
    Attack Base Damage = 0, 0, 0, 80%, 200%, 100%
    Attack CD = 0, 0, 2.5, 1.75, 2.5, 2
    Attack Distance = 0, 0, 0, 250, 300, 450
    Passive Skill = Laceration (0)
    XP = (ceil((level+1)/5)*25)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 blue_oil), Lv25(2 blue_oil), Lv35(3 blue_oil), Lv50(1 teal_oil)

  Forest Trespasser v2
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 3000 + (level*100) + (floor(level/5)*8)*(level*10)
      Level 1 = 3,060
      Level 50 = 48,000
    Base ATK = 320 + (level*12) + (floor(level/5)*5.34)*level
      Level 1 = 332
      Level 50 = 3,590
    Base DEF = 80 + (level*7) + (floor(level/5)*6.2)*level
      Level 1 = 46
      Level 50 = 3,530
    Weapon = Damascus Knife
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow
    Attack Base Damage = 0, 0, 0, 50%, 180%
    Attack CD = 0, 0, 3, 2, 3
    Attack Distance = 0, 0, 0, 250, 300
    Passive Skill = Laceration (0)
    XP = (ceil((level+1)/5)*25)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 red_oil), Lv25(2 red_oil), Lv35(3 red_oil), Lv50(1 orange_oil)

  Illegal Logger v2
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 3000 + (level*110) + (floor(level/5)*8.5)*(level*10)
      Level 1 = 3,110
      Level 50 = 51,000
    Base ATK = 340 + (level*13) + (floor(level/5)*5.4)*level
      Level 1 = 353
      Level 50 = 3,690
    Base DEF = 80 + (level*7) + (floor(level/5)*6.2)*level
      Level 1 = 87
      Level 50 = 3,530
    Weapon = Crosscut Saw
    Attack Pattern = Avoid, Jump, Dash, Crash, Slash
    Attack Base Damage = 0, 0, 0, 110%, 120%
    Attack CD = 0, 0, 3, 2, 2
    Attack Distance = 0, 0, 0, 250, 250
    Passive Skill = Laceration (0-1)
    XP = (ceil((level+1)/5)*25)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 red_oil), Lv25(2 red_oil), Lv35(3 red_oil), Lv50(1 olive_oil)

  Illegal Hunter v2
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 850 + (level*60) + (floor(level/5)*6.5)*(level*10)
      Level 1 = 910
      Level 50 = 36,350
    Base ATK = 320 + (level*13) + (floor(level/5)*6)*level
      Level 1 = 333
      Level 50 = 3,970
    Base DEF = 50 + (level*6) + (floor(level/5)*4.8)*level
      Level 1 = 56
      Level 50 = 2,750
    Weapon = Air Rifle
    Attack Pattern = Avoid, Jump, Dash, O-Shot, Heavy Shot
    Attack Base Damage = 0, 0, 0, 110%, 250%
    Attack CD = 0, 0, 3, 3, 4
    Attack Distance = 0, 0, 0, 600, 600
    Passive Skill = Laceration (1)
    XP = (ceil((level+1)/5)*25)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 lime_oil), Lv25(2 lime_oil), Lv35(3 lime_oil), Lv50(1 yellow_oil)

  Nightfall Smasher
  Nightfall Chaser
  Nightfall Battler
  Nightfall Shooter
  Nightfall Smasher v2
  Nightfall Chaser v2
  Nightfall Battler v2
  Nightfall Shooter v2


Enhanced
--------
  Roller Type-III
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 2750 + (level*75) + (floor(level/5)*7.5)*(level*10)
      Level 1 = 2,825
      Level 50 = 44,000
    Base ATK = 350 + (level*13) + (floor(level/5)*5.5)*level
      Level 1 = 363
      Level 50 = 3,750
    Base DEF = 70 + (level*9) + (floor(level/5)*6.5)*level
      Level 1 = 79
      Level 50 = 3,770
    Attack Pattern = Avoid, Jump, Dash, Crash
    Attack Base Damage = 0, 0, 0, 110%
    Attack CD = 0, 0, 2, 1
    Attack Distance = 0, 0, 0, 200
    XP = (ceil((level+1)/5)*25)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 red_oil), Lv25(2 red_oil), Lv35(3 red_oil), Lv50(1 orange_oil)

  Smasher Type-III
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 3500 + (level*90) + (floor(level/5)*8)*(level*10)
      Level 1 = 3,590
      Level 50 = 48,000
    Base ATK = 420 + (level*17) + (floor(level/5)*6)*level
      Level 1 = 437
      Level 50 = 4,270
    Base DEF = 90 + (level*10) + (floor(level/5)*6.7)*level
      Level 1 = 100
      Level 50 = 3,940
    Attack Pattern = Avoid, Jump, Dash, Crash, Smash
    Attack Base Damage = 0, 0, 0, 100%, 170%
    Attack CD = 0, 0, 2, 1, 2
    Attack Distance = 0, 0, 0, 200, 200
    XP = (ceil((level+1)/5)*30)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 blue_oil), Lv25(2 blue_oil), Lv35(3 blue_oil), Lv50(1 dodgerblue_oil)

  Chaser Type-III
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 6000 + (level*130) + (floor(level/5)*9.5)*(level*10)
      Level 1 = 6,130
      Level 50 = 60,000
    Base ATK = 380 + (level*14) + (floor(level/5)*5.6)*level
      Level 1 = 394
      Level 50 = 3,880
    Base DEF = 70 + (level*10) + (floor(level/5)*6.5)*level
      Level 1 = 80
      Level 50 = 3,820
    Attack Pattern = Avoid, Jump, Dash, Crash, Heavy Crash
    Attack Base Damage = 0, 0, 0, 110%, 150%
    Attack CD = 0, 0, 2, 1, 2
    Attack Distance = 0, 0, 0, 200, 250
    Passive Skill = Chase Lv3 (1600)
    XP = (ceil((level+1)/5)*30)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 lime_oil), Lv25(2 lime_oil), Lv35(3 lime_oil), Lv50(1 olive_oil)

  Rescuer Type-III
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 3000 + (level*80) + (floor(level/5)*7)*(level*10)
      Level 1 = 3,080
      Level 50 = 42,000
    Base ATK = 300 + (level*13) + (floor(level/5)*5)*level
      Level 1 = 313
      Level 50 = 3,450
    Base DEF = 70 + (level*9) + (floor(level/5)*6)*level
      Level 1 = 79
      Level 50 = 3,520
    Attack Pattern = Avoid, Jump, Dash, Crash
    Attack Base Damage = 0, 0, 0, 110%
    Attack CD = 0, 0, 2, 1
    Attack Distance = 0, 0, 0, 200
    Skill List = Lv2 Healing Potion (3%~5s)
    Skill Damage = 0
    Skill CD = 10
    Skill Distance = 9999
    XP = (ceil((level+1)/5)*30)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 lime_oil), Lv25(2 lime_oil), Lv35(3 lime_oil), Lv50(1 chartreuse_oil)

  Battler Type-III
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 5000 + (level*120) + (floor(level/5)*8.4)*(level*10)
      Level 1 = 5,120
      Level 50 = 53,000
    Base ATK = 420 + (level*15) + (floor(level/5)*6)*level
      Level 1 = 435
      Level 50 = 4,170
    Base DEF = 120 + (level*11) + (floor(level/5)*6.7)*level
      Level 1 = 131
      Level 50 = 4,020
    Weapon = Damascus Sword
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow, Thrust, Twin-Thrust
    Attack Base Damage = 0, 0, 0, 90%, 240%, 110%, 2x60%
    Attack CD = 0, 0, 2, 1.5, 2.5, 1.75, 2
    Attack Distance = 0, 0, 0, 250, 300, 450, 300
    XP = (ceil((level+1)/5)*35)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 red_oil), Lv25(2 red_oil), Lv35(3 red_oil), Lv50(1 yellow_oil)

  Shooter Type-III
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 2500 + (level*100) + (floor(level/5)*7.5)*(level*10)
      Level 1 = 2,600
      Level 50 = 45,000
    Base ATK = 380 + (level*15) + (floor(level/5)*6.5)*level
      Level 1 = 395
      Level 50 = 4,380
    Base DEF = 70 + (level*8) + (floor(level/5)*5.8)*level
      Level 1 = 78
      Level 50 = 3,370
    Weapon = Longbow
    Attack Pattern = Avoid, Jump, Dash, O-Shot I, Heavy Shot, Multi Shot, Parallel Shot
    Attack Base Damage = 0, 0, 0, 80%, 230%, 3x60%, 3x70%
    Attack CD = 0, 0, 2, 1.5, 2.5, 2, 3
    Attack Distance = 0, 0, 0, 550, 550, 550, 550, 550
    XP = (ceil((level+1)/5)*35)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 blue_oil), Lv25(2 blue_oil), Lv35(3 blue_oil), Lv50(1 deeppink_oil)

  Bandit
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 4000 + (level*120) + (floor(level/5)*8)*(level*10)
      Level 1 = 4,120
      Level 50 = 50,000
    Base ATK = 380 + (level*14) + (floor(level/5)*5.7)*level
      Level 1 = 394
      Level 50 = 3,930
    Base DEF = 70 + (level*9) + (floor(level/5)*6.5)*level
      Level 1 = 79
      Level 50 = 3,770
    Weapon = Damascus Knife
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow, Thrust
    Attack Base Damage = 0, 0, 0, 60%, 180%, 80%
    Attack CD = 0, 0, 2, 1.5, 2.5, 2
    Attack Distance = 0, 0, 0, 250, 250, 400
    XP = (ceil((level+1)/5)*30)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 red_oil), Lv25(2 red_oil), Lv35(3 red_oil), Lv50(1 purple_oil)

  Tank
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 20000 + (level*500) + (floor(level/5)*11)*(level*10)
      Level 1 = 20,500
      Level 50 = 100,000
    Base ATK = 550 + (level*22) + (floor(level/5)*6.5)*level
      Level 1 = 572
      Level 50 = 4,900
    Base DEF = 50 + (level*10) + (floor(level/5)*5)*level
      Level 1 = 60
      Level 50 = 3,050
    Attack Pattern = Avoid
    Attack Base Damage = 0
    Attack CD = 0
    Attack Distance = 0
    Passive Skill = Pacifist (-)
    XP = (ceil((level+1)/5)*40)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 blue_oil), Lv15(3 blue_oil), Lv20(4 blue_oil), Lv35(1 teal_oil), Lv50(2 teal_oil)

  Assailant
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 5000 + (level*120) + (floor(level/5)*8.4)*(level*10)
      Level 1 = 5,120
      Level 50 = 53,000
    Base ATK = 380 + (level*14) + (floor(level/5)*5.6)*level
      Level 1 = 394
      Level 50 = 3,880
    Base DEF = 90 + (level*10) + (floor(level/5)*6.7)*level
      Level 1 = 100
      Level 50 = 3,940
    Weapon = Damascus Sword
    Attack Pattern = Avoid, Jump, Dash, Heavy Crash, Slash I, Blow, Thrust
    Attack Base Damage = 0, 0, 0, 150%, 90%, 240%, 110%
    Attack CD = 0, 0, 2, 2, 1.5, 2.5, 1.75
    Attack Distance = 0, 0, 0, 250, 250, 300, 450
    XP = (ceil((level+1)/5)*35)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 red_oil), Lv25(2 red_oil), Lv35(3 red_oil), Lv50(1 magenta_oil)

  Accessory
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 4000 + (level*100) + (floor(level/5)*8)*(level*10)
      Level 1 = 4,100
      Level 50 = 49,000
    Base ATK = 380 + (level*15) + (floor(level/5)*6.3)*level
      Level 1 = 395
      Level 50 = 3,880
    Base DEF = 70 + (level*8) + (floor(level/5)*6)*level
      Level 1 = 78
      Level 50 = 3,470
    Weapon = Damascus Sword
    Attack Pattern = Avoid, Jump, Dash, Heavy Crash, Slash I, Blow, Dart Throw
    Attack Base Damage = 0, 0, 0, 150%, 90%, 240%, 50%
    Attack CD = 0, 0, 2, 2, 1.5, 2.5, 5
    Attack Distance = 0, 0, 0, 250, 250, 300, 450
    XP = (ceil((level+1)/5)*35)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 lime_oil), Lv25(2 lime_oil), Lv35(3 lime_oil), Lv50(1 springgreen_oil)

  Dew Smasher v3
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 3500 + (level*90) + (floor(level/5)*8)*(level*10)
      Level 1 = 3,590
      Level 50 = 48,000
    Base ATK = 420 + (level*17) + (floor(level/5)*6)*level
      Level 1 = 437
      Level 50 = 4,270
    Base DEF = 90 + (level*10) + (floor(level/5)*6.7)*level
      Level 1 = 100
      Level 50 = 3,940
    Attack Pattern = Avoid, Jump, Dash, Crash, Smash
    Attack Base Damage = 0, 0, 0, 100%, 170%
    Attack CD = 0, 0, 2, 1, 2
    Attack Distance = 0, 0, 0, 200, 200
    Passive Skill = Acid Infusion (place_meeting--water, -1), Immunity (Slow)
    XP = (ceil((level+1)/5)*30)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 blue_oil), Lv25(2 blue_oil), Lv35(3 blue_oil), Lv50(1 aqua_oil)

  Dew Chaser v3
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 6000 + (level*130) + (floor(level/5)*9.5)*(level*10)
      Level 1 = 6,130
      Level 50 = 60,000
    Base ATK = 380 + (level*14) + (floor(level/5)*5.6)*level
      Level 1 = 394
      Level 50 = 3,880
    Base DEF = 70 + (level*10) + (floor(level/5)*6.5)*level
      Level 1 = 80
      Level 50 = 3,820
    Attack Pattern = Avoid, Jump, Dash, Crash, Heavy Crash
    Attack Base Damage = 0, 0, 0, 110%, 150%
    Attack CD = 0, 0, 2, 1, 2
    Attack Distance = 0, 0, 0, 200, 250
    Passive Skill = Chase Lv3 (1600), Acid Infusion (place_meeting--water, -1), Immunity (Slow)
    XP = (ceil((level+1)/5)*30)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 blue_oil), Lv25(2 blue_oil), Lv35(3 blue_oil), Lv50(1 dodgerblue_oil)

  Dew Battler v3
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 5000 + (level*120) + (floor(level/5)*8.4)*(level*10)
      Level 1 = 5,120
      Level 50 = 53,000
    Base ATK = 420 + (level*15) + (floor(level/5)*6)*level
      Level 1 = 435
      Level 50 = 4,170
    Base DEF = 120 + (level*11) + (floor(level/5)*6.7)*level
      Level 1 = 131
      Level 50 = 4,020
    Weapon = Damascus Sword
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow, Thrust, Twin-Thrust
    Attack Base Damage = 0, 0, 0, 90%, 240%, 110%, 2x60%
    Attack CD = 0, 0, 2, 1.5, 2.5, 1.75, 2
    Attack Distance = 0, 0, 0, 250, 300, 450, 300
    Passive Skill = Acid Infusion (place_meeting--water, -1), Immunity (Slow)
    XP = (ceil((level+1)/5)*35)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 lime_oil), Lv25(2 lime_oil), Lv35(3 lime_oil), Lv50(1 springgreen_oil)

  Dew Shooter v3
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 2500 + (level*100) + (floor(level/5)*7.5)*(level*10)
      Level 1 = 2,600
      Level 50 = 45,000
    Base ATK = 380 + (level*15) + (floor(level/5)*6.5)*level
      Level 1 = 395
      Level 50 = 4,380
    Base DEF = 70 + (level*8) + (floor(level/5)*5.8)*level
      Level 1 = 78
      Level 50 = 3,370
    Weapon = Longbow
    Attack Pattern = Avoid, Jump, Dash, O-Shot I, Heavy Shot, Multi Shot, Parallel Shot
    Attack Base Damage = 0, 0, 0, 80%, 230%, 3x60%, 3x70%
    Attack CD = 0, 0, 2, 1.5, 2.5, 2, 3
    Attack Distance = 0, 0, 0, 550, 550, 550, 550, 550
    Passive Skill = Acid Infusion (place_meeting--water, -1), Immunity (Slow)
    XP = (ceil((level+1)/5)*35)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 lime_oil), Lv25(2 lime_oil), Lv35(3 lime_oil), Lv50(1 teal_oil)

  Skidder
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 6000 + (level*130) + (floor(level/5)*9.5)*(level*10)
      Level 1 = 6,130
      Level 50 = 60,000
    Base ATK = 400 + (level*15) + (floor(level/5)*5.8)*level
      Level 1 = 415
      Level 50 = 4,050
    Base DEF = 90 + (level*10) + (floor(level/5)*6.7)*level
      Level 1 = 100
      Level 50 = 3,940
    Attack Pattern = Avoid, Jump, Dash, Heavy Crash, Sliding Crash
    Attack Base Damage = 0, 0, 0, 150%, 250%
    Attack CD = 0, 0, 2, 3
    Attack Distance = 0, 0, 0, 250, 500
    Passive Skill = Acid Infusion (place_meeting--water, -1), Immunity (Slow)
    XP = (ceil((level+1)/5)*35)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 red_oil), Lv25(2 red_oil), Lv35(3 red_oil), Lv50(1 electricindigo_oil)

  Hydrologist
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 3000 + (level*80) + (floor(level/5)*7)*(level*10)
      Level 1 = 3,080
      Level 50 = 42,000
    Base ATK = 300 + (level*13) + (floor(level/5)*5)*level
      Level 1 = 313
      Level 50 = 3,450
    Base DEF = 70 + (level*9) + (floor(level/5)*6)*level
      Level 1 = 79
      Level 50 = 3,520
    Attack Pattern = Avoid, Jump, Dash, Dart Throw
    Attack Base Damage = 0, 0, 0, 70%
    Attack CD = 0, 0, 2, 2
    Attack Distance = 0, 0, 0, 400
    Passive Skill = Acid Infusion (place_meeting--water, -1), Immunity (Slow), Pacifist (hit--IMG-Bot)
    Skill List = Healing Area (2%~10s), HealPot Throw (5%~3s)
    Skill Damage = 0, 0
    Skill CD = 15, 20
    Skill Distance = 9999, 1000
    XP = (ceil((level+1)/5)*30)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 red_oil), Lv25(2 red_oil), Lv35(3 red_oil), Lv50(1 magenta_oil)

  Poacher v3
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 5000 + (level*120) + (floor(level/5)*8.4)*(level*10)
      Level 1 = 5,120
      Level 50 = 53,000
    Base ATK = 420 + (level*15) + (floor(level/5)*6)*level
      Level 1 = 435
      Level 50 = 4,170
    Base DEF = 120 + (level*11) + (floor(level/5)*6.7)*level
      Level 1 = 131
      Level 50 = 4,020
    Weapon = Damascus Sword
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow, Thrust, Twin-Thrust
    Attack Base Damage = 0, 0, 0, 90%, 240%, 110%, 2x60%
    Attack CD = 0, 0, 2, 1.5, 2.5, 1.75, 2
    Attack Distance = 0, 0, 0, 250, 300, 450, 300
    Passive Skill = Laceration (0), Chase (1000)
    XP = (ceil((level+1)/5)*30)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 blue_oil), Lv25(2 blue_oil), Lv35(3 blue_oil), Lv50(1 teal_oil)

  Forest Trespasser v3
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 6000 + (level*130) + (floor(level/5)*9.5)*(level*10)
      Level 1 = 6,130
      Level 50 = 60,000
    Base ATK = 390 + (level*14) + (floor(level/5)*5.8)*level
      Level 1 = 404
      Level 50 = 3,990
    Base DEF = 90 + (level*10) + (floor(level/5)*6.5)*level
      Level 1 = 100
      Level 50 = 3,810
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow, Thrust
    Attack Base Damage = 0, 0, 0, 60%, 180%, 80%
    Attack CD = 0, 0, 2, 1.5, 2.5, 2
    Attack Distance = 0, 0, 0, 250, 250, 400
    Passive Skill = Laceration (0), Chase (1000)
    XP = (ceil((level+1)/5)*30)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 red_oil), Lv25(2 red_oil), Lv35(3 red_oil), Lv50(1 orange_oil)

  Illegal Logger v3
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 6000 + (level*130) + (floor(level/5)*9.5)*(level*10)
      Level 1 = 6,130
      Level 50 = 60,000
    Base ATK = 400 + (level*15) + (floor(level/5)*5.8)*level
      Level 1 = 415
      Level 50 = 4,050
    Base DEF = 90 + (level*10) + (floor(level/5)*6.5)*level
      Level 1 = 100
      Level 50 = 3,840
    Weapon = Crosscut Saw
    Attack Pattern = Avoid, Jump, Dash, Crash, Heavy Crash, Slash
    Attack Base Damage = 0, 0, 0, 110%, 150%, 130%
    Attack CD = 0, 0, 3, 2, 2, 2
    Attack Distance = 0, 0, 0, 250, 250, 250
    Passive Skill = Laceration (0-1), Chase (1000)
    XP = (ceil((level+1)/5)*35)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 red_oil), Lv25(2 red_oil), Lv35(3 red_oil), Lv50(1 olive_oil)

  Illegal Hunter v3
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 2500 + (level*100) + (floor(level/5)*7.5)*(level*10)
      Level 1 = 2,600
      Level 50 = 45,000
    Base ATK = 380 + (level*15) + (floor(level/5)*6.5)*level
      Level 1 = 395
      Level 50 = 4,380
    Base DEF = 70 + (level*8) + (floor(level/5)*5.8)*level
      Level 1 = 78
      Level 50 = 3,370
    Weapon = Air Rifle
    Attack Pattern = Avoid, Jump, Dash, O-Shot, Heavy Shot, A-Shot
    Attack Base Damage = 0, 0, 0, 120%, 270%, 120%
    Attack CD = 0, 0, 3, 3, 4, 4
    Attack Distance = 0, 0, 0, 600, 600, 600
    Passive Skill = Laceration (1), Chase (1000)
    XP = (ceil((level+1)/5)*30)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 lime_oil), Lv25(2 lime_oil), Lv35(3 lime_oil), Lv50(1 yellow_oil)

  Illegal Lumberjack
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 6000 + (level*130) + (floor(level/5)*9.5)*(level*10)
      Level 1 = 6,130
      Level 50 = 60,000
    Base ATK = 430 + (level*16) + (floor(level/5)*6.1)*level
      Level 1 = 446
      Level 50 = 4,280
    Base DEF = 150 + (level*12) + (floor(level/5)*7)*level
      Level 1 = 162
      Level 50 = 4,250
    Weapon = Felling Axe
    Attack Pattern = Avoid, Jump, Dash, Slash, Blow, Uppercut, Chop
    Attack Base Damage = 0, 0, 0, 140%, 250%, 180%, 280%
    Attack CD = 0, 0, 3, 3, 4, 4, 5
    Attack Distance = 0, 0, 0, 250, 300, 300, 300
    Passive Skill = Laceration (1-2), Chase (1000)
    XP = (ceil((level+1)/5)*35)*xp_mul
    Item Drop = Lv5(1 colorless_oil), Lv10(2 colorless_oil), Lv15(1 blue_oil), Lv25(2 blue_oil), Lv35(3 blue_oil), Lv50(1 dodgerblue_oil)

  Nightfall Smasher v3
  Nightfall Chaser v3
  Nightfall Battler v3
  Nightfall Shooter v3
  Mage Apprentice
  Shaman
  //Blocker
  //Raider
  //Gunner


Elite
-----
  Ferroller
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 10000 + (level*150) + (floor(level/5)*10.5)*(level*10)
      Level 1 = 10,150
      Level 50 = 70,000
    Base ATK = 450 + (level*17) + (floor(level/5)*6)*level
      Level 1 = 467
      Level 50 = 4,300
    Base DEF = 100 + (level*12) + (floor(level/5)*7)*level
      Level 1 = 112
      Level 50 = 4,200
    Attack Pattern = Avoid, Jump, Dash, Crash, Heavy Crash, Rolling Crash
    Attack Base Damage = 0, 0, 0, 120%, 170%, 250%
    Attack CD = 0, 0, 2, 1, 2, 3
    Attack Distance = 0, 0, 0, 250, 300, 600
    Passive Skill = Initial Super-Armor (30%)
    XP = (ceil((level+1)/5)*50)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 red_oil), Lv15(3 red_oil), Lv20(4 red_oil), Lv35(500 coin, 1 orange_oil), Lv50(2 orange_oil, 3 crystal)

  Crusher
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 8000 + (level*130) + (floor(level/5)*9.5)*(level*10)
      Level 1 = 8,130
      Level 50 = 62,000
    Base ATK = 550 + (level*22) + (floor(level/5)*6.5)*level
      Level 1 = 572
      Level 50 = 4,900
    Base DEF = 130 + (level*12) + (floor(level/5)*7)*level
      Level 1 = 142
      Level 50 = 4,230
    Attack Pattern = Avoid, Jump, Dash, Crash, Smash, Heavy Smash
    Attack Base Damage = 0, 0, 0, 110%, 190%, 300%
    Attack CD = 0, 0, 2, 1, 2, 5
    Attack Distance = 0, 0, 0, 250, 250, 400
    Passive Skill = Super-Armor Resfresh (Heavy Smash, 10%)
    XP = (ceil((level+1)/5)*60)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 blue_oil), Lv15(3 blue_oil), Lv20(4 blue_oil), Lv35(500 coin, 1 dodgerblue_oil), Lv50(2 dodgerblue_oil, 3 crystal)

  Trailblazer
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 12000 + (level*200) + (floor(level/5)*10)*(level*10)
      Level 1 = 12,200
      Level 50 = 72,000
    Base ATK = 490 + (level*19) + (floor(level/5)*6.2)*level
      Level 1 = 509
      Level 50 = 4,540
    Base DEF = 100 + (level*11) + (floor(level/5)*6.8)*level
      Level 1 = 111
      Level 50 = 4,050
    Attack Pattern = Avoid, Jump, Dash, Crash, Heavy Crash, Snap Crash
    Attack Base Damage = 0, 0, 0, 120%, 170%, 120%
    Attack CD = 0, 0, 1, 1, 2, 2
    Attack Distance = 0, 0, 0, 250, 300, 400
    Passive Skill = Inescapable Sight (5000)
    XP = (ceil((level+1)/5)*60)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 lime_oil), Lv15(3 lime_oil), Lv20(4 lime_oil), Lv35(500 coin, 1 olive_oil), Lv50(2 olive_oil, 3 crystal)

  Salvager
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 6500 + (level*120) + (floor(level/5)*8.5)*(level*10)
      Level 1 = 6,620
      Level 50 = 55,000
    Base ATK = 400 + (level*17) + (floor(level/5)*5.5)*level
      Level 1 = 417
      Level 50 = 4,000
    Base DEF = 100 + (level*11) + (floor(level/5)*6.5)*level
      Level 1 = 111
      Level 50 = 3,900
    Attack Pattern = Avoid, Jump, Dash, Crash, Heavy Crash
    Attack Base Damage = 0, 0, 0, 120%, 170%
    Attack CD = 0, 0, 2, 1, 2
    Attack Distance = 0, 0, 0, 250, 300
    Skill List = Lv3 Healing Potion (4%~5s), HealPot Throw (5%~3s)
    Skill Damage = 0, 0
    Skill CD = 10, 20
    Skill Distance = 9999, 1000
    XP = (ceil((level+1)/5)*60)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 lime_oil), Lv15(3 lime_oil), Lv20(4 lime_oil), Lv35(500 coin, 1 chartreuse_oil), Lv50(2 chartreuse_oil, 3 crystal)

  Bruiser
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 10500 + (level*160) + (floor(level/5)*9.5)*(level*10)
      Level 1 = 10,660
      Level 50 = 66,000
    Base ATK = 530 + (level*20) + (floor(level/5)*6.5)*level
      Level 1 = 550
      Level 50 = 4,780
    Base DEF = 170 + (level*13) + (floor(level/5)*7.3)*level
      Level 1 = 183
      Level 50 = 4,470
    Weapon = Damascus Sword
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow, Thrust, Twin-Thrust, Uppercut
    Attack Base Damage = 0, 0, 0, 100%, 260%, 120%, 2x70%, 160%
    Attack CD = 0, 0, 2, 1.5, 2.5, 1.75, 2, 2.5
    Attack Distance = 0, 0, 0, 250, 300, 450, 300, 300
    Skill List = ThSlSlBl
    Skill Damage = 50% + 40% + 60% + 200%
    Skill CD = 7
    Skill Distance = 400
    XP = (ceil((level+1)/5)*70)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 red_oil), Lv15(3 red_oil), Lv20(4 red_oil), Lv35(500 coin, 1 yellow_oil), Lv50(2 yellow_oil, 3 crystal)

  Sharpshooter
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 5000 + (level*140) + (floor(level/5)*8)*(level*10)
      Level 1 = 5,140
      Level 50 = 52,000
    Base ATK = 480 + (level*20) + (floor(level/5)*7)*level
      Level 1 = 500
      Level 50 = 4,980
    Base DEF = 100 + (level*10) + (floor(level/5)*6.3)*level
      Level 1 = 110
      Level 50 = 3,750
    Weapon = Longbow
    Attack Pattern = Avoid, Jump, Dash, O-Shot I, Heavy Shot, Multi Shot, Parallel Shot, A-Shot I
    Attack Base Damage = 0, 0, 0, 90%, 250%, 4x70%, 4x80%, 90%
    Attack CD = 0, 0, 2, 1.5, 2.5, 2, 3, 2
    Attack Distance = 0, 0, 0, 550, 550, 550, 550, 550
    Skill List = OsOsMsHs
    Skill Damage = 40% + 60% + 4x25% + 200%
    Skill CD = 10
    Skill Distance = 550
    XP = (ceil((level+1)/5)*70)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 blue_oil), Lv15(3 blue_oil), Lv20(4 blue_oil), Lv35(500 coin, 1 deeppink_oil), Lv50(2 deeppink_oil, 3 crystal)

  Vicious Bandit
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 9000 + (level*150) + (floor(level/5)*9)*(level*10)
      Level 1 = 9,150
      Level 50 = 61,500
    Base ATK = 490 + (level*19) + (floor(level/5)*6.2)*level
      Level 1 = 509
      Level 50 = 4,540
    Base DEF = 100 + (level*12) + (floor(level/5)*6.6)*level
      Level 1 = 112
      Level 50 = 4,000
    Weapon = Damascus Knife
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow, Thrust, Stab I
    Attack Base Damage = 0, 0, 0, 70%, 200%, 90%, 240%
    Attack CD = 0, 0, 2, 1.5, 2.5, 1.75, 3
    Attack Distance = 0, 0, 0, 250, 250, 400, 250
    Passive Skill = Rage v1 (HP <= 50%, 50%, -2)
    XP = (ceil((level+1)/5)*60)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 red_oil), Lv15(3 red_oil), Lv20(4 red_oil), Lv35(500 coin, 1 purple_oil), Lv50(2 purple_oil, 3 crystal)

  Rigid Tank
    xp_mul = 1, 1, 1, 2, 3
    Base Max HP = 50000 + (level*1200) + (floor(level/5)*28)*(level*10)
      Level 1 = 51,200
      Level 50 = 250,000
    Base ATK = 1000 + (level*40) + (floor(level/5)*8)*level
      Level 1 = 1,040
      Level 50 = 7,000
    Base DEF = 120 + (level*13) + (floor(level/5)*6.7)*level
      Level 1 = 133
      Level 50 = 4,120
    Attack Pattern = Avoid, Heavy Crash, Heavy Smash
    Attack Base Damage = 0, 170%, 300%
    Attack CD = 0, 2, 5
    Attack Distance = 0, 300, 400
    Passive Skill = Pacifist (HP <= 35%), Rage v2 (!Pacifist, IGN INT = 80%, DEF = 0, DMG INP = 30%)
    XP = (ceil((level+1)/5)*80)*xp_mul
    Item Drop = Lv5(3 blue_oil), Lv10(4 blue_oil), Lv15(1 teal_oil), Lv25(2 teal_oil), Lv35(1000 coin, 3 teal_oil), Lv50(1 navyblue_oil, 5 crystal)

  Twisted Assailant
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 10000 + (level*160) + (floor(level/5)*9.5)*(level*10)
      Level 1 = 10,160
      Level 50 = 65,500
    Base ATK = 500 + (level*20) + (floor(level/5)*6)*level
      Level 1 = 520
      Level 50 = 4,500
    Base DEF = 130 + (level*13) + (floor(level/5)*7)*level
      Level 1 = 143
      Level 50 = 4,280
    Weapon = Damascus Sword
    Attack Pattern = Avoid, Jump, Dash, Heavy Crash, Slash I, Blow, Thrust, Air Blow A
    Attack Base Damage = 0, 0, 0, 170%, 100%, 260%, 120%, 280%
    Attack CD = 0, 0, 2, 2, 1.5, 2.5, 1.75, 3
    Attack Distance = 0, 0, 0, 300, 300, 350, 500, 350
    Skill List = ThSlUpAbl
    Skill Damage = 50% + 60% + 80% + 210%
    Skill CD = 9
    Skill Distance = 400
    XP = (ceil((level+1)/5)*70)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 red_oil), Lv15(3 red_oil), Lv20(4 red_oil), Lv35(500 coin, 1 magenta_oil), Lv50(2 magenta_oil, 3 crystal)

  Wacky Accessory
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 8000 + (level*140) + (floor(level/5)*9)*(level*10)
      Level 1 = 8,140
      Level 50 = 60,000
    Base ATK = 500 + (level*20) + (floor(level/5)*7)*level
      Level 1 = 520
      Level 50 = 5,000
    Base DEF = 100 + (level*11) + (floor(level/5)*6.3)*level
      Level 1 = 111
      Level 50 = 3,800
    Weapon = Damascus Sword
    Attack Pattern = Avoid, Jump, Dash, Heavy Crash, Slash I, Blow, Serum Throw
    Attack Base Damage = 0, 0, 0, 170%, 100%, 260%, 70%
    Attack CD = 0, 0, 2, 2, 1.5, 2.5, 1.75, 5
    Attack Distance = 0, 0, 0, 300, 300, 350, 500
    Skill List = Group Determination (20%, 8)
    Skill Damage = 0
    Skill CD = 15
    Skill Distance = 450
    XP = (ceil((level+1)/5)*70)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 lime_oil), Lv15(3 lime_oil), Lv20(4 lime_oil), Lv35(500 coin, 1 springgreen_oil), Lv50(2 springgreen_oil, 3 crystal)

  Dew-drop Crusher
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 8000 + (level*130) + (floor(level/5)*9.5)*(level*10)
      Level 1 = 8,130
      Level 50 = 62,000
    Base ATK = 550 + (level*22) + (floor(level/5)*6.5)*level
      Level 1 = 572
      Level 50 = 4,900
    Base DEF = 130 + (level*12) + (floor(level/5)*7)*level
      Level 1 = 142
      Level 50 = 4,230
    Attack Pattern = Avoid, Jump, Dash, Crash, Smash, Heavy Smash
    Attack Base Damage = 0, 0, 0, 110%, 190%, 300%
    Attack CD = 0, 0, 2, 1, 2, 5
    Attack Distance = 0, 0, 0, 250, 250, 400
    Passive Skill = Super-Armor Resfresh (Heavy Smash, 10%), Acid Infusion (place_meeting--water, -1), Immunity (Slow)
    Skill List = Attack Infusion (Acid, 3)
    Skill Damage = 0
    Skill CD = 10
    Skill Distance = 9999
    XP = (ceil((level+1)/5)*70)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 blue_oil), Lv15(3 blue_oil), Lv20(4 blue_oil), Lv35(500 coin, 1 aqua_oil), Lv50(2 aqua_oil, 3 crystal)

  Dew-drop Trailblazer
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 12000 + (level*200) + (floor(level/5)*10)*(level*10)
      Level 1 = 12,200
      Level 50 = 72,000
    Base ATK = 490 + (level*19) + (floor(level/5)*6.2)*level
      Level 1 = 509
      Level 50 = 4,540
    Base DEF = 100 + (level*11) + (floor(level/5)*6.8)*level
      Level 1 = 111
      Level 50 = 4,050
    Attack Pattern = Avoid, Jump, Dash, Crash, Heavy Crash, Snap Crash
    Attack Base Damage = 0, 0, 0, 120%, 170%, 120%
    Attack CD = 0, 0, 1, 1, 2, 2
    Attack Distance = 0, 0, 0, 250, 300, 400
    Passive Skill = Inescapable Sight (5000), Acid Infusion (place_meeting--water, -1), Immunity (Slow)
    Skill List = Attack Infusion (Acid, 3)
    Skill Damage = 0
    Skill CD = 10
    Skill Distance = 9999
    XP = (ceil((level+1)/5)*70)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 blue_oil), Lv15(3 blue_oil), Lv20(4 blue_oil), Lv35(500 coin, 1 dodgerblue_oil), Lv50(2 dodgerblue_oil, 3 crystal)

  Dew-drop Bruiser
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 10500 + (level*160) + (floor(level/5)*9.5)*(level*10)
      Level 1 = 10,660
      Level 50 = 66,000
    Base ATK = 530 + (level*20) + (floor(level/5)*6.5)*level
      Level 1 = 550
      Level 50 = 4,780
    Base DEF = 170 + (level*13) + (floor(level/5)*7.3)*level
      Level 1 = 183
      Level 50 = 4,470
    Weapon = Damascus Sword
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow, Thrust, Twin-Thrust, Uppercut
    Attack Base Damage = 0, 0, 0, 100%, 260%, 120%, 2x70%, 160%
    Attack CD = 0, 0, 2, 1.5, 2.5, 1.75, 2, 2.5
    Attack Distance = 0, 0, 0, 250, 300, 450, 300, 300
    Passive Skill = Acid Infusion (place_meeting--water, -1), Immunity (Slow)
    Skill List = ThSlSlBl, Attack Infusion (Acid, 3)
    Skill Damage = 50% + 40% + 60% + 200%, 0
    Skill CD = 7, 10
    Skill Distance = 400, 9999
    XP = (ceil((level+1)/5)*80)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 lime_oil), Lv15(3 lime_oil), Lv20(4 lime_oil), Lv35(500 coin, 1 springgreen_oil), Lv50(2 springgreen_oil, 3 crystal)

  Dew-drop Sharpshooter
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 5000 + (level*140) + (floor(level/5)*8)*(level*10)
      Level 1 = 5,140
      Level 50 = 52,000
    Base ATK = 480 + (level*20) + (floor(level/5)*7)*level
      Level 1 = 500
      Level 50 = 4,980
    Base DEF = 100 + (level*10) + (floor(level/5)*6.3)*level
      Level 1 = 110
      Level 50 = 3,750
    Weapon = Longbow
    Attack Pattern = Avoid, Jump, Dash, O-Shot I, Heavy Shot, Multi Shot, Parallel Shot, A-Shot I
    Attack Base Damage = 0, 0, 0, 90%, 250%, 4x70%, 4x80%, 90%
    Attack CD = 0, 0, 2, 1.5, 2.5, 2, 3, 2
    Attack Distance = 0, 0, 0, 550, 550, 550, 550, 550
    Passive Skill = Acid Infusion (place_meeting--water, -1), Immunity (Slow)
    Skill List = OsOsMsHs, Attack Infusion (Acid, 3)
    Skill Damage = 40% + 60% + 4x25% + 200%, 0
    Skill CD = 10, 10
    Skill Distance = 550, 9999
    XP = (ceil((level+1)/5)*80)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 lime_oil), Lv15(3 lime_oil), Lv20(4 lime_oil), Lv35(500 coin, 1 teal_oil), Lv50(2 teal_oil, 3 crystal)

  Watertight Skidder
    xp_mul = 1, 1, 1, 1.5, 2
    Base Max HP = 12000 + (level*200) + (floor(level/5)*10.6)*(level*10)
      Level 1 = 12,200
      Level 50 = 75,000
    Base ATK = 510 + (level*20) + (floor(level/5)*6.3)*level
      Level 1 = 530
      Level 50 = 4,660
    Base DEF = 130 + (level*12) + (floor(level/5)*7)*level
      Level 1 = 142
      Level 50 = 4,230
    Attack Pattern = Avoid, Jump, Dash, Heavy Crash, Sliding Crash, Rolling Crash
    Attack Base Damage = 0, 0, 0, 170%, 270%, 320%
    Attack CD = 0, 0, 2, 3, 5
    Attack Distance = 0, 0, 0, 300, 550, 650
    Passive Skill = Acid Infusion (place_meeting--water, -1), Immunity (Slow)
    Skill List = Attack Infusion (Acid, 3), Drenching Smash
    Skill Damage = 0, 200% + 300%
    Skill CD = 10, 25
    Skill Distance = 9999, 700
    XP = (ceil((level+1)/5)*80)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 red_oil), Lv15(3 red_oil), Lv20(4 red_oil), Lv35(500 coin, 1 electricindigo_oil), Lv50(2 electricindigo_oil, 3 crystal)

  Skilled Hydrologist
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 6500 + (level*120) + (floor(level/5)*8.5)*(level*10)
      Level 1 = 6,620
      Level 50 = 55,000
    Base ATK = 400 + (level*17) + (floor(level/5)*5.5)*level
      Level 1 = 417
      Level 50 = 4,000
    Base DEF = 100 + (level*11) + (floor(level/5)*6.5)*level
      Level 1 = 111
      Level 50 = 3,900
    Attack Pattern = Avoid, Jump, Dash, Dart Throw, Serum Throw
    Attack Base Damage = 0, 0, 0, 80%, 80%
    Attack CD = 0, 0, 2, 2, 2
    Attack Distance = 0, 0, 0, 400, 400
    Passive Skill = Acid Infusion (place_meeting--water, -1), Immunity (Slow), Pacifist (hit--IMG-Bot), Buff (place_meeting--water, CD RDC, 25%, -1)
    Skill List = Lv2 Healing Area (3%~10s), Lv2 HealPot Throw (8%~3s), place_meeting--water[Purification (water, 3%~10s--0.5s)]
    Skill Damage = 0, 0, 0
    Skill CD = 15, 20, 40
    Skill Distance = 9999, 1000, 9999
    XP = (ceil((level+1)/5)*70)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 red_oil), Lv15(3 red_oil), Lv20(4 red_oil), Lv35(500 coin, 1 magenta_oil), Lv50(2 magenta_oil, 3 crystal)

  Outlawed Poacher
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 10500 + (level*160) + (floor(level/5)*9.5)*(level*10)
      Level 1 = 10,660
      Level 50 = 66,000
    Base ATK = 530 + (level*20) + (floor(level/5)*6.5)*level
      Level 1 = 550
      Level 50 = 4,780
    Base DEF = 170 + (level*13) + (floor(level/5)*7.3)*level
      Level 1 = 183
      Level 50 = 4,470
    Weapon = Damascus Sword
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow, Thrust, Twin-Thrust, Uppercut
    Attack Base Damage = 0, 0, 0, 100%, 260%, 120%, 2x70%, 160%
    Attack CD = 0, 0, 2, 1.5, 2.5, 1.75, 2, 2.5
    Attack Distance = 0, 0, 0, 250, 300, 450, 300, 300
    Passive Skill = Laceration (0), Chase (1000), Initial Super-Armor (35%)
    Skill List = ThSlUpAbl, Group Determination
    Skill Damage = 50% + 60% + 80% + 210%, 0
    Skill CD = 9, 15
    Skill Distance = 400, 550
    XP = (ceil((level+1)/5)*70)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 blue_oil), Lv15(3 blue_oil), Lv20(4 blue_oil), Lv35(500 coin, 1 teal_oil), Lv50(2 teal_oil, 3 crystal)

  Outlawed Trespasser
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 12000 + (level*200) + (floor(level/5)*10)*(level*10)
      Level 1 = 12,200
      Level 50 = 72,000
    Base ATK = 505 + (level*19) + (floor(level/5)*6.35)*level
      Level 1 = 524
      Level 50 = 4,630
    Base DEF = 120 + (level*12) + (floor(level/5)*7)*level
      Level 1 = 132
      Level 50 = 4,220
    Weapon = Damascus Knife
    Attack Pattern = Avoid, Jump, Dash, Slash I, Blow, Thrust, Stab I
    Attack Base Damage = 0, 0, 0, 70%, 200%, 90%, 240%
    Attack CD = 0, 0, 2, 1.5, 2.5, 1.75, 3
    Attack Distance = 0, 0, 0, 250, 250, 400, 250
    Passive Skill = Laceration (0), Chase (1000), Initial Super-Armor (25%)
    Skill List = Boost (ATK SPD, 30%, 10)
    Skill Damage = 0
    Skill CD = 15
    Skill Distance = 9999
    XP = (ceil((level+1)/5)*70)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 blue_oil), Lv15(3 blue_oil), Lv20(4 blue_oil), Lv35(500 coin, 1 teal_oil), Lv50(2 teal_oil, 3 crystal)

  Outlawed Logger
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 12000 + (level*200) + (floor(level/5)*10)*(level*10)
      Level 1 = 12,200
      Level 50 = 72,000
    Base ATK = 510 + (level*20) + (floor(level/5)*6.4)*level
      Level 1 = 530
      Level 50 = 4,710
    Base DEF = 150 + (level*12) + (floor(level/5)*7.2)*level
      Level 1 = 162
      Level 50 = 4,350
    Weapon = Crosscut Saw
    Attack Pattern = Avoid, Jump, Dash, Crash, Heavy Crash, Slash, Slit
    Attack Base Damage = 0, 0, 0, 120%, 170%, 150%, 220%
    Attack CD = 0, 0, 3, 1, 2, 2, 3
    Attack Distance = 0, 0, 0, 250, 250, 250, 250
    Passive Skill = Laceration (0-2), Chase (1000), Initial Super-Armor (50%)
    Skill List = Harsh Slit
    Skill Damage = 450%
    Skill CD = 10
    Skill Distance = 450
    XP = (ceil((level+1)/5)*80)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 blue_oil), Lv15(3 blue_oil), Lv20(4 blue_oil), Lv35(500 coin, 1 teal_oil), Lv50(2 teal_oil, 3 crystal)

  Outlawed Hunter
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 5000 + (level*140) + (floor(level/5)*8)*(level*10)
      Level 1 = 5,140
      Level 50 = 52,000
    Base ATK = 480 + (level*20) + (floor(level/5)*7)*level
      Level 1 = 500
      Level 50 = 4,980
    Base DEF = 100 + (level*10) + (floor(level/5)*6.3)*level
      Level 1 = 110
      Level 50 = 3,750
    Weapon = Air Rifle
    Attack Pattern = Avoid, Jump, Dash, O-Shot, Heavy Shot, A-Shot, Critical Shot
    Attack Base Damage = 0, 0, 0, 120%, 270%, 120%, 300%
    Attack CD = 0, 0, 3, 3, 4, 4, 7
    Attack Distance = 0, 0, 0, 600, 600, 600, 600
    Passive Skill = Laceration (1), Chase (1000), Initial Super-Armor (20%)
    Skill List = Flick Shot (place_meeting--IMG-Bot_ATK)
    Skill Damage = 300%
    Skill CD = 10
    Skill Distance = 9999
    XP = (ceil((level+1)/5)*70)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 lime_oil), Lv15(3 lime_oil), Lv20(4 lime_oil), Lv35(500 coin, 1 yellow_oil), Lv50(2 yellow_oil, 3 crystal)

  Outlawed Lumberjack
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 12000 + (level*200) + (floor(level/5)*10)*(level*10)
      Level 1 = 12,200
      Level 50 = 72,000
    Base ATK = 600 + (level*23) + (floor(level/5)*6.7)*level
      Level 1 = 623
      Level 50 = 5,100
    Base DEF = 200 + (level*14) + (floor(level/5)*7.6)*level
      Level 1 = 214
      Level 50 = 4,700
    Weapon = Felling Axe
    Attack Pattern = Avoid, Jump, Dash, Slash, Blow, Uppercut, Chop, Heavy Chop
    Attack Base Damage = 0, 0, 0, 140%, 250%, 180%, 280%, 380%
    Attack CD = 0, 0, 3, 3, 4, 4, 5, 10
    Attack Distance = 0, 0, 0, 250, 300, 300, 300, 300
    Passive Skill = Laceration (1-4), Chase (1000), Initial Super-Armor (75%)
    Skill List = Groundsplitter Chop
    Skill Damage = 700%
    Skill CD = 15
    Skill Distance = 450
    XP = (ceil((level+1)/5)*80)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 blue_oil), Lv15(3 blue_oil), Lv20(4 blue_oil), Lv35(500 coin, 1 teal_oil), Lv50(2 dodgerblue_oil, 3 crystal)

  Forest Overseer
    xp_mul = 1, 1, 1, 1.5, 2.5
    Base Max HP = 6500 + (level*120) + (floor(level/5)*8.5)*(level*10)
      Level 1 = 6,620
      Level 50 = 55,000
    Base ATK = 400 + (level*17) + (floor(level/5)*5.5)*level
      Level 1 = 417
      Level 50 = 4,000
    Base DEF = 100 + (level*11) + (floor(level/5)*6.5)*level
      Level 1 = 111
      Level 50 = 3,900
    Attack Pattern = Avoid, Jump
    Attack Base Damage = 0, 0
    Attack CD = 0, 0
    Attack Distance = 0, 0
    Passive Skill = Chase (1000), Initial Super-Armor (100%), Abuse (HP <= 30%, ~99 Enemies, 50% DMG OTP, 30% CD RDC, 40% AGI, 5% HP Loss/s, 20s)
    Skill List = Provoke (IMG-Bot, 20% ATK SCL, 30% DMG INP, 7s), Ridicule (IMG-Bot, -35% DEF AMP, 7s), Offend (~2 Enemies, 30%, AGI, ATK SPD, 10s), Mock (~2 Enemies, 30% ATK SCL, 15% CRT BLD, -20% DEF SCL, 10s), Insult (~4 Enemies, 20% CRT PRT, -20% DMG INP, -10% HP, 10s)
    Skill Damage = 0, 0, 0, 0, 0
    Skill CD = 10, 10, 15, 15, 15
    Skill Distance = 9999, 9999, 9999, 9999, 9999
    XP = (ceil((level+1)/5)*70)*xp_mul
    Item Drop = Lv5(3 colorless_oil), Lv10(2 lime_oil), Lv15(3 lime_oil), Lv20(4 lime_oil), Lv35(500 coin, 1 springgreen_oil), Lv50(2 springgreen_oil, 3 crystal)

  Shadow Crusher
  Shadow Trailblazer
  Shadow Bruiser
  Shadow Sharpshooter
  Mage
  Elder Shaman
  Witch
  //Compact Blocker
  //Blaster


Boss
----
  Ferroller-X
    xp_mul = 1, 1, 1, 2, 3
    Base Max HP = 100000 + (level*2500) + (floor(level/5)*50)*(level*10)
      Level 1 = 102,500
      Level 50 = 475,000
    Base ATK = 550 + (level*20) + (floor(level/5)*7)*level
      Level 1 = 570
      Level 50 = 5,050
    Base DEF = 250 + (level*18) + (floor(level/5)*7)*level
      Level 1 = 268
      Level 50 = 4,650
    Attack Pattern = Avoid, Jump, Dash, Crash, Heavy Crash, Snap Crash, Rolling Crash, Smash, Heavy Smash
    Attack Base Damage = 0, 0, 0, 120%, 170%, 120%, 250%, 190%, 300%
    Attack CD = 0, 0, 2, 1, 2, 2, 3, 2, 5
    Attack Distance = 0, 0, 0, 250, 300, 400, 600, 250, 400
    Passive Skill = Initial Super-Armor (30%), Super-Armor Refresh (HP <= 50%, 50%)
    Skill List = Triple Smash, Boost (DMG INP, -15%, 7)
    Skill Damage = 100% + 150% + 250%, 0
    Skill CD = 20, 15
    Skill Distance = 300, 9999
    Ultimate List = Air Smash, Ext Boost (HP <= 50%, [ATK Scale, Agility, DMG Output], 20%, 20)
    Ultimate Damage = 60% + 40% + 600%, 0
    Ultimate CD = 60, 35
    Ultimate Distance = 300, 9999
    XP = (ceil((level+1)/5)*375)*xp_mul
    Item Drop = Lv5(500 coin, 3 red_oil), Lv10(500 coin, 4 red_oil), Lv15(1000 coin, 1 orange_oil), Lv25(1000 coin, 2 orange_oil), Lv35(3 orange_oil, 5 crystal), Lv50(1 orangered_oil, 10 crystal)

  Fluvial/Hoarfrost Hydrobot
    xp_mul = 1, 1, 1, 2, 3
    Base Max HP = 140000 + (level*4000) + (floor(level/5)*65)*(level*10)
      Level 1 = 144,000
      Level 50 = 665,000
    Base ATK = 650 + (level*23) + (floor(level/5)*7.4)*level
      Level 1 = 673
      Level 50 = 5,500
    Base DEF = 200 + (level*17) + (floor(level/5)*7)*level
      Level 1 = 217
      Level 50 = 4,550
    Weapon = [-]
             [Frost Sword]
    Attack Pattern = [Avoid, Jump, Dash, Heavy Crash, Snap Crash, Sliding Crash, Rolling Crash, Waterball Shot]
                     [Avoid, Jump, Dash, Slash, Blow, Thrust, Uppercut, Smash]
    Attack Base Damage = [0, 0, 0, 170%, 110%, 270%, 320%, 130%]
                         [0, 0, 0, 100%, 260%, 130%, 170%, 350%]
    Attack CD = [0, 0, 2, 2, 2, 3, 5, 3]
                [0, 0, 2, 2, 3, 2, 3, 5]
    Attack Distance = [0, 0, 0, 300, 400, 600, 650, 500]
                      [0, 0, 0, 350, 400, 650, 400, 750]
    Passive Skill = [Immunity (Slow), Acid Infusion (place_meeting--water, -1)]
                    [Immunity (Slow), //Chilly Air (Frozen System, ~30%, 200~600), //Defensive Icicle (temp_s-armor--break, 75%, ~4)]
    Skill List = [Slippery Body, Watermine Burst, Waterball Mortar]
                 [Frozen System, Icicle Thrust, Groundfreeze Uppercut]
    Skill Damage = [0, 500%, 300%]
                   [0, 4x150%, 3x200%]
    Skill CD = [10, 15, 20]
               [10, 15, 20]
    Skill Distance = [9999, 9999, 2500]
                     [9999, 9999, 1000]
    Ultimate List = [Waterball Barrage, Waterball Volley]
                    [Icicle Crush, Charged Snow Blast]
    Ultimate Damage = [9x50% + 250%, 30x35%]
                      [1200%, 350%~750% + 5x40% + 200%]
    Ultimate CD = [45, 60]
                  [60, 45]
    Ultimate Distance = [650, 9999]
                        [9999, 1000]
    XP = (ceil((level+1)/5)*600)*xp_mul
    Item Drop = Lv5(500 coin, 3 red_oil), Lv10(500 coin, 4 red_oil), Lv15(1000 coin, 1 electricindigo_oil), Lv25(1000 coin, 2 electricindigo_oil), Lv35(3 electricindigo_oil, 5 crystal), Lv50(1 hanpurple_oil, 10 crystal)

  Deforester Chief
    xp_mul = 1, 1, 1, 2, 3
    Base Max HP = 120000 + (level*3000) + (floor(level/5)*60)*(level*10)
      Level 1 = 123,000
      Level 50 = 570,000
    Base ATK = 650 + (level*25) + (floor(level/5)*7.8)*level
      Level 1 = 675
      Level 50 = 5,800
    Base DEF = 200 + (level*14) + (floor(level/5)*7.6)*level
      Level 1 = 214
      Level 50 = 4,700
    Weapon = [Felling Axe]
             [Chainsaw]
    Attack Pattern = [Avoid, Jump, Dash, Slash, Blow, Uppercut, Chop, Heavy Chop]
                     [Avoid, Jump, Dash, Slash, Blow, Thrust, Downward Cut]
    Attack Base Damage = [0, 0, 0, 150%, 270%, 200%, 300%, 410%]
                         [0, 0, 0, 120%, 220%, 170%, 300%]
    Attack CD = [0, 0, 3, 3, 3, 4, 4, 5, 10]
                [0, 0, 3, 3, 4, 3, 5]
    Attack Distance = [0, 0, 0, 250, 300, 300, 300, 300]
                      [0, 0, 0, 300, 300, 450, 300]
    Passive Skill = [Laceration (1-4), Initial Super-Armor (25%), Abuse (HP <= 16.67%, ~99 Enemies, 50% DMG OTP, 30% CD RDC, 40% AGI, 5% HP Loss/s, 20s)]
                    [Laceration (1-3), Initial Super-Armor (25%), Overheat (HP <= 20%, 20% DMG OTP, 30% CC PWR, Laceration +1, 0.34% HP Loss/s, 60s), Super-Armor Refresh (HP <= 20%, 15%)]
    Skill List = [Heavy Uppercut, Groundsplitter Chop, Astray Determination (30% AGI, 20% ATK SPD, 35% CRT DMG, 15s)]
                 [Activate Chainsaw (!overheat--force_use(HP <= 50%), 20% IGN INT, +15s), Groundsplitter Cut, Powered Saw --activated]
    Skill Damage = [100% + 400%, 800%, 0]
                   [0, 700%, 4x100% + 200%]
    Skill CD = [15, 20, 30]
               [15, 20, 20]
    Skill Distance = [600, 1000, 9999]
                     [9999, 1000, 400]
    Ultimate List = [Deforest Team (HP >= 25%--force_use(HP <= 20%), I-Logger, F-Trespasser, I-Hunter, Normal~Elite), Threatening Aura (IMG-Bot, -20% ATK SCL, -35% DEF SCL, -25% AGI, 15s)]
                    [Ruthless Roughcut, Defectless Split]
    Ultimate Damage = [0, 0]
                      [150% + 14x40% + 340%, 19x50% + 350%]
    Ultimate CD = [90, 45]
                  [60, 60]
    Ultimate Distance = [9999, 9999]
                        [1500, 400]
    XP = (ceil((level+1)/5)*600)*xp_mul
    Item Drop = Lv5(500 coin, 3 blue_oil), Lv10(500 coin, 4 blue_oil), Lv15(1000 coin, 1 dodgerblue_oil), Lv25(1000 coin, 2 dodgerblue_oil), Lv35(3 dodgerblue_oil, 5 crystal), Lv50(1 reblue_oil, 10 crystal)

  (Midnight) Phantom

Superboss
---------
  //Permafrost Cyrobot

Tyrant
------


Deskripsi Skill Musuh
----------------
  Passive
    Chase (dis) = ganti chase_dis menjadi dis
    Inescapable Sight (dis) = ganti chase_dis menjadi dis, CD dash -1s
    Initial Super-Armor (n%) = Super-Armor awal sebanyak n% dari Max HP
    Super-Armor Refresh (con, n%) = Isi Super-Armor setelah kondisi con terpenuhi sebesar n% dari Max HP
    Pacifist (con) = tidak akan menyerang sampai kondisi con terpenuhi
    Buff (con, stat, val, t) = tambah stat sebanyak val selama t detik setelah kondisi con terpenuhi
    Rage v1 (con, val, t) = tambah agility dan atk_spd sebanyak val selama t detik setelah kondisi con terpenuhi
    Rage v2 (con, stat = val) = tambah stat sebanyak val dengan permanen setelah kondisi con terpenuhi
    Immunity (type) = kebal terhadap debuff/cc type
    Acid Infusion (con, t) = ganti damage menjadi acid ketika kondisi con terpenuhi selama t detik
    Laceration (n) = tambah stack laceration sebanyak n saat memberikan serangan. setelah mencapai 5 stack, akan mengakibatkan 1% Max HP/s selama 5s

  Active
    Healing Potion (regen%~ts) = drop healing potion, HP regen sebanyak regen% selama t detik
    HP Throw (regen%~ts) = lempar healing potion ke target, HP regen sebanyak regen% selama t detik
    Boost (stat, val, t) = tambah stat sebanyak val selama t detik
    Ext Boost (con, stat, val, t) = tambah stat sebanyak val selama t detik setelah kondisi con terpenuhi
    -Group- Determination (val, t) = buff semua bot musuh dalam area, meningkatkan ATK dan DEF Scale sebanyak val selama t detik
    Attack Infusion (type, t) = ganti jenis damage menjadi type selama t detik (kalau sudah terganti, tambah DMG OTP sesuai jenis damage sebanyak 20% selama t detik)
    Purification (target, regen%~ts) = tambah efek HP regen pada target sebanyak regen% selama t detik jika kondisi con terpenuhi
