Game Modes
  Campaign                  -> Chapter 1, 2, ...
  Resource Node             -> Coin Production, Oil Extraction, Metal Mining, Mold Delineation, Crystal Cleansing
  // Challenge              -> Illusionary Hall, Tower of Willpower [, Unending Storm, Wayfarer's Legacy]
  // Story Modes            -> Main Story, Side Story, Tales of the Void
  // Co-op                  -> Joint Battles, Raids [, Infinite Legion, Guild's Legacy, Guardian Gauntlet, Unity Wars]
  // Arena                  -> Deathmatch, Team Deathmatch, Capture the Flag, Domination, Elimination
  // Phantasm Battleground
  Training Ground           -> Freeplay, Tutorial

Training Ground
  === Freeplay
  === Tutorial 
      --- Beginner -> Movement (Move, Jump, Sprint, Dash), Basic Combat (BATK, Critical/Grave Hit, DERV, SPMV, ULTI), Beginner Trial
      --- Advanced -> Status Ailment (Debuff, CC), Intermediate Combat (Dodge, Retaliate, Attack Interruption, Armor [, Execution]), Advanced Combat (Class Switch, Switch-In/Out, Joint Attack, Parallel Combat), Advanced Trial
      --- Class -> Warrior (DERV, SPMV, ULTI, INTG, UNIQ, EXT), ...



Chapter 1
  Type Mod = [0, 1, 2, 3]
  Stage 1
    Stage Type = 1 --area_battle
    Room W = 5000
    Room H = 1200
    Enemy = 3 + 2 Roller
    Enemy Level = 1
  Stage 2
    Stage Type = 1 --area_battle
    Room W = 7500
    Room H = 1200
    Enemy = 4 + 3 + 3 Roller
    Enemy Level = 1
  Stage 3
    Stage Type = 1 --area_battle
    Room W = 10000
    Room H = 1200
    Enemy = 5 + 3 + 3 + 4 Roller
    Enemy Level = 2
  Stage 4
    Stage Type = 1 --area_battle
    Room W = 12500
    Room H = 1200
    Initial Enemy = 1 Roller, 2 Smasher, 2 Chaser
    Spawn Enemy = (3 Roller)
                  (2 Roller, 1 Smasher)
                  (2 Roller, 1 Smasher, 1 Chaser)
    Enemy Level = 2
  Stage 5
    Stage Type = 1 --area_battle
    Room W = 15000
    Room H = 1200
    Initial Enemy = 1 Roller Type-II, 1 Smasher, 1 Chaser, 1 Smasher Type-II, 1 Chaser Type-II
    Spawn Enemy = (2 Roller, 1 Roller Type-II)
                  (2 Roller Type-II, 1 Smasher)
                  (2 Roller Type-II, 1 Smasher, 1 Chaser)
    Enemy Level = 3
  Stage 6
    Stage Type = 1 --area_battle
    Room W = 17500
    Room H = 1200
    Initial Enemy = 1 Roller Type-II, 1 Smasher Type-II, 1 Chaser Type-II, 2 Rescuer
    Spawn Enemy = (3 Roller Type-II, 1 Smasher Type-II)
                  (2 Roller Type-II, 2 Smasher Type-II, 1 Chaser Type-II)
                  (1 Roller Type-II, 2 Smasher Type-II, 2 Chaser Type-II, 1 Rescuer Type-II)
    Enemy Level = 3
  Stage 7
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1200
    Initial Enemy = 1 Battler, 2 Smasher Type-II, 2 Chaser Type-II, 2 Battler Type-II
    Spawn Enemy = (2 Roller Type-II, 1 Smasher Type-II, 1 Rescuer Type-II)
                  (2 Smasher Type-II, 2 Chaser Type-II)
                  (1 Roller Type-III, 1 Smasher Type-II, 2 Chaser Type-II, 1 Rescuer Type-II)
                  (2 Roller Type-III, 1 Chaser Type-II, 1 Rescuer Type-II, 1 Battler Type-II)
    Enemy Level = 3 - 4
  Stage 8
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1200
    Initialial Enemy = 1 Shooter, 2 Battler Type-II, 2 Smasher Type-III. 2 Chaser Type-III
    Spawn Enemy = (2 Roller Type-III, 1 Battler Type-II, 1 Rescuer Type-II)
                  (1 Chaser Type-II, 1 Battler Type-II, 2 Rescuer Type-II)
                  (1 Roller Type-III, 1 Smasher Type-II, 1 Battler Type-II, 1 Rescuer Type-II, 1 Shooter Type-II
                  (1 Smasher Type-III, 1 Chaser Type-III, 1 Battler Type-II, 1 Rescuer Type-II, 1 Shooter Type-II)
    Enemy Level = 4
  Stage 9
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1200
    Initial Enemy = 1 Chaser Type-III, 1 Roller Type-III, 1 Smasher Type-III, 1 Rescuer Type-III,
                    1 Smasher Type-III, 1 Shooter Type-III, 1 Battler Type-III
    Spawn Enemy = (1 Smasher Type-II, 1 Roller Type-III, 1 Shooter Type-II)
                  (1 Battler Type-II, 1 Chaser Type-II, 1 Rescuer Type-III)
                  (1 Chaser Type-III, 1 Shooter Type-II, 1 Smasher Type-II, 1 Rescuer Type-II)
                  (1 Smasher Type-III, 1 Smasher Type-II, 1 Chaser Type-II, 1 Rescuer Type-II)
                  (1 Roller Type-III, 1 Smasher Type-II, 1 Rescuer Type-III, 1 Shooter Type-II)
    Enemy Level = 4 - 5
  Stage 10
    Stage Type = 2 --area_battle
    Room W = 15000
    Room H = 1200
    Initial Enemy = 1 Smasher Type-II, 1 Rescuer Type-III, 1 Battler Type-III, 1 Trailblazer, 1 Shooter Type-II
    Spawn Enemy = (1 Roller Type-II, 1 Roller Type-III, 1 Ferroller)
                  (1 Chaser Type-III, 1 Salvager)
                  (1 Roller Type-III, 1 Rescuer Type-III, 1 Crusher)
                  (1 Bruiser, 1 Sharpshooter)
    Enemy Level = 4 - 6

Chapter 2 (1-botol_plastik)
  Level Mod = [-1, -1, 0, 1, 2]
  Type Mod = [0, 1, 2, 3]
  Stage 1
    Stage Type = 1 --area_battle
    Room W = 12500
    Room H = 1600
    Initial Enemy = 1 Roller Type-III, 1 Chaser Type-III, 1 Smasher Type-III,
                    1 Battler Type-III, 1 Shooter Type-III
    Spawn Enemy = (1 Roller Type-II, 1 Rescuer Type-II, 1 Smasher Type-II)
                  (1 Smasher Type-II, 1 Bandit, 1 Chaser Type-II, 1 Rescuer Type-II)
                  (1 Bandit, 1 Battler Type-III, 1 Shooter Type-II, 1 Rescuer Type-II)
                  (2 Bandit, 1 Smasher Type-II, 1 Rescuer Type-III)
    Enemy Level = 5 - 6
    Objectives = Kalahkan semua musuh
  Stage 2
    Stage Type = 1 --area_battle
    Room W = 15000
    Room H = 1600
    Initial Enemy = 1 Chaser Type-III, 1 Battler Type-III, 1 Smasher Type-III, 1 Tank,
                    1 Chaser Type-III, 1 Shooter Type-III, 1 Rescuer Type-III
    Spawn Enemy = (1 Roller Type-II, 1 Rescuer Type-II, 1 Bandit)
                  (2 Bandit, 1 Rescuer Type-II, 1 Smasher Type-II)
                  (1 Roller Type-III, 1 Shooter Type-II, 1 Battler Type-II, 1 Smasher Type-II)
                  (1 Roller Type-III, 1 Chaser Type-II, 1 Rescuer Type-III, 1 Bandit)
    Enemy Level = 6 - 7
    Objectives = Kalahkan semua musuh
  Stage 3
    Stage Type = 1 --area_battle
    Room W = 17500
    Room H = 1600
    Initial Enemy = 1 Roller Type-III, 1 Chaser Type-III, 1 V-Bandit, 1 Battler Type-III, 1 Salvager
    Spawn Enemy = (2 Bandit, 1 Shooter Type-II, 1 Rescuer Type-II)
                  (1 Roller Type-III, 1 Rescuer Type-II, 1 Smasher Type-II, 1 Shooter Type-II)
                  (1 Battler Type-III, 1 Smasher Type-III, 2 Bandit)
                  (1 Chaser Type-II, 1 Shooter Type-III, 1 Bandit, 1 Battler Type-III)
                  (1 Smasher Type-III, 1 Roller Type-III, 1 Shooter Type-II, 1 Chaser Type-II)
    Enemy Level = 6 - 8
    Objectives = Kalahkan semua musuh
  Stage 4
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = Chaser Type-III, Smasher Type-III, Battler Type-III, Shooter Type-III,
                    Roller Type-III, Tank, Chaser Type-III, Smasher Type-III,  Bandit, Rescuer Type-III
    Spawn Enemy = (Roller Type-II, Bandit, Rescuer Type-II, Chaser Type-II)
                  (Smasher Type-II, Roller Type-II, Battler Type-II, Shooter Type-II)
                  (Bandit, Rescuer Type-III, Bandit, Battler Type-II)
                  (Roller Type-III, Battler Type-II, Smasher Type-III, Rescuer Type-II)
                  (2 Bandit, 1 Shooter Type-III, 1 Battler Type-II)
    Enemy Level = 7 - 8
    Objectives = Kalahkan semua musuh
  Stage 5
    Stage Type = 2 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = Ferroller, Battler Type-III, Shooter Type-III, R-Tank, Bruiser,
                    Salvager, V-Bandit, Sharpshooter, Crusher, Trailblazer
    Spawn Enemy = (2 Smasher Type-II, 2 Rescuer Type-II)
                  (Chaser Type-II, Bandit, Roller Type-II, Shooter Type-II)
                  (Roller Type-III, Rescuer Type-II, Shooter Type-II, Bandit)
                  (Chaser Type-III, Bandit, Battler Type-II, Smasher Type-II)
                  (Bandit, Salvager, Shooter Type-III, Smasher Type-II)
    Enemy Level = 7 - 9
    Objectives = Kalahkan semua musuh
  Stage 6
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = Chaser Type-III, Bandit, Tank, Battler Type-III, Assailant, Accessory
    Spawn Enemy = (Roller Type-II, Chaser Type-II, Shooter Type-II, Smasher Type-II)
                  (Smasher Type-II, Battler Type-II, Bandit, Rescuer Type-II)
                  (Assailant, Accessory, Smasher Type-II, Shooter Type-II)
                  (Battler Type-II, Shooter Type-III, Chaser Type-II, Bandit)
                  (Bandit, Rescuer Type-III, 2 Battler Type-II)
                  (Bandit, Smasher Type-III, Rescuer Type-III, Shooter Type-II)
    Enemy Level = 8 - 9
    Objectives = Ambil 5 botol, kalahkan semua musuh
  Stage 7
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = V-Bandit, Battler Type-III, Crusher, Trailblazer, Sharpshooter, Ferroller, Tank
    Spawn Enemy = (Bandit, Rescuer Type-II, Shooter Type-II, Roller Type-II)
                  (Roller Type-III, Chaser Type-II, 2 Smasher Type-II)
                  (Battler Type-II, Shooter Type-III, Bandit, Chaser Type-II)
                  (Accessory, Assailant, Chaser Type-II, Rescuer Type-II)
                  (Battler Type-III, Bandit, Roller Type-III, Smasher Type-II)
                  (T-Assailant, W-Accessory, Salvager)
    Enemy Level = 8 - 10
    Objectives = Kalahkan semua musuh
  Stage 8
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = Chaser Type-III, Bandit, Tank, Battler Type-III, Shooter Type-III,
                    Assailant, Accessory, Smasher Type-III, Bandit, Shooter Type-III
    Spawn Enemy = (Roller Type-III, Smasher Type-II, Rescuer Type-II)
                  (Chaser Type-II, Shooter Type-II, Bandit)
                  (Rescuer Type-II, Roller Type-III, Chaser Type-III)
                  (Battler Type-II, Bandit, Rescuer Type-III)
                  (Smasher Type-III, Assailant, Rescuer Type-II, Accessory)
                  (Battler Type-III, Shooter Type-III, Bandit, Rescuer Type-III)
    Enemy Level = 9 - 10
    Objectives = Ambil 5 botol, kalahkan semua musuh
  Stage 9
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = Sharpshooter, Ferroller, Bandit, R-Tank, Battler Type-III, Trailblazer,
                    2 Salvager, T-Assailant, W-Accessory
    Spawn Enemy = (Roller Type-III, Bandit, Rescuer Type-II)
                  (Battler Type-II, Chaser Type-III, Shooter Type-II)
                  (Smasher Type-II, Bruiser, Rescuer Type-II)
                  (Roller Type-III, Bandit, Crusher)
                  (2 Assailant, 2 Accessory)
                  (2 V-Bandit, 2 Rescuer Type-III)
    Enemy Level = 9 - 11
    Objectives = Kalahkan semua musuh
  Stage 10
    Stage Type = 3 --area_battle
    Room W = 15000
    Room H = 1600
    Initial Enemy = V-Bandit, Ferroller, Trailblazer, Salvager, Bruiser
    Spawn Enemy = (Roller Type-III, Bandit, Rescuer Type-II, W-Accessory, Shooter Type-II,
                   Crusher, Assailant, Salvager, Rescuer Type-III, Shooter Type-III)
                  (Ferroller-X)
    Enemy Level = 10 - 13
    Objectives = Kalahkan semua musuh, kalahkan boss

Chapter 3 (2-sampah, 1-bom_ikan)
  Level Mod = [-2, -1, 0, 1, 3]
  Type Mod = [0, 1, 2, 4]
  Stage 1
    Stage Type = 1 --area_battle
    Room W = 12500
    Room H = 1600
    Initial Enemy = Dew Chaser, Dew Chaser v2, Dew Smasher v2, Dew Shooter v2, Dew Battler v2
    Spawn Enemy = (Dew Smasher, Dew Chaser, Dew Battler)
                  (Dew Smasher, Dew Battler, Dew Shooter, Dew Chaser)
                  (2 Dew Chaser v2, Dew Smasher v2, Dew Shooter v2)
                  (Dew Smasher v2, Dew Shooter v2, Dew Battler v2, Dew Shooter v2)
    Enemy Level = 10
    Objectives = Kalahkan semua musuh
  Stage 2
    Stage Type = 1 --area_battle
    Room W = 15000
    Room H = 1600
    Initial Enemy = Dew Battler v3, Tank, Bandit, Dew Smasher v3, Dew Chaser v3
    Spawn Enemy = (Dew Smasher v2, Dew Shooter v2, Bandit)
                  (Bandit, 2 Dew Chaser v2, Dew Battler v2)
                  (Dew Battler v3, 2 Dew Chaser v2, Dew Smasher v3)
                  (Dew Battler v3, Assailant, Accessory, Dew Shooter v2)
    Enemy Level = 11 - 12
    Objectives = Kalahkan semua musuh
  Stage 3
    Stage Type = 1 --area_battle
    Room W = 17500
    Room H = 1600
    Initial Enemy = Dew-drop Trailblazer, V-Bandit, T-Assailant, W-Accessory, Dew-drop Bruiser
    Spawn Enemy = (2 Dew Battler v2, 2 Dew Chaser v2)
                  (Assailant, Dew Shooter v2, Dew Smasher v2, Accessory)
                  (Dew Smasher v3, Dew Shooter v2, Bandit, Dew Chaser v2)
                  (Assailant, Accessory, Dew Battler v2, Bandit)
                  (Dew Battler v3, Dew Smasher v3, Dew Shooter v3, Dew Chaser v3)
    Enemy Level = 11 - 13
    Objectives = Ambil 5 sampah, kalahkan semua musuh
  Stage 4
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = Dew Smasher v3, Tank, Dew Chaser v3, Dew Smasher v3, Dew Shooter v3,
                    2 Dew Battler v3
    Spawn Enemy = (Dew Chaser v2, Dew Shooter v2, Bandit, Dew Smasher v2)
                  (Dew Battler v2, Dew Smasher v3, 2 Bandit)
                  (Assailant, Bandit, Dew Battler v3, Accessory)
                  (Dew Smasher v3, Bandit, Dew Chaser v3, Dew Shooter v3)
                  (Assailant, Bandit, Accessory, Dew Smasher v3)
    Enemy Level = 11 - 12
    Objectives = Kalahkan semua musuh
  Stage 5
    Stage Type = 2 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = Dew-drop Crusher, Dew-drop Bruiser, Dew-drop Sharpshooter, R-Tank
                    2 V-Bandit, Dew-drop Trailblazer, Dew-drop Bruiser
    Spawn Enemy = (Dew Chaser v2, Dew Smasher v2, Dew Shooter v3)
                  (Accessory, Assailant, Dew Battler v3, Dew Chaser v2)
                  (Bandit, Dew Shooter v3, Dew Smasher v3)
                  (Dew Chaser v3, Dew-drop Crusher, 2 Dew Shooter v3)
                  (T-Assailant, W-Accessory, Dew Smasher v3, Dew Shooter v3)
                  (Bandit, Dew-drop Trailblazer, Dew Battler v3, Dew-drop Sharpshooter)
    Enemy Level = 12 - 14
    Objectives = Ambil 7 sampah, kalahkan semua musuh
  Stage 6
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = Dew Smasher v3, Tank, Dew Chaser v3, Dew Battler v3, Dew
                    Smasher v3, Bandit, Dew Shooter v3
    Spawn Enemy = (Dew Smasher v2, Dew Battler v2, Dew Shooter v2)
                  (Dew Chaser v2, Bandit, Dew Smasher v2, Dew Shooter v2)
                  (Dew Battler v3, Accessory, Dew Shooter v2, Assailant)
                  (Dew Battler v3, Dew Chaser v2, Dew Smasher v3, Bandit)
                  (Assailant, Accessory, Bandit, Dew Shooter v3)
                  (Dew Chaser v3, Dew Battler v3, Dew Shooter v3, Dew Smasher v3)
    Enemy Level = 12 - 13
    Objectives = Kalahkan semua musuh
  Stage 7
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = Bandit, Dew Chaser v3, Dew Smasher v3, Skidder, Dew Battler v3,
                    Hydrologist, Dew Shooter v3
    Spawn Enemy = (Dew Battler v2, Dew Chaser v2, Dew Smasher v2)
                  (Accessory, Assailant, Dew Chaser v2, Dew Shooter v2)
                  (Dew Chaser v3, Dew Smasher v2, Bandit, Dew Shooter v2)
                  (2 Dew Chaser v3, Skidder, 2 Dew Shooter v2)
                  (Dew Battler v3, Bandit, Dew Shooter v3)
                  (Dew Battler v3, Dew Smasher v3, Dew Shooter v3, Skidder)
    Enemy Level = 12 - 13
    Objectives = Ambil 7 sampah, kalahkan semua musuh
  Stage 8
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = V-Bandit, Dew-drop Crusher, Dew-drop Trailblazer, V-Bandit,
                    Hydrologist, T-Assailant, W-Accessory, Tank, Dew-drop
                    Sharpshooter, Dew-drop Bruiser
    Spawn Enemy = (Bandit, Dew Shooter v2, Dew Chaser v2)
                  (2 Dew Smasher v2, Dew Battler v3)
                  (Dew Battler v2, Skidder, Dew Chaser v2)
                  (2 Skidder, Dew Shooter v2, Dew Shooter v3)
                  (Dew Battler v3, Assailant, W-Accessory)
                  (Dew Battler v3, Dew Smasher v3, Dew Shooter v3, Skidder)
    Enemy Level = 13 - 15
    Objectives = Hancurkan 5 bom ikan, kalahkan semua musuh
  Stage 9
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = Dew Chaser v3, Skidder, Dew Battler v3, Dew Smasher v3, Assailant,
                    Accessory, Hydrologist, Dew Battler v3
    Spawn Enemy = (Bandit, 2 Dew Smasher v2)
                  (Dew Battler v3, Dew Chaser v2, Dew Smasher v2, Dew Shooter v2)
                  (Skidder, Bandit, Dew Battler v2, Dew Smasher v2)
                  (Dew Battler v3, Skidder, Dew Shooter v3)
                  (Dew Smasher v3, Accessory, Dew Battler v3, Assailant)
                  (Skidder, Dew Battler v3, Bandit, Dew Shooter v3)
    Enemy Level = 13 - 14
    Objectives = Kalahkan semua musuh
  Stage 10
    Stage Type = 2 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = V-Bandit, Dew-drop Crusher, R-Tank, Dew-drop Trailblazer,
                    Dew-drop Bruiser, W-Skidder, Dew-drop Crusher, S-Hydrologist,
                    Dew-drop Sharpshooter
    Spawn Enemy = (Dew Chaser v3, Dew Smasher v2, Dew Battler v2)
                  (Dew Smasher v3, Bandit, Dew Chaser v2)
                  (Dew Battler v3, Dew Shooter v3, Skidder)
                  (Dew-drop Trailblazer, Dew Shooter v3, Bandit)
                  (T-Assailant, Dew Smasher v3, W-Accessory)
                  (Dew Battler v3, Dew-drop Crusher, Dew-drop Sharpshooter)
                  (Dew-drop Trailblazer, Dew-drop Bruiser, W-Skidder)
    Enemy Level = 13 - 15
    Objectives = Ambil 5 sampah, hancurkan 5 bom ikan, kalahkan semua musuh
  Stage 11
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = Dew Smasher v3, Skidder, Dew Battler v3, Hydrologist,
                    Assailant, Accessory, Dew Shooter v3
    Spawn Enemy = (Dew Chaser v2, Dew Battler v2, Dew Smasher v2)
                  (Dew Chaser v3, Dew Smasher v2, Dew Shooter v2)
                  (Dew Smasher v2, Bandit, Dew Chaser v3)
                  (Dew Battler v2, Dew Smasher v3, Dew Chaser v3)
                  (Dew Battler v3, Dew Shooter v3, Dew Smasher v3)
                  (Bandit, 2 Skidder)
    Enemy Level = 14 - 15
    Objectives = Kalahkan semua musuh
  Stage 12
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = Bandit, Dew Shooter v3, Dew Chaser v3, Dew Smasher v3,
                    Hydrologist, Dew Battler v3, Dew Shooter v3
    Spawn Enemy = (Dew Battler v2, Dew Shooter v2, Dew Smasher v2)
                  (Dew Chaser v2, Skidder, Dew Shooter v2)
                  (Accessory, Bandit, Assailant)
                  (Dew Smasher v3, Skidder, Dew Shooter v3)
                  (Dew Battler v3, Dew Smasher v3, Dew Shooter v3)
                  (Skidder, Bandit, Dew Chaser v3)
    Enemy Level = 14 - 15
    Objectives = Ambil 5 sampah, kalahkan semua musuh
  Stage 13
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = 2 Dew Smasher v3, Dew Battler v3, Skidder, Hydrologist,
                    Dew Chaser v3, Dew Shooter v3
    Spawn Enemy = (Bandit, Dew Shooter v2, Dew Chaser v2)
                  (Dew Chaser v3, Dew Shooter v3, Dew Battler v2)
                  (2 Dew Smasher v3, Dew Shooter v3)
                  (Skidder, Assailant, Accessory)
                  (Dew Chaser v3, 2 Skidder)
                  (Bandit, Dew Battler v3, Dew Shooter v3)
    Enemy Level = 14 - 15
    Objectives = Hancurkan 5 bom ikan, Kalahkan semua musuh
  Stage 14
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = Dew Smasher v3, Dew-drop Crusher, Dew-rop Trailblazer, Tank, Bandit,
                    S-Hydrologist, Dew-drop Bruiser, Dew-drop Sharpshooter, W-Skidder
    Spawn Enemy = (Dew Chaser v3, Dew Shooter v2, Dew Smasher v2)
                  (Dew Battler v2, Skidder, Dew Shooter v2)
                  (Dew Chaser v3, Bandit, Dew Smasher v3)
                  (2 Skidder, 2 Dew Shooter v3)
                  (Dew Chaser v3, Assailant, Dew Battler v3, Accessory)
                  (Bandit, Dew Chaser v3, Skidder, Dew Smasher v3)
    Enemy Level = 15 - 17
    Objectives = Hancurkan 5 bom ikan, ambil 7 sampah, kalahkan semua musuh
  Stage 15
    Stage Type = 3 --area_battle
    Room W = 15000
    Room H = 1600
    Initial Enemy = V-Bandit, W-Accessory, T-Assailant, S-Hydrologist, Dew-drop Sharpshooter
    Spawn Enemy = (Dew Chaser v2, Dew Smasher v2, Skidder, Dew Battler v3, Dew Shooter v3)
                  (Dew Battler v2, Skidder, W-Accessory, T-Assailant, Dew-drop Sharpshooter)
                  (Fluvial/Hoarfrost Hydrobot)
    Enemy Level = 15 - 19
    Objectives = Kalahkan semua musuh, kalahkan bos

Chapter 4 ([2-sampah, 3-bahan berbahaya])
  Level Mod = [-3, -1, 0, 2, 3]
  Type Mod = [0, 1, 3, 4]
  Stage 1
    Stage Type = 1 --area_battle
    Room W = 12500
    Room H = 1600
    Initial Enemy = Poacher v2, F-Trespasser v2, I-Hunter v2, 2 I-Logger v2
    Spawn Enemy = (F-Trespasser, Poacher, I-Logger)
                  (Poacher, I-Logger, F-Trespasser, I-Hunter)
                  (2 F-Trespasser, 2 I-Logger v2)
                  (Poacher v2, F-Trespasser v2, I-Logger v2, I-Hunter v2)
    Enemy Level = 15
    Objectives = Kalahkan semua musuh
  Stage 2
    Stage Type = 1 --area_battle
    Room W = 15000
    Room H = 1600
    Initial Enemy = Poacher v3, F-Trespasser v3, I-Hunter v3, 2 I-Logger v3
    Spawn Enemy = (F-Trespasser, Poacher, I-Hunter)
                  (F-Trespasser, F-Trespasser v2, I-Logger v2, I-Logger)
                  (Assailant, Accessory, I-Logger v2, I-Hunter v3)
                  (I-Logger v3, Poacher v3, F-Trespasser v3, I-Hunter v3)
    Enemy Level = 16 - 17
    Objectives = Ambil 5 sampah, kalahkan semua musuh
  Stage 3
    Stage Type = 1 --area_battle
    Room W = 17500
    Room H = 1600
    Initial Enemy = I-Logger v3, F-Trespasser v3, 2 I-Hunter v3, 2 I-Lumberjack, I-Logger v3
    Spawn Enemy = (Poacher v2, I-Logger v2, I-Hunter v2)
                  (F-Trespasser v2, I-Logger v2, I-Lumberjack)
                  (Poacher v3, I-Logger v2, I-Hunter v2, F-Trespasser v3)
                  (2 I-Logger v3, I-Hunter v2, I-Hunter v3)
                  (I-Lumberjack, I-Logger v3, 2 I-Hunter v3)
    Enemy Level = 16 - 17
    Objectives = Kalahkan semua musuh
  Stage 4
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = O-Poacher, 2 O-Logger, O-Trespasser, I-Lumberjack, O-Hunter, I-Lumberjack
    Spawn Enemy = (Accessory, Assailant, I-Hunter v2)
                  (F-Trespasser v3, I-Logger v2, I-Hunter v2)
                  (I-Logger v3, F-Trespasser v2, I-Lumberjack, Poacher v3)
                  (I-Lumberjack, I-Logger v3, O-Poacher, I-Hunter v3)
                  (I-Lumberjack, I-Hunter v3, F-Trespasser v3, O-Hunter)
    Enemy Level = 16 - 19
    Objectives = Ambil 5 bahan berbahaya, kalahkan semua musuh
  Stage 5
    Stage Type = 2 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = 2 O-Logger, O-Hunter, R-Tank, V-Bandit, O-Lumberjack, O-Hunter,
                    O-Trespasser, O-Poacher
    Spawn Enemy = (Poacher v2, I-Logger v2, I-Hunter v2)
                  (I-Lumberjack, F-Trespasser v2, I-Hunter v2, Bandit)
                  (2 I-Logger v3, F-Trespasser v3)
                  (Poacher v3, I-Lumberjack, I-Hunter v3)
                  (F-Trespasser v3, O-Lumberjack, 2 I-Hunter v3)
                  (W-Accessory, Assailant, O-Poacher, I-Lumberjack)
    Enemy Level = 17 - 20
    Objectives = Ambil 7 bahan berbahaya, kalahkan semua musuh
  Stage 6
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = Bandit, F-Trespasser v3, I-Logger v3, I-Hunter v3, I-Lumberjack,
                    Poacher v3, F-Trespasser v3
    Spawn Enemy = (F-Trespasser, I-Hunter, Poacher)
                  (Poacher v2, F-Trespasser v2, I-Logger v2, I-Hunter v2)
                  (2 I-Logger v3, Assailant, Accessory)
                  (I-Lumberjack, 2 Poacher v2, I-Hunter v3)
                  (2 F-Trespasser v3, 2 I-Logger v3)
                  (Poacher v3, I-Logger v3, I-Lumberjack, I-Hunter v3)
    Enemy Level = 17 - 18
    Objectives = Kalahkan semua musuh
  Stage 7
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = 2 I-Lumberjack, 2 I-Logger v3, F-Trespasser v3, Poacher v3, 3 I-Hunter v3
    Spawn Enemy = (I-Logger v2, I-Hunter v2, Poacher v2)
                  (2 F-Trespasser v2, I-Hunter v3)
                  (2 F-Trespasser v2, 2 Poacher v3)
                  (I-Lumberjack, Poacher v3, I-Logger v3)
                  (F-Trespasser v3, Poacher v3, I-Logger v3, I-Hunter v3)
                  (Poacher v3, I-Lumberjack, I-Logger v3, F-Trespasser v3)
    Enemy Level = 17 - 18
    Objectives = Ambil 7 sampah, kalahkan semua musuh
  Stage 8
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = F-Trespasser v3, I-Lumberjack, 2 I-Hunter v3, 2 I-Logger v3,
                    Poacher v3, F-Trespasser v3, I-Lumberjack
    Spawn Enemy = (F-Trespasser v2, 2 I-Logger v2)
                  (Poacher v3, I-Logger v2, I-Hunter v2)
                  (I-Lumberjack, I-Hunter v3, I-Logger v2, F-Trespasser v2)
                  (Poacher v3, I-Lumberjack, I-Hunter v3)
                  (I-Hunter v3, F-Trespasser v3, I-Logger v3, I-Lumberjack)
                  (2 I-Lumberjack, 2 I-Logger v3)
    Enemy Level = 18 - 19
    Objectives = Ambil 7 sampah, kalahkan semua musuh
  Stage 9
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = O-Poacher, 2 O-Logger, O-Lumberjack, O-Hunter, O-Trespasser,
                    2 O-Logger, O-Hunter
    Spawn Enemy = (2 F-Trespasser v2, I-Hunter v2)
                  (Poacher v3, I-Logger v2, F-Trespasser v2)
                  (I-Lumberjack, F-Trespasser v2, I-Logger v3, I-Hunter v2)
                  (Poacher v3, F-Trespasser v2, I-Lumberjack, F-Overseer)
                  (I-Logger v3, I-Lumberjack, I-Hunter v3)
                  (I-Lumberjack, Poacher v3, F-Trespasser v3, F-Overseer)
    Enemy Level = 18 - 21
    Objectives = Kalahkan semua musuh
  Stage 10
    Stage Type = 2 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = O-Trespasser, O-Hunter, O-Logger, 2 O-Lumberjack, R-Tank,
                    O-Poacher, 2 O-Logger, O-Hunter
    Spawn Enemy = (2 Logger v2, Poacher v2)
                  (F-Trespasser v2, I-Hunter v2, I-Lumberjack)
                  (I-Logger v3, F-Trespasser v3, I-Hunter v2, I-Lumberjack)
                  (Accessory, Assailant, Bandit, I-Hunter v2)
                  (O-Poacher, F-Trespasser v3, I-Lumberjack)
                  (O-Logger, I-Lumberjack, F-Overseer, I-Logger v3)
                  (2 O-Lumberjack, F-Overseer, I-Hunter v3)
    Enemy Level = 18 - 21
    Objectives = Ambil 10 sampah dan 5 bahan berbahaya, kalahkan semua musuh
  Stage 11
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = Poacher v3, 2 I-Logger v3, 2 I-Lumberjack, 2 I-Hunter v3
    Spawn Enemy = (Poacher v2, I-Logger v2, I-Hunter v2)
                  (I-Logger v2, Poacher v2, F-Trespasser v2)
                  (I-Logger v3, F-Trespasser v2, 2 I-Hunter v2)
                  (Poacher v3, F-Trespasser v2, I-Lumberjack, I-Hunter v2)
                  (2 Poacher v3, I-Logger v3, I-Logger v2)
                  (F-Trespasser v3, 2 I-Lumberjack, I-Hunter v3)
    Enemy Level = 19 - 20
    Objectives = Kalahkan semua musuh
  Stage 12
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = I-Logger v3, F-Trespasser v3, 2 I-Lumberjack, I-Hunter v3, 2 Poacher v3
    Spawn Enemy = (Poacher v2, I-Logger v2, F-Trespasser v2)
                  (Poacher v3, F-Trespasser v2, Poacher v2, F-Trespasser v2)
                  (I-Logger v3, I-Lumberjack, F-Trespasser v2, I-Hunter v2)
                  (I-Lumberjack, I-Logger v3, I-Hunter v3, F-Trespasser v2)
                  (I-Logger v3, I-Lumberjack, F-Trespasser v3, I-Hunter v3)
                  (Poacher v3, I-Lumberjack, I-Logger v3, I-Hunter v3)
    Enemy Level = 19 - 20
    Objectives = Ambil 5 bahan berbahaya, kalahkan semua musuh
  Stage 13
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = F-Trespasser v3, Poacher v3, 2 I-Logger v3, 2 I-Lumberjack, I-Hunter v3
    Spawn Enemy = (2 I-Logger v2, F-Trespasser v3)
                  (Poacher v3, I-Logger v2, I-Lumberjack, I-Hunter v2)
                  (I-Lumberjack, Poacher v3, I-Hunter v3, F-Trespasser v2)
                  (I-Logger v3, Poacher v3, I-Hunter v3, F-Trespasser v3)
                  (I-Lumberjack, I-Logger v3, I-Lumberjack, I-Hunter v3)
                  (Poacher v3, I-Lumberjack, I-Hunter v3, I-Logger v3)
    Enemy Level = 19 - 20
    Objectives = Ambil 7 sampah, kalahkan semua musuh
  Stage 14
    Stage Type = 1 --area_battle
    Room W = 20000
    Room H = 1600
    Initial Enemy = O-Trespasser, O-Poacher, O-Logger, 2 O-Lumberjack, O-Logger, O-Hunter
    Spawn Enemy = (I-Logger v3, I-Hunter v2, F-Trespasser v2)
                  (Poacher v2, F-Trespasser v2, I-Lumberjack, I-Hunter v3)
                  (F-Trespasser v3, I-Hunter v2, Poacher v3, I-Logger v3)
                  (2 F-Trespasser v3, 2 I-Hunter v3)
                  (2 I-Lumberjack, Poacher v3, F-Overseer)
                  (Poacher v3, I-Lumberjack, F-Overseer, I-Hunter v3)
    Enemy Level = 20 - 23
    Objectives = Ambil 5 sampah dan 5 bahan berbahaya, kalahkan semua musuh
  Stage 15
    Stage Type = 3 --area_battle
    Room W = 15000
    Room H = 1600
    Initial Enemy = O-Poacher, O-Logger, O-Hunter, O-Logger, O-Lumberjack
    Spawn Enemy = (Poacher v3, I-Lumberjack, 2 I-Hunter v2, F-Overseer)
                  (I-Logger v3, O-Lumberjack, I-Hunter v3, F-Trespasser v3, F-Overseer)
                  (Deforester Chief)
    Enemy Level = 20 - 24
    Objectives = Kalahkan semua musuh, kalahkan boss

Chapter 5

Coin Production
  Stage 1
    Stage Type = 1 --wave_battle
    Room W = 5000
    Room H = 1600
    Initial Enemy = -
    Spawn Enemy = [(3 C-Roller)
                   (2 C-Roller, 1 C-Chaser)
                   (1 C-Roller, 2 C-Chaser, 1 C-Smasher)]
    Enemy Level = 10
    Objectives = Kalahkan semua musuh
  Stage 2
    Stage Type = 1 --wave_battle
    Room W = 7500
    Room H = 1600
    Initial Enemy = C-Roller, C-Chaser, 2 C-Smasher, C-Chaser
    Spawn Enemy = [(2 C-Roller, C-Smasher)
                   (C-Chaser, C-Roller, C-Smasher)
                   (2 C-Smasher, 2 C-Chaser)]
    Enemy Level = 20
    Objectives = Kalahkan semua musuh
  Stage 3
    Stage Type = 2 --wave_battle
    Room W = 10000
    Room H = 1600
    Initial Enemy = C-Chaser, C-Roller, C-Ferroller, C-Trailblazer, C-Crusher
    Spawn Enemy = [(C-Roller, C-Chaser, C-Smasher)
                   (C-Smasher, 2 C-Chaser)
                   (2 C-Roller, C-Smasher, C-Chaser)]
                  [(C-Chaser, C-Trailblazer)
                   (C-Ferroller, C-Chaser, C-Crusher)]
    Enemy Level = 35 - 36
    Objectives = Kalahkan semua musuh

Oil Extraction
  Stage 1
    Stage Type = 1 --wave_battle
    Room W = 5000
    Room H = 1600
    Initial Enemy = -
    Spawn Enemy = [(Roller v3, Smasher v3, Bandit)
                   (Assailant, Accessory, Rescuer v3)
                   (Battler v3, Rescuer v3, Shooter v3, Bandit)]
    Enemy Level = 10
    Objectives = Kalahkan semua musuh
  Stage 2
    Stage Type = 1 --wave_battle
    Room W = 7500
    Room H = 1600
    Initial Enemy = Chaser v3, Smasher v3, Battler v3, Shooter v3, Rescuer v3
    Spawn Enemy = [(Roller v3, Smasher v3, Bandit)
                   (Assailant, Accessory, Rescuer v3)
                   (Battler v3, Smasher v3, Rescuer v3, Shooter v3)]
    Enemy Level = 20
    Objectives = Kalahkan semua musuh
  Stage 3
    Stage Type = 2 --wave_battle
    Room W = 10000
    Room H = 1600
    Initial Enemy = Dew Chaser v3, Dew Battler v3, W-Skidder, Dew-drop Sharpshooter, O-Lumberjack
    Spawn Enemy = [(Dew Battler v3, Assailant, Accessory)
                   (Battler v3, Rescuer v3, Dew Shooter v3)
                   (Bandit, Dew Smasher v3, Rescuer v3, Dew Shooter v3)]
                  [(W-Skidder, Dew Battler v3)
                   (O-Poacher, Salvager, I-Hunter v3)]
    Enemy Level = 35 - 36
    Objectives = Kalahkan semua musuh

Metal Mining
  Stage 1
    Stage Type = 1 --wave_battle
    Room W = 5000
    Room H = 1600
    Initial Enemy = -
    Spawn Enemy = [(2 A-Battler, A-Shooter)
                   (A-Shooter, A-Battler, A-Shooter)
                   (2 A-Battler, 2 A-Shooter)]
    Enemy Level = 10
    Objectives = Kalahkan semua musuh
  Stage 2
    Stage Type = 1 --wave_battle
    Room W = 7500
    Room H = 1600
    Initial Enemy = A-Battler, A-Shooter, A-Battler, 2 A-Shooter
    Spawn Enemy = [(2 A-Battler, A-Shooter)
                   (A-Battler, 2 A-Shooter)
                   (A-Battler, A-Shooter, A-Assailant, A-Accessory)]
    Enemy Level = 20 - 21
    Objectives = Kalahkan semua musuh
  Stage 3
    Stage Type = 2 --wave_battle
    Room W = 10000
    Room H = 1600
    Initial Enemy = A-Battler, A-Bruiser, A-Sharpshooter, A-Bruiser, A-Sharpshooter
    Spawn Enemy = [(2 A-Battler, A-Shooter)
                   (2 A-Battler, A-Shooter)
                   (A-Bruiser, 2 A-Shooter, A-Battler)]
                  [(A-Assailant, A-Accessory)
                   (A-Battler, A-Bruiser, A-Sharpshooter)]
    Enemy Level = 35 - 36
    Objectives = Kalahkan semua musuh

Mold Delineation
  Stage 1
    Stage Type = 2 --wave_battle
    Room W = 5000
    Room H = 1600
    Initial Enemy = -
    Spawn Enemy = [(Battler v2, Chaser v2, Smasher v2)
                   (Dew Battler v3, Rescuer v3, Skidder, I-Hunter v3)
                   (O-Poacher, O-Lumberjack, Sharpshooter)]
    Enemy Level = 35 - 38
    Objectives = Kalahkan semua musuh
  Stage 2
    Stage Type = 2 --wave_battle
    Room W = 5000
    Room H = 1600
    Initial Enemy = -
    Spawn Enemy = [(Battler v2, Rescuer v2, Chaser v2)
                   (Bandit, I-Logger v3, Rescuer v3)
                   (Dew-drop Bruiser, Salvager, W-Skidder, Salvager)]
    Enemy Level = 35 - 38
    Objectives = Kalahkan semua musuh
  Stage 3
    Stage Type = 2 --wave_battle
    Room W = 5000
    Room H = 1600
    Initial Enemy = -
    Spawn Enemy = [(2 Smasher v2, I-Logger v2)
                   (Bandit, Smasher v3, Skidder, F-Trespasser v3)
                   (O-Poacher, T-Assailant, W-Accessory)]
    Enemy Level = 35 - 38
    Objectives = Kalahkan semua musuh
  Stage 4
    Stage Type = 2 --wave_battle
    Room W = 5000
    Room H = 1600
    Initial Enemy = -
    Spawn Enemy = [(Battler v2, Dew Battler v2, Poacher v2)
                   (Assailant, Accessory, Poacher v3, Battler v3)
                   (W-Accessory, O-Poacher, T-Assailant)]
    Enemy Level = 35 - 38
    Objectives = Kalahkan semua musuh
  Stage 5
    Stage Type = 2 --wave_battle
    Room W = 5000
    Room H = 1600
    Initial Enemy = -
    Spawn Enemy = [(2 Shooter v2, Dew Shooter v2)
                   (2 Shooter v3, 2 Dew Shooter v3)
                   (Dew-drop Sharpshooter, Sharpshooter, Dew-drop Sharpshooter)]
    Enemy Level = 35 - 38
    Objectives = Kalahkan semua musuh
  Stage 6
    Stage Type = 2 --wave_battle
    Room W = 5000
    Room H = 1600
    Initial Enemy = -
    Spawn Enemy = [(2 F-Trespasser v2, Rescuer v2)
                   (Rescuer v3, F-Trespasser v3, Bandit, Rescuer v3)
                   (O-Trespasser, V-Bandit, Salvager)]
    Enemy Level = 35 - 38
    Objectives = Kalahkan semua musuh

Crystal Cleansing
  Stage 1
    Stage Type = 1 --wave_battle
    Room W = 5000
    Room H = 1600
    Initial Enemy = -
    Spawn Enemy = [(C-D Battler, C-Skidder, C-D Shooter)
                   (C-Poacher, 2 C-Skidder)
                   (C-D Battler, C-Poacher, C-Skidder, C-D Shooter)
                   (C-Poacher, C-Skidder, C-D Shooter, C-Skidder, C-Poacher)]
    Enemy Level = 10
    Objectives = Kalahkan semua musuh
  Stage 2
    Stage Type = 2 --wave_battle
    Room W = 7500
    Room H = 1600
    Initial Enemy = 2 C-D Battler, 2 C-Poacher, C-D Shooter
    Spawn Enemy = [(C-Poacher, C-D Battler, C-D Shooter)
                   (2 C-Skidder, C-Poacher, C-D Battler)
                   (2 C-D Battler, C-W Skidder, C-D Shooter)
                   (C-O Poacher, C-D Bruiser, 2 C-D Shooter)]
    Enemy Level = 20 - 21
    Objectives = Kalahkan semua musuh
  Stage 3
    Stage Type = 2 --wave_battle
    Room W = 10000
    Room H = 1600
    Initial Enemy = C-Poacher, C-D Battler, C-D Sharpshooter, C-D Bruiser, C-O Poacher
    Spawn Enemy = [(2 C-D Battler, C-D Shooter)
                   (3 C-Skidder, C-D Shooter)
                   (C-D Bruiser, C-Skidder, C-Poacher, C-D Shooter)
                   (C-D Battler, C-Skidder, C-F Overseer, C-Poacher)]
                  [(C-Poacher, 2 C-D Sharpshooter)
                   (C-D Battler, C-O Poacher, C-F Overseer, C-W Skidder)]
    Enemy Level = 35 - 37
    Objectives = Kalahkan semua musuh
