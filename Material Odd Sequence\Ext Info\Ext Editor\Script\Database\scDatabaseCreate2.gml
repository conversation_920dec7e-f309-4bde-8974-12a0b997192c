function db_create_skill_list(db_bots, class_num, type) {
	// class_num dimulai dari 1
	// type = 1-batk, 2-derv, 3-spmv, 4-ulti, 5-intg, 6-uniq, 7-ext
	var sect_name = ["", "sk_batk", "sk_derv", "sk_spmv", "sk_ulti", "sk_intg", "sk_uniq", "sk_ext"];
	var atk_name = ["type1", "type2", "type3", "type4", "type5", "type6", "type7", "type8", "type9", "type10", "type11"];
	var spr = [[], [noone, sBotsSkillWarriorBasic, sBotsSkillWarriorDerv, sBotsSkillWarriorSpmv, sBotsSkillWarriorUlti, sBotsSkillModule, sBotsSkillWarriorUniq, sBotsSkillWarriorExtra], 
			   [noone, sBotsSkillArcherBasic, sBotsSkillArcherDerv, sBotsSkillArcherSpmv, sBotsSkillArcherUlti, sBotsSkillModule, sBotsSkillArcherUniq, sBotsSkillArcherExtra], 
			   [noone, sBotsSkillMedicBasic, sBotsSkillMedicDerv, sBotsSkillMedicSpmv, sBotsSkillMedicUlti, sBotsSkillModule, sBotsSkillMedicUniq, sBotsSkillMedicExtra]];
	var spr_intg = [noone, sBotsSkillWarriorIntg, sBotsSkillArcherIntg, sBotsSkillMedicIntg];
	
	var atk_cnt = 0;
	var title = [];
	var desc = [];
	var tag = [];
	var unlocked = [];
	var level = [];
	var unlock_lv = [];
	var talent = [];
	var atk_cost = [];
	var upgraded = [];
	var adj_count = [];
	var chain = [];
	var sk_mod = [];
	var str_name = [];
	
	var db_wp = db_create_weapon(db_bots, class_num);
	var bots_data = db_get_row(db_bots, 1, class_num);
	var batk_limit = db_get_value(db_wp, 14, bots_data.weapon);		// Limit lv
	
	switch (type) {
		case 1:		// BATK
			switch (bots_data.num) {
				case 1:		// Warrior
					title = ["Slash", "Blow", "Thrust", "Double Slash", "Twin Thrust", "Uppercut", "Spinning Slash", "Slam", "Charged Slash", "Smash", "Stab"];
					atk_cnt = array_length(title);
					#region		// Desc & Tag
					desc = [{text: [switch_lang("Warrior menebas pedangnya ke depan, memberikan kerusakan fisik sedang kepada musuh.", "Warrior slashes its sword forward, dealing moderate physical damage to enemies."), switch_lang("Harga serangan = 1", "Attack cost = 1")], 
							 adv: switch_lang("Bisa dihubungkan dengan \"Double Slash\" atau \"Smash\".", "Can be connected with \"Double Slash\" or \"Smash\"."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Warrior menebas pedangnya ke depan dengan keras, memberikan kerusakan fisik tinggi dan memukul mundur musuh.", "Warrior slashes its sword forward with a bang, dealing high physical damage and causing knockback to enemies."), switch_lang("Harga serangan = 2", "Attack cost = 2")], 
							 adv: switch_lang("Mendapatkan Damage Output kalau rangkaian serangan sebelumnya adalah \"Slash\" atau \"Double Slash\". Efek ini akan dikalikan dengan nomor rantainya.", "Gain Damage Output if the previous basic attack sequence was \"Slash\" or \"Double Slash\". This effect will be multiplied by the chain number."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Warrior bergerak ke depan dan menusuk musuh, memberikan kerusakan fisik sedang kepada musuh.", "Warrior moves forward and thrust enemies, dealing moderate physical damage to enemies."), switch_lang("Harga serangan = 1", "Attack cost = 1")], 
							 adv: switch_lang("Mendapatkan Armor Penetration saat menggunakan serangan ini.", "Gain Armor Penetration when this attack is performed."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Warrior menebas pedangnya ke depan sebanyak 2 kali dengan cepat, memberikan kerusakan fisik sedang kepada musuh.", "Warrior slashes its sword forward 2 times quickly, dealing moderate physical damage to enemies."), switch_lang("Harga serangan = 2", "Attack cost = 2")], 
							 adv: switch_lang("Bisa dihubungkan dengan \"Slash\" atau \"Smash\".", "Can be connected with \"Slash\" or \"Smash\"."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Warrior menusuk pedangnya 2 kali ke depan, memberikan kerusakan fisik sedang kepada musuh.", "Warrior thrusts its sword forward 2 times quickly, dealing moderate physical damage to enemies."), switch_lang("Harga serangan = 2", "Attack cost = 2")], 
							 adv: switch_lang("Mendapatkan Armor Penetration saat menggunakan serangan ini.", "Gain Armor Penetration when this attack is performed."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Warrior menebas pedangnya ke atas, memberikan kerusakan fisik sedang dan melempar musuh ke udara.", "Warrior slashes its sword upperward, dealing moderate physical damage to enemies and knock it airborne."), switch_lang("Harga serangan = 1", "Attack cost = 1")], 
							 adv: "",
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Warrior memutar pedangnya di sekitar tubuhnya, memberikan kerusakan fisik sedang kepada musuh.", "Warrior spins its sword around its body, dealing moderate physical damage to enemies."), switch_lang("Harga serangan = 2", "Attack cost = 2")], 
							 adv: switch_lang("Akan menyimpan nomor rantai ketika digunakan.", "Will keep the chain number when performed."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Warrior membanting pedangnya ke tanah dengan paksa, memberikan kerusakan fisik tinggi dan memukul mundur musuh.", "Warrior slams its sword on the ground forcefully, dealing high physical damage and causing knockback to enemies."), switch_lang("Harga serangan = 3", "Attack cost = 3")], 
							 adv: switch_lang("Musuh yang berada di area utama serangan juga akan terlempar ke udara.", "Enemies in the main attack area will also be knocked airborne."),
							 dis: switch_lang("Hanya dapat digunakan saat mengudara.", "Can only be used in mid-air."),
							 mdf: ""}, 
							{text: [switch_lang("Warrior mengisi energi ke dalam pedangnya dan mengeluarkannya dengan kekuatan yang tinggi, memberikan kerusakan fisik masif dan memukul mundur musuh.", "Warrior charges its energy into its sword and releases it with high power, dealing massive physical damage and causing knockback to enemy."), switch_lang("Harga serangan = 3", "Attack cost = 3")], 
							 adv: switch_lang("Bisa diisi sampai tahap 3. Mendapatkan tambahan kekuatan knockback untuk setiap tahap pengisian.", "Can be charged up to 3 phase. Gain additional knockback power for each charging phase."),
							 dis: switch_lang("Akan gagal kalau tidak diisi sama sekali.", "Will fail if it's not charged at all."),
							 mdf: ""}, 
							{text: [switch_lang("Warrior menebas pedangnya ke tanah, memberikan kerusakan fisik tinggi kepada musuh.", "Warrior slashes its sword to the ground, dealing high physical damage to enemies."), switch_lang("Harga serangan = 3", "Attack cost = 3")], 
							 adv: switch_lang("Rantai III juga akan menyebabkan knockdown kepada musuh.", "Chain III will also cause a knockdown on the enemy."),
							 dis: switch_lang("Tidak bisa digunakan saat mengudara. Bisa dihubungkan dengan \"Slash\" atau \"Double Slash\", tetapi akan mengurangi nomor rantainya sebanyak 2.", "Can't be used in mid-air. Can be connected with \"Slash\" or \"Double Slash\", but will reduce the chain number by 2."),
							 mdf: ""}, 
							{text: [switch_lang("Warrior bergerak ke depan dan mengisi pedangnya selama sepersekian detik. Setelah itu, Warrior menusuk pedangnya ke depan dengan kuat, memberikan kerusakan fisik masif kepada musuh.", "Warrior dashes forward and charge its sword for a split second. Afterwards, Warrior thrusts its sword forward strongly, dealing massive physical damage to enemies."), switch_lang("Harga serangan = 4", "Attack cost = 4")], 
							 adv: switch_lang("Mendapatkan BATK Damage Bonus kalau Warrior menghindari serangan musuh saat menggunakan serangan ini.", "Gain BATK Damage Bonus if Warrior dodges enemy attacks while Warrior is performing this attack."),
							 dis: "",
							 mdf: ""}, 
							];
					tag = [{num: [1, 3, 4], text: ["Damage", "Multi", "Chainable"]},
						   {num: [1, 3, 5], text: ["Damage", "Single", "Knockback"]}, 
						   {num: [1, 3], text: ["Damage", "Single"]}, 
						   {num: [1, 3, 4], text: ["Damage", "Multi", "Chainable"]}, 
						   {num: [1, 3], text: ["Damage", "Single"]}, 
						   {num: [1, 3, 5], text: ["Damage", "Single", "Airborne"]}, 
						   {num: [1, 3, 4], text: ["Damage", "Multi", "Chainable"]}, 
						   {num: [1, 3, 5], text: ["Damage", "AoE", "Knockback"]}, 
						   {num: [1, 3, 5], text: ["Damage", "Multi", "Knockback"]}, 
						   {num: [1, 3, 4, 5], text: ["Damage", "Single", "Chainable", "Knockdown"]}, 
						   {num: [1, 3, 8], text: ["Damage", "Single", "Burst"]}
						   ];
					#endregion
					unlock_lv = [1, 1, 3, 5, 7, 10, 13, 15, 17, 20, 20];
					atk_cost = [1, 2, 1, 2, 2, 1, 2, 3, 3, 3, 4];
					talent = [{m7: 0, m8: 0, s17: 0}, {s3: 0}, {}, {m9: 0, s17: 0}, {s6: 0}, {}, {}, {s12: 0}, {m10: 0, s11: 0, s17: 0}, {m11: 0}, {s18: 0}];
					upgraded = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
					adj_count = [0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 1];
					chain = [{valid: 1, val: 1}, {valid: 0, val: 0}, {valid: 0, val: 0}, {valid: 1, val: 1}, {valid: 0, val: 0}, {valid: 0, val: 0}, 
							 {valid: 1, val: 0}, {valid: 0, val: 0}, {valid: 0, val: 0}, {valid: 1, val: -2}, {valid: 0, val: 0}];
				break;
				case 2:		// Archer
					title = ["Ordinary Shot", "Heavy Shot", "Multi Shot", "Double Shot", "Parallel Shot", "Acidic Shot", "Piercing Shot", "Charged Shot", "Explosive Shot"];
					atk_cnt = array_length(title);
					#region		// Desc & Tag
					desc = [{text: [switch_lang("Archer menembakkan anak panah ke target, memberikan kerusakan fisik sedang kepada musuh.", "Archer shoots arrows at the target, dealing moderate physical damage to enemies."), switch_lang("Harga serangan = 1", "Attack cost = 1")], 
							 adv: switch_lang("Bisa dihubungkan dengan \"Double Shot\", \"Acidic Shot\", atau \"Piercing Shot\". Rantai IV dan Rantai VI bisa menembus 2 musuh sekaligus. Mendapatkan 20% Physical Damage Bonus saat dalam mode Pinpoint.", "Can be connected with \"Double Shot\", \"Acidic Shot\", or \"Piercing Shot\". Chain IV and Chain VI can pierce 2 enemies at once. Gain 20% Physical Damage Bonus while in Pinpoint mode."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Archer menembakkan anak panah berat ke target, memberikan kerusakan fisik tinggi kepada musuh dan membuat musuh tersebut tersentak.", "Archer shoots heavy arrow at the target, dealing high damage and causing flinch to enemies."), switch_lang("Harga serangan = 2", "Attack cost = 2")], 
							 adv: switch_lang("Mendapatkan Damage Output berdasarkan posisi dari rangkaian serangan dasar. Mendapatkan 15% Critical Buildup saat dalam mode Pinpoint.", "Gain extra Damage Output based on basic attack sequence position. Gain 15% Critical Buildup while in Pinpoint mode."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Archer menembakkan beberapa anak panah berturut-turut ke target, memberikan kerusakan fisik sedang kepada musuh.", "Archer shoots multiple arrows in quick succession at the target, dealing moderate physical damage to enemies."), switch_lang("Harga serangan = 2", "Attack cost = 2")], 
							 adv: switch_lang("Mendapatkan tambahan anak panah berdasarkan setengah dari nomor rantai sebelumnya (dibulatkan ke atas). Mendapatkan 20% Attack Speed saat dalam mode Pinpoint.", "Gain additional arrows based on half of previous chain number (rounded up). Gain 20% Attack Speed while in Pinpoint mode."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Archer melakukan 2 serangan \"Ordinary Shot\" dengan cepat ke target berdasarkan nomor rantainya, memberikan kerusakan fisik sedang kepada musuh.", "Archer performs 2 \"Ordinary Shot\" attacks quickly at the target based on the chain number, dealing moderate physical damage to enemies."), switch_lang("Harga serangan = 2", "Attack cost = 2")], 
							 adv: switch_lang("Bisa dihubungkan dengan \"Ordinary Shot\", \"Acidic Shot\", atau \"Piercing Shot\".", "Can be connected with \"Ordinary Shot\", \"Acidic Shot\", or \"Piercing Shot\"."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Archer menembakkan beberapa anak panah sekaligus ke target, memberikan kerusakan fisik sedang kepada musuh.", "Archer shoots multiple arrows at once at the target, dealing moderate physical damage to enemies."), switch_lang("Harga serangan = 2", "Attack cost = 2")], 
							 adv: switch_lang("Mendapatkan tambahan anak panah berdasarkan setengah dari nomor rantai sebelumnya (dibulatkan ke atas). Mendapatkan 1 anak panah tambahan kalau digunakan saat mengudara. Mengurangi 35% dari Sudut Multi-Proyektil saat dalam mode Pinpoint.", "Gain additional arrows based on half of previous chain number (rounded up). Gain 1 extra arrow if used in mid-air. Reduces 35% of Multi-Projectile Angle while in Pinpoint mode."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Archer menembakkan anak panah ke target, memberikan kerusakan asam sedang kepada musuh.", "Archer shoots arrows at the target, dealing moderate acid damage to enemies."), switch_lang("Harga serangan = 2", "Attack cost = 2")], 
							 adv: switch_lang("Bisa dihubungkan dengan \"Ordinary Shot\", \"Double Shot\", atau \"Piercing Shot\". Rantai IV dan Rantai VI bisa menembus 2 musuh sekaligus. Mendapatkan 20% Acid Damage Bonus saat dalam mode Pinpoint.", "Can be connected with \"Ordinary Shot\", \"Double Shot\", or \"Piercing Shot\". Chain IV and Chain VI can pierce 2 enemies at once. Gain 20% Acid Damage Bonus while in Pinpoint mode."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Archer menembakkan anak panah yang tajam ke target, memberikan kerusakan fisik sedang kepada musuh.", "Archer shoots sharp arrows at the target, dealing moderate physical damage to enemies."), switch_lang("Harga serangan = 2", "Attack cost = 2")], 
							 adv: switch_lang("Bisa dihubungkan dengan \"Ordinary Shot\", \"Double Shot\", atau \"Acidic Shot\". Rantai IV dan Rantai VI bisa menembus 3 musuh sekaligus. Mendapatkan 15% Armor Penetration saat dalam mode Pinpoint.", "Can be connected with \"Ordinary Shot\", \"Double Shot\", or \"Acidic Shot\". Chain IV and Chain VI can pierce 3 enemies at once. Gain 15% Armor Penetration while in Pinpoint mode."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Archer mengisi energinya ke busurnya dan mengeluarkannya dengan kekuatan yang tinggi, memberikan kerusakan fisik masif dan memukul mundur musuh.", "Archer charges its energy into its bow and releases it with high power, dealing massive physical damage and causing knockback to enemies."), switch_lang("Harga serangan = 3", "Attack cost = 3")], 
							 adv: switch_lang("Bisa diisi sampai tahap 3. Mendapatkan tambahan kekuatan knockback dan serangan maksimal untuk setiap tahap pengisian. Mendapatkan 25% Charge Speed saat dalam mode Pinpoint.", "Can be charged up to 3 phase. Gain additional max hit and knockback power for each charging phase. Gain 25% Charge Speed while in Pinpoint mode."),
							 dis: switch_lang("Akan gagal kalau tidak diisi sama sekali. Setiap musuh yang terkena serangan ini akan mengurangi skala kerusakan serangan ini sebanyak 15%.", "Will fail if not charged at all. Each enemy hit by this attack will reduce this attack's damage scale by 15%."),
							 mdf: ""}, 
							{text: [switch_lang("Archer menembakkan anak panah peledak yang akan meledak saat mengenai objek, memberikan kerusakan fisik tinggi dan memukul mundur semua Bot di dalam radius.", "Archer shoots an explosive arrow at the target that will explode on contact, dealing high physical damage and causing knockback to all bots within a radius."), switch_lang("Harga serangan = 3", "Attack cost = 3")], 
							 adv: switch_lang("Mendapatkan AoE Damage Scale kalau digunakan saat mengudara. Mendapatkan 30% Critical Damage saat dalam mode Pinpoint.", "Gain AoE Damage Scale if used in mid-air. Gain 30% Critical Damage while in Pinpoint mode."),
							 dis: switch_lang("Rekan satu tim juga bisa terkena serangan ini.", "Teammates can also be damaged if hit by this attack."),
							 mdf: ""} 
							];
					tag = [{num: [1, 3, 4], text: ["Damage", "Single", "Chainable"]},
						   {num: [1, 3, 5], text: ["Damage", "Single", "Flinch"]}, 
						   {num: [1, 3, 8], text: ["Damage", "Single", "Rapid"]}, 
						   {num: [1, 3, 4], text: ["Damage", "Single", "Chainable"]}, 
						   {num: [1, 3, 8], text: ["Damage", "Single", "Rapid"]}, 
						   {num: [1, 3, 4], text: ["Damage", "Single", "Chainable"]}, 
						   {num: [1, 3, 4], text: ["Damage", "Multi", "Chainable"]}, 
						   {num: [1, 3, 5], text: ["Damage", "Multi", "Knockback"]}, 
						   {num: [1, 3, 5], text: ["Damage", "AoE", "Knockback"]}
						   ];
					#endregion
					unlock_lv = [1, 1, 3, 5, 7, 10, 13, 17, 20];
					atk_cost = [1, 2, 2, 2, 2, 2, 2, 3, 3];
					talent = [{m6: 0, m7: 0, s7: 0}, {s12: 0}, {s18: 0}, {s7: 0, s8: 0}, {s3: 0}, {m8: 0, m9: 0, s7: 0}, {m10: 0, s7: 0}, {m11: 0, s13: 0}, {s5: 0}];
					upgraded = [0, 0, 0, 0, 0, 0, 0, 0, 0];
					adj_count = [0, 1, 0, 0, 0, 0, 0, 1, 1];
					chain = [{valid: 1, val: 1}, {valid: 0, val: 0}, {valid: 0, val: 0}, {valid: 1, val: 1}, {valid: 0, val: 0}, 
							 {valid: 1, val: 1}, {valid: 1, val: 1}, {valid: 0, val: 0}, {valid: 0, val: 0}];
				break;
				case 3:		// Medic
					title = ["Slash", "Blow", "Thrust", "Acidic Slash", "Uppercut", "Spinning Slash", "Stab", "Drag Out"];
					atk_cnt = array_length(title);
					#region		// Desc & Tag
					desc = [{text: [switch_lang("Medic menebas pisaunya ke depan, memberikan kerusakan fisik kecil kepada musuh.", "Medic slashes its knife forward, dealing small physical damage to enemies."), switch_lang("Harga serangan = 1", "Attack cost = 1")], 
							 adv: switch_lang("Bisa dihubungkan dengan \"Acidic Slash\" atau \"Stab\".", "Can be connected with \"Acidic Slash\" or \"Stab\"."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Medic menebas pisaunya dengan keras, memberikan kerusakan fisik sedang dan memukul mundur musuh.", "Medic slashes its knife forward with a bang, dealing moderate physical damage and causing knockback to enemies."), switch_lang("Harga serangan = 2", "Attack cost = 2")], 
							 adv: switch_lang("Mendapatkan Damage Output berdasarkan nomor rantai sebelumnya.", "Gain Damage Output based on previous chain number."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Medic bergerak ke depan dan menusuk musuh, memberikan kerusakan fisik kecil kepada musuh.", "Medic moves forward and thrust enemies, dealing small physical damage to enemies."), switch_lang("Harga serangan = 1", "Attack cost = 1")], 
							 adv: switch_lang("Mendapatkan Armor Penetration saat menggunakan serangan ini.", "Gain Armor Penetration when this attack is performed."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Medic menebas pisaunya ke depan, memberikan kerusakan asam kecil kepada musuh.", "Medic slashes its knife forward, dealing small acid damage to enemies."), switch_lang("Harga serangan = 2", "Attack cost = 2")], 
							 adv: switch_lang("Bisa dihubungkan dengan \"Slash\" atau \"Stab\".", "Can be connected with \"Slash\" or \"Stab\"."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Medic menebas pisaunya ke atas, memberikan kerusakan fisik kecil kepada musuh dan melemparnya ke udara.", "Medic slashes its knife upperward, dealing moderate physical damage to enemies and knock it airborne."), switch_lang("Harga serangan = 1", "Attack cost = 1")], 
							 adv: switch_lang("Mendapatkan Physical Damage Bonus kalau serangan berikutnya adalah \"Blow\".", "Gain Physical Damage Bonus if the next attack is \"Blow\"."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Medic memutar pisaunya di sekitar tubuhnya, memberikan kerusakan fisik kecil kepada musuh. Seragan ini dianggap Rantai I kalau nomor rantai sebelumnya kurang dari 3 dan dianggap Rantai II kalau nomor rantai sebelumnya adalah 3 atau lebih.", "Medic spins its knife around its body, dealing small physical damage to enemies. This attack will regarded as Chain I if the previous chain number lower than 3 and will regarded as Chain II if the previous chain number is 3 or higher."), switch_lang("Harga serangan = 2", "Attack cost = 2")], 
							 adv: switch_lang("Mendapatkan Attack Speed berdasarkan nomor rantai sebelumnya. Rantai II akan mengulang serangan ini sebanyak nomor rantai sebelumnya.", "Gain Attack Speed based on the previous chain number. Chain II will repeat the attack as many times as the previous chain number."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Medic menusuk pisaunya ke depan, memberikan kerusakan fisik tinggi kepada musuh.", "Medic thrusts its knife forward, dealing moderate physical damage to enemies."), switch_lang("Harga serangan = 3", "Attack cost = 3")], 
							 adv: switch_lang("Mendapatkan Armor Penetration saat menggunakan serangan ini.", "Gain Armor Penetration when this attack is performed."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Medic merobek musuh yang sebelumnya tertusuk dengan pisaunya, memberikan kerusakan fisik tinggi membuat musuh tersebut tersentak. Nomor rantai pada serangan ini adalah berdasarkan dari nomor rantai sebelumnya.", "Medic tears through enemies previously stabbed with its knife, dealing high physical damage and causing enemies to flinch. Chain number in this attack is based on the previous chain number."), switch_lang("Harga serangan = 3", "Attack cost = 3")], 
							 adv: switch_lang("Mendapatkan Armor Penetration saat menggunakan serangan ini. Mendapatkan Damage Output kalau HP musuh 50% atau lebih sedikit dari HP maksimumnya.", "Gain Armor Penetration when this attack is performed. Gain Damage Output if enemies' HP is 50% or lower than its max HP."),
							 dis: switch_lang("Hanya dapat digunakan kalau serangan sebelumnya adalah \"Stab\" dan targetnya valid.", "Can only be used if the previous attack was \"Stab\" and the target is valid."),
							 mdf: ""}
							];
					tag = [{num: [1, 3, 4], text: ["Damage", "Multi", "Chainable"]},
						   {num: [1, 3, 5], text: ["Damage", "Single", "Knockback"]}, 
						   {num: [1, 3], text: ["Damage", "Single"]}, 
						   {num: [1, 3, 4], text: ["Damage", "Multi", "Chainable"]}, 
						   {num: [1, 3, 5], text: ["Damage", "Single", "Airborne"]}, 
						   {num: [1, 3, 8], text: ["Damage", "Multi", "Rapid"]}, 
						   {num: [1, 3, 4], text: ["Damage", "Single", "Chainable"]}, 
						   {num: [1, 3, 5], text: ["Damage", "Single", "Flinch"]}
						   ];
					#endregion
					unlock_lv = [1, 1, 3, 5, 7, 10, 15, 20];
					atk_cost = [1, 2, 1, 2, 1, 2, 3, 3];
					talent = [{m7: 0, m8: 0}, {s2: 0}, {s1: 0}, {m7: 0, m8: 0}, {s2: 0}, {m9: 0, s3: 0}, {m10: 0, m11: 0}, {m10: 0, m11: 0, s4: 0}];
					upgraded = [0, 0, 0, 0, 0, 0, 0, 0];
					adj_count = [0, 1, 1, 0, 0, 0, 0, 0];
					chain = [{valid: 1, val: 1}, {valid: 0, val: 0}, {valid: 0, val: 0}, {valid: 1, val: 1}, 
							 {valid: 0, val: 0}, {valid: 0, val: 0}, {valid: 1, val: -1}, {valid: 0, val: 0}];
				break;
				
			}
		break;
		case 2:		// DERV
			switch (bots_data.num) {
				case 1:				// Warrior
					title = ["Self Repair", "Redirect"];
					atk_cnt = array_length(title);
					desc = [{text: [switch_lang("Warrior menyembuhkan dirinya sendiri secara terus menerus dengan sedikit poin setelah tidak melakukan apapun selama beberapa detik.", "Warrior heals itself continuously with small points after doing nothing for a few seconds."), switch_lang("Akan terpicu secara otomatis kalau Warrior tidak bergerak dan tidak diserang selama waktu tertentu.", "Will trigger automatically if Warrior isn't moving and isn't attacked for a certain period of time.")], 
							 adv: "",
							 dis: "",
							 mdf: ""}, 
							{text: switch_lang("Warrior membatalkan serangannya dan membalikkan serangannya untuk menginterupsi serangan musuh.", "Warrior cancels its attack and reverses its attack to interrupt enemies' attack."), 
							 adv: switch_lang("Pengalihan yang berhasil akan meniadakan kerusakan, crowd-control, dan debuff dari serangan musuh. Mendapatkan Attack Speed kalau Warrior berhasil mengalihkan serangan musuh.", "A successful redirection will nullify damage, crowd-control, and debuffs from enemies' attacks. Gain Attack Speed if the Warrior successfully redirects enemies' attacks."),
							 dis: switch_lang("Hanya bisa digunakan saat Warrior sedang melakukan serangan dasar. Hanya bekerja pada serangan musuh yang berjarak dekat.", "Can only be used when the Warrior is using a basic attack. Only works on enemies' melee attacks."),
							 mdf: ""} 
							];
					tag = [{num: [4, 3], text: ["Healing", "Single"]},
						   {num: [2, 3, 2, 2], text: ["Defense", "Single", "Anti-CC", "Anti-Debuff"]}
						   ];
					unlock_lv = [1, 10];
					atk_cost = [0, 0];
					talent = [{s1: 0}, {s9: 0}];
					batk_limit = [10, 10];
					upgraded = [0, 0];
					adj_count = [0, 0];
				break;
				case 2:				// Archer
					title = ["Follow-Up Shot", "Twister Shot"];
					atk_cnt = array_length(title);
					desc = [{text: [switch_lang("Archer melakukan serangan dasar yang sama seperti sebelumnya, memberikan kerusakan yang bervariasi dan bisa menyebabkan crowd-control berdasarkan tipe serangan dasar sebelumnya.", "Archer performs the same basic attack as before, dealing various damage and can cause crowd-control based on the previous basic attack type."), switch_lang("Menggunakan skill ini akan mengonsumsi 1 tumpukan Multiplex Arrowhead kalau harga serangan sebelumnya adalah 1. Kalau bukan, maka akan mengonsumsi 2 tumpukan Multiplex Arrowhead.", "Using this skill consumes 1 stack of Multiplex Arrowhead if the attack cost of the previous basic attack type is 1. Otherwise, it consumes 2 stacks of Multiplex Arrowhead.")], 
							 adv: switch_lang("Tidak akan memulai ulang rangkaian serangan dasar.", "Won't reset the basic attack sequence."),
							 dis: switch_lang("Hanya bisa digunakan saat menyerang. Tidak bisa digunakan pada rangkaian terakhir dari serangan dasar atau saat dalam mode Pinpoint.", "Can only be used while attacking. Can't be used in the last basic attack sequence or while in Pinpoint mode."),
							 mdf: ""}, 
							{text: [switch_lang("Archer menembakkan anak panah yang berputar ke target, memberikan kerusakan fisik sedang saat terkena musuh dan memberikan kerusakan fisik sangat kecil saat berputar pada musuh.", "Archer shoots a spinning arrow at the target, dealing moderate physical damage on hit and dealing tiny physical damage per hit while spinning on the enemy."), switch_lang("Menggunakan skill ini mengonsumsi 1 tumpukan Multiplex Arrowhead. Tahan tombol DERV untuk mengonsumsi sampai dengan 3 tumpukan Multiplex Arrowhead. Setiap tumpukan tambahan yang dikonsumsi akan menambah jumlah putaran skill ini sebanyak 2.", "Using this skill consumes 1 stack of Multiplex Arrowheads. Hold down the DERV button to consume up to 3 stacks of Multiplex Arrowhead. Each extra stack consumed will increase the spin count of this skill by 2.")], 
							 adv: "",
							 dis: switch_lang("Tidak bisa digunakan saat dalam mode Pinpoint.", "Can't be used while in Pinpoint mode."),
							 mdf: ""}
							];
					tag = [{num: [1, 8], text: ["Damage", "Various"]},
						   {num: [1, 3, 8], text: ["Damage", "Single", "Rapid"]}
						   ];
					unlock_lv = [1, 10];
					atk_cost = [0, 0];
					talent = [{s1: 0}, {}];
					batk_limit = [10, 10];
					upgraded = [0, 0];
					adj_count = [0, 0];
				break;
				case 3:				// Medic
					file_text_decrypt(string_lower(bots_data.name) + ".txt");
					var up = ini_read_real("talents", "m12", 0);
					file_text_encrypt(string_lower(bots_data.name) + ".txt");

					if (!up) {
						title = ["Dart Throw"];
						desc = [{text: [switch_lang("Medic melempar dart berjarak dekat ke target, memberikan kerusakan rendah pada musuh.", "Medic throws short-ranged darts at the target, dealing low damage to enemies."), 
										switch_lang("Bisa ditumpuk. Tahan tombol DERV untuk melempar semua dart yang tersisa.", "Stackable. Hold down the DERV button to throw all remaining darts.")], 
								adv: "",
								dis: "",
								mdf: ""}
								];
						tag = [{num: [1, 3], text: ["Damage", "Single"]}
							];
						upgraded = [0];
					} else {
						title = ["Serum Throw"];
						desc = [{text: [switch_lang("Medic melempar serum berjarak dekat ke target, memberikan kerusakan rendah pada musuh. Serum-serum ini akan terisi secara otomatis saat mengenai musuh.", 
													"Medic throws short-ranged serums at the target, dealing low damage to enemies. These serums will be filled automatically if it hits an enemy."), 
										switch_lang("Bisa ditumpuk. Tahan tombol DERV untuk melempar semua serum yang tersisa. Mengambil serum yang terisi akan menyembuhkan Bot yang mengambilnya. Jumlah penyembuhannya berdasarkan pada jumlah kerusakan yang diberikan dan tidak bisa melebihi 5% dari HP maksimal milik Medic.", 
													"Stackable. Hold down the DERV button to throw all remaining serums. Picking up the filled serum will heal the one picking it up. The healing amount is based on the damage dealt and can't exceed 5% of the Medic's max HP.")], 
								adv: "",
								dis: "",
								mdf: ""}
								];
						tag = [{num: [1, 3, 4], text: ["Damage", "Single", "Healing"]}
							];
						upgraded = [1];
					}
					atk_cnt = array_length(title);
					unlock_lv = [1];
					atk_cost = [0];
					talent = [{s8: 0, s17: 0}];
					batk_limit = [10];
					adj_count = [1];
				break;
				
			}
		break;
		case 3:		// SPMV
			switch (bots_data.num) {
				case 1:				// Warrior
					title = ["Determination", "Sidecut", "Parry"];
					atk_cnt = array_length(title);
					desc = [{text: [switch_lang("Warrior menguatkan tubuhnya, meningkatkan Attack Scale dan Defense Scale nya selama durasi tertentu.", "Warrior strengthens its body, increasing its Attack Scale and Defense Scale for the specified duration."), 
									switch_lang("Kalau skill ini tidak dimodifikasi, tahan tombol SPMV untuk memicu \"Group Determination\", yang mana akan meningkatkan efektivitas buff sebesar 1.5x, durasi buff sebesar 2x, dan cooldown nya sebesar 3x. Efek ini berlaku untuk semua rekan tim.", "If this skill is unmodified, hold down the SPMV button to trigger \"Group Determination,\" which will multiply the buff's effectiveness by 1.5x, buff's duration by 2x, and its cooldown by 3x. This effect applies to all teammates.")], 
							 adv: "",
							 dis: switch_lang("Tidak bisa digunakan saat mengudara.", "Can't be used in mid-air."),
							 mdf: switch_lang("Kalau skill di bagian [A] diatur ke \"Sidecut\", bukan \"Determination,\" skill ini akan dimodifikasi. Kalau tidak, skill ini akan tetap tidak dimodifikasi dan bisa memicu \"Group Determination.\"", "If the skill in the [A] section is set to \"Sidecut\" instead of \"Determination,\" this skill will be modified. Otherwise, it remains unmodified and can trigger \"Group Determination.\"")}, 
							{text: [switch_lang("Warrior meluncur ke depan dan melakukan 2 serangan yang cepat, memberikan kerusakan fisik sedang kepada musuh.", "Warrior dashes forward and performs 2 quick attacks, dealing moderate physical damage to the enemies."), 
									switch_lang("Kalau skill ini tidak dimodifikasi, tahan tombol SPMV untuk mengonsumsi semua tumpukan skill ini. Setelah itu, Warrior melakukan skill ini sebanyak tumpukan yang dikonsumsi. Mendapatkan tumpukan saat melakukan gerakan ini akan otomatis mengonsumsi tumpukannya.", "If this skill is unmodified, hold down the SPMV button to trigger \"Sidecut Onslaught,\" which makes Warrior perform this skill based on the number of consumed stacks. Gaining stacks during this move will automatically consume the stacks.")], 
							 adv: switch_lang("Bisa ditumpuk. Mendapatkan Attack Speed selama durasi tertentu setelah mengonsumsi tumpukan.", "Stackable. Gain Attack Speed for a certain duration after consuming the stack."),
							 dis: switch_lang("Tidak bisa digunakan saat mengudara.", "Can't be used in mid-air."),
							 mdf: switch_lang("Kalau skill di bagian [B] diatur ke \"Determination\", bukan \"Sidecut,\" skill ini akan dimodifikasi. Kalau tidak, skill ini akan tetap tidak dimodifikasi dan bisa memicu \"Sidecut Onslaught.\"", "If the skill in the [B] section is set to \"Determination\" instead of \"Sidecut,\" this skill will be modified. Otherwise, it remains unmodified and can trigger \"Sidecut Onslaught.\"")}, 
							{text: [switch_lang("Warrior menghadang serangan jarak dekat musuh, menggantikan Physical Damage Reduction selama durasi tertentu.", "Warrior blocks enemies' melee attacks, substituting Physical Damage Reduction for the specified duration."), 
									switch_lang("Tahan tombol SPMV untuk durasi yang lebih lama. Kalau Warrior menggunakan skill ini tepat sebelum musuh menyerangnya, akan terpicu \"Perfect Parry.\" ", "Hold down the SPMV button for a longer duration. If the Warrior uses this skill right before the enemy attacks it, it will trigger \"Perfect Parry.\" "), 
									switch_lang("\"Perfect Parry\" akan meniadakan crowd-control dan debuff dari serangan musuh. \"Perfect Parry\" juga akan membuat penyerang tersentak dan memotong cooldown dari skill ini sebesar 50%.", "\"Perfect Parry\" will nullify crowd-control and debuffs from enemies' attacks. \"Perfect Parry\" will also make the attacker flinch and cut the cooldown of this skill by 50%.")], 
							 adv: switch_lang("Mendapatkan tambahan Physical Damage Reduction setelah menggunakan skill ini sampai animasinya selesai.", "Gain additional Physical Damage Reduction after using this skill until the animation ends."),
							 dis: switch_lang("Tidak bisa dimodifikasi. Tidak bisa bergerak saat menggunakan skill ini.", "Unmodifiable. Can't move while using this skill."),
							 mdf: ""} 
							];
					tag = [{num: [7, 3, 3], text: ["Buff", "Single", "Multi"]},
						   {num: [1, 3, 8], text: ["Damage", "Single", "Rapid"]},
						   {num: [2, 2, 2], text: ["Defense", "Anti-CC", "Anti-Debuff"]}
						   ];
					unlock_lv = [1, 10, 20];
					atk_cost = [0, 0, 0];
					talent = [{s4: 0}, {s8: 0}, {s14: 0}];
					batk_limit = [10, 10, 10];
					upgraded = [0, 0, 0];
					adj_count = [3, 1, 2];
					sk_mod = [{sect_a: [1, 2], sect_b: []}, {sect_a: [], sect_b: [1, 2]}, {sect_a: [], sect_b: []}];

					file_text_decrypt(string_lower(bots_data.name) + ".txt");
					var up = ini_read_real("talents", "m12", 0);
					file_text_encrypt(string_lower(bots_data.name) + ".txt");
					if (up) {
						title[2] = "Parry & Counter";
						desc[2].text[1] += switch_lang("Setelah itu, Warrior meluncurkan serangan yang kuat, memberikan kerusakan fisik tinggi dan memukul mundur musuh.", "Afterwards, Warrior launches a powerful attack that dealing high physical damage and causes knockback to enemies.");
						array_push(tag[2].num, 1);
						array_push(tag[2].text, "Damage");
						upgraded[2] = 1;
					}
				break;
				case 2:				// Archer
					title = ["Flick Shot", "Spike Shot", "Half-Moon Shot", "Supercharged Shot"];
					atk_cnt = array_length(title);
					desc = [{text: [switch_lang("Archer melakukan dash pendek ke belakang dan menembakkan anak panah setelahnya, memberikan kerusakan fisik sedang kepada musuh. Saat menggunakan skill ini, semua serangan musuh yang diarahkan ke Archer akan meleset.", "Archer performs a short backward dash and shoots an arrow afterward, dealing moderate physical damage to enemies. While using this move, all enemy attacks that are targeting Archer will miss."), 
									switch_lang("Mendapatkan 1 tumpukan Multiplex Arrowhead setelah menggunakan skill ini, bukan mengonsumsinya. Kalau skill ini tidak dimodifikasi, cooldown skill ini akan dipotong sebesar 50%.", "Gain 1 stack of Multiplex Arrowhead after using this skill instead of consuming it. If this skill is unmodified, the cooldown of this skill will be cut by 50%.")], 
							 adv: switch_lang("Mengakhiri cooldown Dash setelah menggunakan skill ini. Menghindari serangan musuh saat menggunakan skill ini akan menggandakan kerusakan dasar skill ini. Dash ke belakang lebih jauh dan menyerang dua kali saat dalam mode Pinpoint.", "Ends Dash cooldown after using this skill. Dodging enemy attacks while using this skill will double the base damage. Dash back further and attack twice while in Pinpoint mode."),
							 dis: switch_lang("Tidak bisa digunakan saat mengudara.", "Can't be used in mid-air."),
							 mdf: switch_lang("Kalau skill di bagian [B] diatur ke \"Spike Shot\", \"Electric Shot\", \"Half-Moon Shot\", atau \"Supercharged Shot,\" bukan \"Flick Shot,\" maka skill ini akan dimodifikasi. Kalau tidak, skill ini akan tetap tidak dimodifikasi.", "If the skill in the [B] section is set to \"Spike Shot\", \"Electric Shot\", \"Half-Moon Shot\", or \"Supercharged Shot\" instead of \"Flick Shot,\" this skill will be modified. Otherwise, it remains unmodified.")}, 
							{text: [switch_lang("Archer melompat ke depan dan mengisi daya busurnya untuk sesaat. Setelah itu, Archer menembakkan beberapa anak panah sekaligus ke target, memberikan kerusakan fisik sedang kepada musuh dan melambatkan musuh yang terkena serangan ini selama durasi tertentu.", "Archer jumps forward and charges its bow for a split second. Afterward, Archer shoots several arrows at once at the target, dealing moderate physical damage and slowing down the hit enemies for a certain duration."), 
									switch_lang("Mengonsumsi sampai dengan 3 tumpukan Multiplex Arrowhead. Setiap tumpukan yang dikonsumsi akan menambah jumlah anak panah sebanyak 1. Kalau skill ini tidak dimodifikasi, durasi perlambatan akan diperpanjang selama 2 detik.", "Consume up to 3 stacks of Multiplex Arrowhead. Each consumed stack will increase the arrow count by 1. If this skill is unmodified, the Slow duration will be extended for 2 seconds.")], 
							 adv: switch_lang("Mendapatkan Attack Scale selama durasi tertentu setelah menggunakan skill ini. Mengurangi Multi-Projectile Angle sebesar 35% saat dalam mode Pinpoint.", "Gain Attack Scale for a certain duration after using this skill. Reduces 35% of Multi-Projectile Angle while in Pinpoint mode."),
							 dis: switch_lang("Tidak bisa digunakan saat mengudara. Dash akan masuk cooldown setelah menggunakan skill ini.", "Can't be used in mid-air. Dash will enter cooldown after using this skill."),
							 mdf: switch_lang("Kalau skill di bagian [B] diatur ke \"Half-Moon Shot\" atau \"Supercharged Shot,\" bukan \"Spike Shot,\" maka skill ini akan dimodifikasi. Kalau tidak, skill ini akan tetap tidak dimodifikasi.", "If the skill in the [B] section is set to \"Half-Moon Shot\" or \"Supercharged Shot\" instead of \"Spike Shot,\" this skill will be modified. Otherwise, it remains unmodified.")},  
							{text: [switch_lang("Archer melompat ke depan dan menembak berulang kali, memberikan kerusakan fisik kecil kepada musuh. Serangan ini akan berlanjut sampai Archer mendarat ke tanah atau diinterupsi oleh musuh.", "Archer jumps forward and shoots down repeatedly, dealing low physical damage to enemies. The attack will continue until Archer lands on the ground or is interrupted by an enemy attack."), 
									switch_lang("Mengonsumsi sampai dengan 3 tumpukan Multiplex Arrowhead. Setiap tumpukan yang dikonsumsi akan menambah tinggi lompatan sebesar 15% dan kecepatan serangan sebesar 10%. Kalau skill ini tidak dimodifikasi, cooldown dari skill ini akan berkurang sebanyak 7x.", "Consume up to 3 stacks of Multiplex Arrowhead. Each consumed stack will increase jump height by 15% and attack speed by 10%. If this skill is unmodified, the cooldown of this skill will be reduced by 7x.")], 
							 adv: switch_lang("Mendapatkan Attack Speed selama durasi tertentu setelah menggunakan skill ini. Menggunakan skill ini saat dalam mode Pinpoint tidak akan mengonsumsi tumpukan Multiplex Arrowheads, tapi dianggap mengonsumsi 3 tumpukan Multiplex Arrowhead.", "Gain Attack Speed for a certain duration after using this skill. Using this skill while in Pinpoint mode won't consume stacks of Multiplex Arrowheads, but will count as consuming 3 stacks of Multiplex Arrowheads."),
							 dis: switch_lang("Tidak bisa digunakan saat mengudara. Mengakhiri mode Pinpoint kalau skill ini digunakan saat dalam mode Pinpoint.", "Can't be used in mid-air. End Pinpoint mode if this skill is used while in Pinpoint mode."),
							 mdf: switch_lang("Kalau skill di bagian [A] diatur ke \"Flick Shot\", \"Spike Shot\", atau \"Electric Shot,\" bukan \"Half-Moon Shot,\" maka skill ini akan dimodifikasi. Kalau tidak, skill ini akan tetap tidak dimodifikasi.", "If the skill in the [A] section is set to \"Flick Shot\", \"Spike Shot\", or \"Electric Shot\" instead of \"Half-Moon Shot,\" this skill will be modified. Otherwise, it remains unmodified.")}, 
							{text: [switch_lang("Archer mengisi energinya ke busurnya dan mengeluarkannya dengan kekuatan yang masif, memberikan kerusakan fisik mengerikan dan memukul mundur musuh.", "Archer charges its energy into its bow and releases it with massive power, dealing monstrous physical damage and causing knockback to enemies."), 
									switch_lang("Bisa diisi sampai tahap 4. Setiap tahap pengisian akan mengonsumsi 1 tumpukan Multiplex Arrowhead untuk menambah basis kerusakan skill ini sebesar 0.1x. Kalau skill ini tidak dimodifikasi, skill ini akan mulai mengisi daya dari tahap 3.", "Can be charged up to 4 phases. Each charge phase will consume 1 stack of Multiplex Arrowhead to increase the base damage of this skill by 0.1x. If this skill is unmodified, it will start charging from phase 3."), 
									switch_lang("Setelah diisi daya ke tahap 4, Archer akan masuk status Overcharge. Mengisi daya saat dalam status ini akan mengisi poin overload.", "Once charged up to phase 4, Archer will enter the Overcharge state. Charging in this state will charge the overload points.")], 
							 adv: [switch_lang("Mendapatkan tambahan kekuatan knockback dan serangan maksimal untuk setiap tahap pengisian. Mendapatkan 25% Charge Speed saat dalam mode Pinpoint.", "Gain additional max hit and knockback power for each charging phase. Gain 25% Charge Speed while in Pinpoint mode."),
								   switch_lang("Mendapatkan tambahan basis kerusakan sesuai dengan persentase Overcharge. Serangan pertama dalam status Overcharge akan menambah skala kerusakan serangan ini sebesar 50%.", "Gain extra base damage based on Overcharge percentage. The first attack in Overcharge state will increase the damage scale of this attack by 50%.")],
							 dis: switch_lang("Akan gagal kalau tidak diisi sama sekali. Setiap musuh yang terkena serangan ini akan mengurangi skala kerusakan serangan ini sebanyak 15% (tidak bisa lebih rendah dari 10%). Menambah cooldown sebesar 50% kalau Archer masuk ke status Overcharge.", "Will fail if not charged at all. Each enemy hit by this attack will reduce this attack's damage scale by 15% (can't be lower than 10%). Increases cooldown by 50% if Archer reaches Overcharge state."),
							 mdf: switch_lang("Kalau skill di bagian [A] diatur ke \"Flick Shot\", \"Spike Shot\", \"Electric Shot\", atau \"Half-Moon Shot,\" bukan \"Supercharged Shot,\" maka skill ini akan dimodifikasi. Kalau tidak, skill ini akan tetap tidak dimodifikasi.", "If the skill in the [A] section is set to \"Flick Shot\", \"Spike Shot\", \"Electric Shot\", or \"Half-Moon Shot\" instead of \"Supercharged Shot,\" this skill will be modified. Otherwise, it remains unmodified.")}, 
							];
					tag = [{num: [1, 3], text: ["Damage", "Single"]},
						   {num: [1, 3, 6], text: ["Damage", "Single", "Slow"]},
						   {num: [1, 3, 8], text: ["Damage", "Single", "Rapid"]},
						   {num: [1, 3, 5, 9], text: ["Damage", "Multi", "Knockback", "Nuke"]}
						   ];
					unlock_lv = [1, 5, 15, 25];
					atk_cost = [0, 0, 0, 0];
					talent = [{s16: 0, s17: 0}, {s5: 0, s17: 0}, {s19: 0}, {m11: 0, s15: 0, s17: 0}];
					batk_limit = [10, 10, 10, 10];
					upgraded = [0, 0, 0, 0];
					adj_count = [2, 1, 2, 1];
					sk_mod = [{sect_a: [], sect_b: [1, 2, 3, 4]}, {sect_a: [], sect_b: [2, 3, 4]}, {sect_a: [1, 2, 3], sect_b: []}, {sect_a: [1, 2, 3, 4], sect_b: []}];

					file_text_decrypt(string_lower(bots_data.name) + ".txt");
					var up = ini_read_real("talents", "m12", 0);
					file_text_encrypt(string_lower(bots_data.name) + ".txt");
					if (up) {
						title[1] = "Electric Shot";
						desc[1] = {text: [switch_lang("Archer melompat ke depan dan mengisi daya busurnya untuk sesaat. Setelah itu, Archer menembakkan anak panah elektrik ke target, memberikan kerusakan fisik masif kepada semua bot di dalam radius dan menimbulkan Stun kepada semua bot di dalam radius selama durasi tertentu.", "Archer jumps forward and charges its bow for a split second. Afterward, Archer shoots an electric arrow at the target, dealing massive physical damage and causing a stun to all bots within a radius for a certain duration."), 
										  switch_lang("Mengonsumsi sampai dengan 3 tumpukan Multiplex Arrowhead. Setiap tumpukan yang dikonsumsi akan menambah radius ledakan sebesar 10%. Kalau skill ini tidak dimodifikasi, durasi Stun akan diperpanjang selama 2 detik.", "Consume up to 3 stacks of Multiplex Arrowhead. Each consumed stack will increase the explosion radius by 10%. If this skill is unmodified, the Stun duration will be extended for 2 seconds.")], 
								   adv: switch_lang("Mendapatkan Attack Scale selama durasi tertentu setelah menggunakan skill ini. Mendapatkan 50% CC Power saat dalam mode Pinpoint.", "Gain Attack Scale for a certain duration after using this skill. Gain 50% CC Power while in Pinpoint mode."),
								   dis: switch_lang("Tidak bisa digunakan saat mengudara. Dash akan masuk cooldown setelah menggunakan skill ini.", "Can't be used in mid-air. Dash will enter cooldown after using this skill."),
								   mdf: switch_lang("Kalau skill di bagian [B] diatur ke \"Half-Moon Shot\" atau \"Supercharged Shot,\" bukan \"Electric Shot,\" maka skill ini akan dimodifikasi. Kalau tidak, skill ini akan tetap tidak dimodifikasi.", "If the skill in the [B] section is set to \"Half-Moon Shot\" or \"Supercharged Shot\" instead of \"Electric Shot,\" this skill will be modified. Otherwise, it remains unmodified.")};
						tag[1] = {num: [1, 3, 5], text: ["Damage", "Multi", "Stun"]};
						upgraded[1] = 3;
					}
				break;
				case 3:				// Medic
					title = ["Acid Vial", "Healing Potion", "Diminisher Knife"];
					atk_cnt = array_length(title);
					desc = [{text: [switch_lang("Medic menuangkan botol asam ke pisaunya, menggantikan jenis kerusakan dari semua serangan dasarnya menjadi kerusakan asam selama durasi tertentu.", "Medic pours a vial of acid into its knife, changing the damage type of all its basic attacks to acid damage for a certain duration."), 
									switch_lang("Tahan tombol SPMV untuk durasi yang lebih lama. Cooldown skill ini berdasarkan berapa lamanya durasi skill ini berlangsung. Kalau skill ini tidak dimodifikasi, cooldown dari skill ini akan dipotong sebesar 50%.", "Hold down the SPMV button for a longer duration. The cooldown of this skill is based on how long the duration of this skill lasts. If this skill is unmodified, the cooldown of this skill will be cut by 50%.")], 
							 adv: switch_lang("Mendapatkan Acid Damage Bonus selama durasi tertentu.", "Gain Acid Damage Bonus for a certain duration."),
							 dis: "",
							 mdf: switch_lang("Kalau skill di bagian [A] diatur ke \"Diminisher Knife,\" bukan \"Acid Vial,\" maka skill ini akan dimodifikasi. Kalau tidak, skill ini akan tetap tidak dimodifikasi.", "If the skill in the [A] section is set to \"Diminisher Knife\" instead of \"Acid Vial,\" this skill will be modified. Otherwise, it remains unmodified.")},
							{text: [switch_lang("Medic mengeluarkan ramuan penyembuh. Setelah digunakan, ramuan ini akan meregenerasi HP pengguna dengan persentase rendah selama durasi tertentu.", "Medic releases a healing potion. Once used, this potion will regenerate the user's HP at a low percentage for a certain duration."), 
									switch_lang("Medic bisa menggunakan ramuan ini untuk dirinya sendiri maupun diberikan ke rekan tim. Tahan tombol SPMV untuk menjatuhkan ramuan ini. Kalau skill ini tidak dimodifikasi, durasi dari skill ini akan diperpanjang sebesar 50%.", "Medic can either use the potion itself or pass the potion to teammates. Hold down the SPMV button to drop the potion. If this skill is unmodified, the duration of this skill will be extended by 50%.")], 
							 adv: "",
							 dis: "",
							 mdf: switch_lang("Kalau skill di bagian [A] diatur ke \"Diminisher Knife,\" bukan \"Healing Potion,\" maka skill ini akan dimodifikasi. Kalau tidak, skill ini akan tetap tidak dimodifikasi.", "If the skill in the [A] section is set to \"Diminisher Knife\" instead of \"Healing Potion,\" this skill will be modified. Otherwise, it remains unmodified.")}, 
							{text: [switch_lang("Medic melempar pisau yang dibuat secara khusus kepada target, memberikan kerusakan sedang pada musuh. Musuh yang terkena pisau ini akan menerima debuff tertentu sampai pisaunya dilepaskan.", "Medic throws a custom-made knife at the target, dealing moderate damage to the enemy. Enemies hit by this knife will take certain debuff until the knife is removed."), 
									switch_lang("Pisau ini akan hancur kalau jarak lemparannya terlalu jauh. Hanya masuk cooldown kalau pisaunya hancur. Rekan tim bisa mengambil pisau ini. Hanya bisa diambil secara manual setelah pisaunya mengenai musuh selama satu per empat dari waktu cooldown. Kalau skill ini tidak dimodifikasi, pengali debuff dari skill ini akan ditambah sebesar 0.5x.", "This knife will be destroyed if the throwing distance is too far. Only enter cooldown if the knife is destroyed. Teammates can also take this knife. Can be picked up manually after hitting an enemy for a quarter of cooldown time. If this skill is unmodified, the debuff multiplier of this skill will be increased by 0.5x.")], 
							 adv: "",
							 dis: switch_lang("Harus mengambil pisaunya secara manual atau sampai musuh yang terkena skill ini dikalahkan untuk menggunakan skill ini lagi.", "Must manually pick up the knife or until the hit enemy is defeated to use this skill again."),
							 mdf: switch_lang("Kalau skill di bagian [B] diatur ke \"Acid Vial\" atau \"Healing Potion,\" bukan \"Diminisher Knife,\" maka skill ini akan dimodifikasi. Kalau tidak, skill ini akan tetap tidak dimodifikasi.", "If the skill in the [B] section is set to \"Acid Vial\" or \"Healing Potion\" instead of \"Diminisher Knife,\" this skill will be modified. Otherwise, it remains unmodified.")}
							];
					tag = [{num: [7, 3], text: ["Buff", "Single"]},
						   {num: [4, 3], text: ["Healing", "Single"]},
						   {num: [1, 3, 6, 8], text: ["Damage", "Single", "Debuff", "Various"]}
						   ];
					unlock_lv = [1, 10, 25];
					atk_cost = [0, 0, 0];
					talent = [{s11: 0}, {s6: 0}, {s18: 0}];
					batk_limit = [10, 10, 10];
					upgraded = [0, 0, 0];
					adj_count = [0, 2, 2];
					chain = [{valid: 0, val: 0}, {valid: 0, val: 0}, {valid: 0, val: 0}];
					sk_mod = [{sect_a: [1, 3], sect_b: []}, {sect_a: [2, 3], sect_b: []}, {sect_a: [], sect_b: [1, 2, 3]}];
				break;
			}
		break;
		case 4:		// ULTI
			switch (bots_data.num) {
				case 1:				// Warrior
					title = ["Whirlwind Cut", "Devastating Ambush", "Daunting Shockwaves"];
					atk_cnt = array_length(title);
					desc = [{text: switch_lang("Warrior melakukan tebasan berputar berulang kali di sekitar tubuhnya, memberikan kerusakan fisik kecil kepada musuh. Setelah tebasan berputar ini selesai, Warrior meluncurkan tebasan keras ke depan, memberikan kerusakan fisik tinggi kepada musuh dan memukul mundur musuh.", "Warrior performs repeated spinning slashes around its body, dealing low physical damage to enemies. After the spinning slash ends, Warrior delivers a powerful blow forward, dealing high physical damage and causing knockback to enemies."), 
							 adv: switch_lang("Bisa bergerak saat menggunakan skill ini.", "Can move while using the skill."),
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Warrior menusuk ke depan dengan pedangnya, membuat musuh tersentak, dilanjutkan dengan uppercut yang melempar musuh ke udara. Setelah jeda sebentar, Warrior menghantam musuh dengan dahsyat, memberikan kerusakan fisik yang mengerikan kepada musuh dan membuatnya tersentak kembali.", "Warrior thrusts forward with its sword, causing the enemy to flinch, followed by an uppercut that launches the enemy airborne. After a short pause, the Warrior delivers a devastating pounce, dealing monstrous physical damage to the enemy and making the enemy flinch again."), 
									switch_lang("Tahan tombol ULTI setelah Warrior melompat untuk mengisi daya hantamannya.", "Hold down the ULTI button after Warrior jumps to charge the pounce attack.")], 
							 adv: switch_lang("Mendapatkan Armor Penetration dan Physical Damage Output untuk hantamannya berdasarkan persentase pengisian daya.", "Gain Armor Penetration and Physical Damage Output for the pounce attack based on charge percentage."),
							 dis: switch_lang("Tidak bisa digunakan saat mengudara.", "Can't be used in mid-air."),
							 mdf: ""},
							{text: [switch_lang("Warrior menguatkan pedangnya, mengaktifkan mode Shockwave. Semua serangan yang dilakukan oleh Warrior akan membuat gelombang kejut, memberikan kerusakan fisik sangat kecil berdasarkan serangan yang dilakukan dan membuat musuh terkena impair.", "Warrior strengthens its sword, activating Shockwave mode. All attacks made by the Warrior will create a shockwave, dealing tiny physical damage based on the attack made and impairing the hit enemies."), 
									switch_lang("Bisa dimatikan secara manual dengan menekan tombol ULTI. Cooldown skill ini berdasarkan dari lamanya skill ini digunakan dan baru akan dimulai setelah mencapai durasi maksimal atau dimatikan secara manual.", "Can be deactivated manually by pressing ULTI button. This skill's cooldown is based on the length of time this skill has been used and will only start after reaching the maximum duration or being deactivated manually.")], 
							 adv: switch_lang("Mendapatkan Critical Buildup dan Critical Damage saat dalam mode Shockwave.", "Gain Critical Buildup and Critical Damage while in Shockwave mode."),
							 dis: switch_lang("Tidak bisa digunakan saat mengudara.", "Can't be used in mid-air."),
							 mdf: ""} 
							];
					tag = [{num: [1, 3, 5, 8], text: ["Damage", "Multi", "Knockback", "Anti-Swarm"]},
						   {num: [1, 3, 5, 9], text: ["Damage", "Single", "Flinch", "Nuke"]},
						   {num: [8, 7, 3, 6], text: ["Enhance", "Buff", "Single", "Impair"]}
						  ];
					unlock_lv = [1, 15, 30];
					atk_cost = [0, 0, 0];
					talent = [{s13: 0}, {s10: 0}, {s20: 0}];
					batk_limit = [10, 10, 10];
					upgraded = [0, 0, 0];
					adj_count = [0, 1, 0];
				break;
				case 2:				// Archer
					title = ["Cloudburst Volley", "Harpoon Breakout", "Pinpoint Shooting"];
					atk_cnt = array_length(title);
					desc = [{text: [switch_lang("Archer menembakkan anak panah yang sangat banyak ke langit. Setelah penundaan selama beberapa detik, anak panah ini jatuh ke area target satu per satu dengan kecepatan yang sangat tinggi, memberikan kerusakan fisik sangat kecil kepada musuh untuk setiap anak panah.", "Archer shoots tremendous arrows into the sky. After a few seconds of delay, the arrows fell into the targeted area one by one at very high speed, dealing tiny physical damage per arrow to enemies."), 
									switch_lang("Menggunakan skill ini bisa mengonsumsi sampai 5 tumpukan Multiplex Arrowhead. Setiap tumpukan yang dikonsumsi akan menambah jumlah anak panah sebanyak 3.", "Using this skill can consume up to 5 stacks of Multiplex Arrowhead. Each consumed stack will increase the number of arrows by 3.")], 
							 adv: "",
							 dis: "",
							 mdf: ""}, 
							{text: [switch_lang("Archer menembakkan anak panah yang berbentuk seperti harpun ke target, memberikan kerusakan fisik sangat kecil kepada musuh. Archer bisa menarik harpun ini untuk memecah setiap harpun yang ada. Setelah ditarik, harpun ini akan pecah dan mengeluarkan bilah kecil, memberikan kerusakan fisik sedang kepada musuh.", "Archer shoots a harpoon-like arrow at the target, dealing tiny physical damage to the enemy. Archer can draw it to break each of the harpoons. Once drawn, the harpoons will break and release small blades, dealing moderate physical damage to the hit enemies."), 
									switch_lang("Musuh yang terkena serangan ini akan dilambatkan sampai harpunnya ditarik. Menggunakan skill ini bisa mengonsumsi 1 tumpukan Multiplex Arrowhead untuk menembakkan harpun spesial. Tahan tombol ULTI untuk menarik harpun yang ada.", "Enemies hit by this attack will be slowed down until the harpoon is drawn. Using this skill can consume 1 stack of Multiplex Arrowhead to shoot a special harpoon. Hold down the ULTI button to draw and break the harpoon.")], 
							 adv: switch_lang("Bisa ditumpuk. Mendapatkan Armor Damage ketika harpunnya ditarik. Bilah yang dikeluarkan memiliki kekuatan interupsi yang sempurna. Kerusakan ledakan dari harpun spesial akan dikalikan sebanyak 1.5x.", "Stackable. Gain Armor Damage when the harpoon is drawn. The released blades have perfect interruption power. Burst damage from special harpoons will be multiplied by 1.5x."),
							 dis: switch_lang("Setiap harpun mengurangi Agility milik Archer sebesar 10% sampai harpunnya ditarik.", "Each harpoon reduces the Archer's Agility by 10% until the harpoon is drawn."),
							 mdf: ""},
							{text: [switch_lang("Archer menajamkan pandangannya dan kekuatan anak panahnya, mengaktifkan mode Pinpoint. Semua serangan yang dilakukan oleh Archer akan mengaktifkan efek spesial dari serangan yang digunakan.", "Archer sharpens its vision and arrow power, activating Pinpoint mode. All attacks performed by Archer will activate the special effects of the used attack."),
									switch_lang("Menggunakan skill ini bisa mengonsumsi sampai 5 tumpukan Multiplex Arrowhead. Setiap tumpukan yang dikonsumsi akan memberikan Archer 1 tumpukan Precision. Tekan tombol ULTI untuk mengonsumsi 1 tumpukan Precision dan Archer akan menembakkan anak panah yang kuat ke target, memberikan kerusakan fisik rendah kepada musuh dan memukul mundur musuh.", "Using this skill can up to 5 stacks of Multiplex Arrowhead. Each consumed stack will grant 1 stack of Precision to Archer. Press the ULTI button to consume a stack of Precision and Archer will shoot powerful arrows at the target, dealing low physical damage and causing knockback to enemies."), 
									switch_lang("Bisa dimatikan secara manual dengan menekan tombol ULTI. Cooldown skill ini berdasarkan dari lamanya skill ini digunakan dan baru akan dimulai setelah mencapai durasi maksimal atau dimatikan secara manual.", "Can be deactivated manually by pressing ULTI button. This skill's cooldown is based on the length of time this skill has been used and will only start after reaching the maximum duration or being deactivated manually.")], 
							 adv: switch_lang("Mendapatkan Attack Scale dan Critical Damage saat dalam mode Pinpoint. Juga mendapatkan Accuracy dan Recoil Reduction yang sempurna saat dalam mode Pinpoint.", "Gain Attack Scale and Critical Damage while in Pinpoint mode. Also gain perfect Accuracy and Recoil Reduction while in Pinpoint mode."),
							 dis: switch_lang("Tidak bisa digunakan saat mengudara. Kehilangan pertahanan dan tidak bisa bergerak maupun melompat saat dalam mode Pinpoint. Jarak dash yang berkurang setengahnya saat dalam mode Pinpoint.", "Can't be used in mid-air. Loss defense and can't move or jump while in Pinpoint mode. Dash distance will be halved while in Pinpoint mode."),
							 mdf: ""},
							];
					tag = [{num: [1, 3, 8, 9], text: ["Damage", "Single", "Anti-Swarm", "Breakneck"]},
						   {num: [1, 3, 6, 8], text: ["Damage", "Single", "Slow", "Anti-Armor"]},
						   {num: [1, 3, 8, 7], text: ["Damage", "Single", "Enhance", "Buff"]}
						  ];
					unlock_lv = [1, 10, 30];
					atk_cost = [0, 0, 0];
					talent = [{s4: 0}, {s9: 0}, {s14: 0}];
					batk_limit = [10, 10, 10];
					upgraded = [0, 0, 0];
					adj_count = [3, 1, 1];
				break;
				case 3:				// Medic
					title = ["Medical Kit", "Corrosive Concentration", "CBC Booster"];
					atk_cnt = array_length(title);
					desc = [{text: [switch_lang("Medic mengeluarkan kotak medis. Setelah digunakan, kotak medis ini akan memulihkan HP pengguna dengan persentase tiggi.", "Medic takes out a medical kit. Once used, this medical kit will restore the user's HP at a high percentage."), 
									switch_lang("Medic bisa menggunakan kotak medis ini untuk dirinya sendiri maupun diberikan ke rekan tim. Tahan tombol SPMV untuk menjatuhkan kotak medis ini. Harus memecahkan kodenya untuk menggunakan perangkat ini. Medic akan melewati proses pemecahan kode.", "Medic can either use the medical kit itself or pass the medical kit to other Bots. Hold down the ULTI button to drop the medical kit. Must be decoded to use this device. Medic will skip the decoding process.")], 
							 adv: switch_lang("Mendapatkan Physical Damage Bonus dan Acid Damage Bonus selama durasi tertentu setelah menggunakan kotak medis.", "Gain Physical Damage Bonus and Acid Damage Bonus for a certain duration after using the medical kit."),
							 dis: switch_lang("Jumlah penyembuhan dan efektivitas buff akan berkurang setengahnya kalau pengguna gagal memecahkan kodenya.", "The healing amount and buff effectiveness will be halved if the user fails to decode."),
							 mdf: ""}, 
							{text: switch_lang("Medic melemparkan botol kaca yang berisi larutan asam ke target dan akan pecah saat terkena tanah. Setelah pecah, botol ini akan mengeluarkan konsentrasi korosif, memberikan kerusakan asam kecil per detik dan melambatkan semua Bot di dalam radius.", "Medic throws a glass bottle filled with acid concentration at the target and it will break if it hits the ground. Once broken, this bottle will release a corrosive concentration, dealing low acid damage per second and slowing all Bots within a radius."), 
							 adv: switch_lang("Mendapatkan Acid Damage Reduction selama durasi tertentu setelah menggunakan skill ini.", "Gain Acid Damage Reduction for a certain duration after using this skill."),
							 dis: switch_lang("Tidak akan mengeluarkan larutan asam kalau botolnya pecah di udara.", "Won't release its concentration if the bottle breaks in mid-air."),
							 mdf: ""},
							{text: [switch_lang("Medic mengeluarkan booster. Setelah digunakan, booster ini akan memulihkan HP pengguna dengan persentase tinggi dan memberikan pengguna buff tertentu selama durasi tertentu.", "Medic takes out a booster. Once used, this booster will regenerate the user's HP by a low percentage and gives the user certain buff for a certain duration."), 
									switch_lang("Medic bisa menggunakan booster ini untuk dirinya sendiri maupun diberikan ke rekan tim. Tahan tombol SPMV untuk menjatuhkan booster ini. Harus memecahkan kodenya untuk menggunakan perangkat ini. Medic akan melewati proses pemecahan kode.", "Medic can either use the booster itself or pass the booster to other Bots. Hold down the ULTI button to drop the booster. Must be decoded to use this device. Medic will skip the decoding process.")], 
							 adv: switch_lang("Mendapatkan buff yang kuat selama durasi tertentu setelah menggunakan booster ini.", "Gain a powerful buff for a certain duration after using the booster."),
							 dis: switch_lang("Durasi dan efektivitas buff akan berkurang setengahnya kalau pengguna gagal memecahkan kodenya.", "The duration and buff effectiveness will be halved if the user fails to decode."),
							 mdf: ""},
							];
					tag = [{num: [4, 3, 7], text: ["Healing", "Single", "Buff"]},
						   {num: [1, 3, 6, 8], text: ["DoT", "Multi", "Slow", "Anti-Swarm"]},
						   {num: [7, 3, 8, 9], text: ["Buff", "Single", "Various", "Overload"]}
						  ];
					unlock_lv = [1, 15, 30];
					atk_cost = [0, 0, 0];
					talent = [{s9: 0, s16: 0}, {s14: 0, s15: 0}, {s16: 0, s19: 0, s20: 0}];
					batk_limit = [10, 10, 10];
					upgraded = [0, 0, 0];
					adj_count = [2, 1, 1];

					file_text_decrypt(string_lower(bots_data.name) + ".txt");
					var tal_s20 = ini_read_real("talents", "s20", 0);
					file_text_encrypt(string_lower(bots_data.name) + ".txt");
					if (tal_s20) {
						adj_count[2] = 2;
					}
				break;
			}
		break;
		case 5:		// INTG
			#region			// Module
			title = ["Grave Hit", "Superbody", "Sequence Breaker", "Substitute Care", "Fatalism Prevention"];
			str_name = ["", "grave", "superbody", "seq_breaker", "sub_care", "fatal_prev"];
			atk_cnt = array_length(title);
			desc = [{text: [switch_lang("Mengaktifkan efek \"Serangan Maut\" pada kelas ini.", "Activates the \"Grave Hit\" effect on this class."), 
							switch_lang("Serangan Maut\nSetelah kelas ini memberikan serangan kritis ke musuh, kelas ini mendapatkan poin serangan maut. Setelah poin serangan maut mencapai 100 dan kelas ini memberikan serangan kritis ke musuh, serangan ini akan berubah menjadi serangan maut.", "Grave Hit\nOnce this class inflicts a critical hit on enemies, it gains grave hit points. Once the grave hit points reach 100 and this class inflicts a critical hit on enemies, it turns the critical hit into a grave hit."), 
							switch_lang("Serangan maut adalah serangan kritis, tapi skala kerusakan dari serangan ini bertambah sesuai dengan nilai Grave Damage.", "Grave hits are critical hits, but the damage scale of the attacks is increased by the Grave Damage value.")], 
					 adv: "",
					 dis: "",
					 mdf: ""}, 
					{text: [switch_lang("Mengaktifkan efek \"Superbody\" pada kelas ini.", "Activates the \"Superbody\" effect on this class."), 
							switch_lang("Superbody\nSetelah kelas ini kehilangan HP sebanyak 40% dari HP maksmimunya, kelas ini mendapatkan Super-Armor sebanyak HP yang hilang dari kelas ini. Super-Armor ini akan memudar seiring berjalannya waktu.", "Superbody\nOnce this class loses 40% HP of its maximum HP, it will gain a Super-Armor based on the lost HP of this class. This Super-Armor will fade over time.")], 
					 adv: "",
					 dis: "",
					 mdf: ""},
					{text: [switch_lang("Mengaktifkan efek \"Sequence Breaker\" pada kelas ini.", "Activates the \"Sequence Breaker\" effect on this class."), 
							switch_lang("Sequence Breaker\nSetelah kelas ini terkena serangan 5 serangan beruntun dalam waktu dekat, kelas ini bisa mengaktifkan efek ini dengan menekan tombol dash dalam waktu terbatas.", "Sequence Breaker\nAfter this class is hit by 5 consecutive enemy attacks in a short period of time, this class can activate this effect by pressing the dash button for a limited time."), 
							switch_lang("Setelah diaktifkan, kelas ini mendapatkan Ignore Interruption yang sempurna selama durasi tertentu dan menghentakkan tanah, memukul mundur semua musuh di dalam area. Setelah itu, kelas ini akan mendapatkan Super-Armor selama durasi tertentu. Super-Armor ini akan memudar seiring berjalannya waktu.", "Once activated, this class gains perfect Ignore Interruption for a certain duration and then stomps the ground, causing knockback to all enemies within the area. Afterwards, this class will gain Super-Armor for a certain duration. This Super-Armor will fade over time.")],
					 adv: "",
					 dis: "",
					 mdf: ""},
					{text: [switch_lang("Mengaktifkan efek \"Perawatan Substitusi\" pada kelas ini.", "Activates the \"Substitute Care\" effect on this class."), 
							switch_lang("Perawatan Substitusi\nKelas ini menyembuhkan rekan tim yang tidak berada di lapangan dengan persentase kecil. Ini hanya aktif kalau kelas ini juga tidak berada di lapangan.", "Substitute Care\nThis class heals teammates who are not on the field at a small percentage. It is only active if this class is also not on the field.")], 
					 adv: "",
					 dis: "",
					 mdf: ""},
					{text: [switch_lang("Mengaktifkan efek \"Fatalism Prevention\" pada kelas ini.", "Activates the \"Fatalism Prevention\" effect on this class."), 
							switch_lang("Fatalism Prevention\nKalau rekan tim yang berada di lapangan hampir dikalahkan, kelas ini mengganti rekan tim itu dengan cepat, mencegah rekan tim agar tidak dikalahkan. Kelas ini juga menyediakan penyembuhan cepat kepada rekan tim tersebut selama durasi tertentu.", "Fatalism Prevention\nIf a teammate on the field is about to be defeated, this class quickly replaces that teammate, preventing the teammate from being defeated. This class also provides a quick healing effect to the teammate for a certain duration.")], 
					 adv: "",
					 dis: "",
					 mdf: ""}
					];
			tag = [{num: [1, 3], text: ["Damage", "Single"]},
				   {num: [2], text: ["Armor"]},
				   {num: [2, 2, 5], text: ["Armor", "Survival", "Knockback"]},
				   {num: [4], text: ["Healing"]},
				   {num: [2, 4], text: ["Survival", "Healing"]}
				];
			
			for (var i = 0; i < atk_cnt+1; i++) {
				unlock_lv[i] = -1;
				atk_cost[i] = 0;
				talent[i] = {};
				batk_limit[i] = 1;
				upgraded[i] = 0;
				adj_count[i] = 0;
			}
			#endregion

			#region			// Effective Module
			atk_cnt += 1;
			file_text_decrypt(string_lower(bots_data.name) + ".txt");
			switch (bots_data.num) {
				case 1:				// Warrior
					unlock_lv = [1, 1, 1, ini_read_real("talents", "s19", 0), -1, -1];
					talent[0] = {s7: 0};
					talent[2] = {s5: 0};
					array_insert(title, 0, "Hunter's Intuition");
					array_insert(desc, 0, {text: switch_lang("[Serba bisa]\n\nWarrior mendapatkan buff berdasarkan kondisi dirinya sendiri dan memberikan debuff kepada musuh dalam kondisi tertentu.\n\n" + 
															 "Setelah Warrior menyerang musuh dan HP musuh tersebut di bawah 50% dari HP maksimumnya, musuh ini akan mendapatkan tanda \"Chased\". Musuh dengan tanda \"Chased\" akan diperlambat dan Warrior mendapatkan Agility sampai tandanya dihilangkan.\n\n" + 
															 "Kalau HP Warrior di bawah 30% dari HP maksimumnya, Warrior mendapatkan efek \"Retreat\", mendapatkan Defense sampai HP nya 30% atau lebih dari HP maksimumnya.\n\n" + 
															 "Hanya 1 tanda \"Chased\" yang bisa ada dalam satu waktu. Tanda ini akan dihilangkan kalau musuh dengan tanda ini dikalahkan. Menyerang musuh lain yang HP nya di bawah 50% dari HP maksimumnya akan memberikan tandanya ke musuh terakhir yang terkena serangan.",
															 "[All-rounder]\n\nWarrior gains buff based on self condition and give debuff to enemies under certain condition.\n\n" + 
															 "After Warrior attacks an enemy and the enemy's HP is below 50% of its max HP, this enemy will get a \"Chased\" mark. Enemies with the \"Chased\" mark will be slowed down and Warrior gains Agility until the mark is removed.\n\n" + 
															 "If Warrior's HP is below 30% of its maximum HP, Warrior gains the \"Retreat\" effect, gaining Defense until its HP is 30% of its maximum HP or higher.\n\n" + 
															 "Only 1 \"Chased\" mark can exist at a time. This mark will be removed if the enemy with this mark is defeated. Attacking other enemies with HP below 50% of their max HP will give a mark to the last enemy hit."), 
										   adv: "", dis: "", mdf: ""});
					array_insert(tag, 0, {num: [7, 6, 3], text: ["Buff", "Debuff", "Single"]});
				break;
				case 2:				// Archer
					unlock_lv = [1, 1, -1, -1, -1, -1];
					talent[0] = {s2: 0};
					array_insert(title, 0, "Spatial Precision");
					array_insert(desc, 0, {text: switch_lang("[Penyerang]\n\nArcher mengecek kondisi musuh di lapangan secara berkala. Archer memiliki efek \"Spacekeeper\", yang mana bisa meningkatkan Attack milik Archer kalau ada musuh diantara 400px sampai 650px dari Archer. Setiap musuh dalam jarak tersebut akan menambah tumpukan \"Spacekeeper\" sebanyak 1.\n\n" + 
															 "Efek \"Spacekeeper\" bisa ditumpuk dan akan disegarkan setiap 3 detik.", 
															 "[Attacker]\n\nArcher periodically checks the condition of enemies on the field. Archer has a \"Spacekeeper\" effect, which can increase Archer's Attack if any enemy is between 400px and 650px from Archer. Each enemy within that distance will increase the \"Spacekeeper\" stack by 1.\n\n" + 
															 "The \"Spacekeeper\" effect is stackable and will be refreshed every 3 seconds."), 
										   adv: "", dis: "", mdf: ""});
					array_insert(tag, 0, {num: [7, 3], text: ["Buff", "Single"]});
				break;
				case 3:				// Medic
					unlock_lv = [1, -1, -1, -1, 1, ini_read_real("talents", "s10", 0)];
					talent[0] = {s6: 0, s13: 0, s19: 0};
					array_insert(title, 0, "Guardian's Gift");
					array_insert(desc, 0, {text: switch_lang("[Penyembuh]\n\nMedic melempar ramuan penyembuh kecil ke rekan tim yang berada di lapangan secara berkala, memulihkan HP rekan tim dengan persentase kecil. Hanya aktif kalau HP rekan tim tidak penuh dan Medic tidak berada di lapangan.", 
															 "[Healer]\n\nMedic periodically throws small healing potions at a teammate on the field, restoring the teammate's HP by a low percentage. Only active if the teammate's HP is not full and the Medic is not on the field."), 
										   adv: "", dis: "", mdf: ""});
					array_insert(tag, 0, {num: [4, 7], text: ["Healing", "Support"]});
				break;
			}
			file_text_encrypt(string_lower(bots_data.name) + ".txt");
			#endregion
		break;
		case 6:		// UNIQ
			switch (bots_data.num) {
				case 1:				// Warrior
					title = ["ADS Adaptation"];
					desc = [{text: switch_lang("Warrior beradaptasi dengan kondisi dirinya sendiri di lapangan. Efek ini disebut sebagai \"Form\", yaitu efek yang berbasis poin dan dibatasi sampai 200 poin. Ini dibagi menjadi 3 jenis:\n\n" + 
												"Form Menyerang\nEfek \"Form\" akan berubah menjadi merah setelah Warrior merusak musuh. Dalam kondisi ini, Warrior mendapatkan poin Form dari:\n" + 
												"- Memberikan kerusakan ke musuh: mendapatkan 5 poin.\n" +  
												"- Memberikan kerusakan kritis ke musuh: mendapatkan 15 poin.\n" +  
												"- Mengalahkan musuh: mendapatkan 25 poin.\n" +  
												"Mengonsumsi semua poin dalam tipe ini akan menambah Attack milik Warrior selama durasi tertentu.\n\n" + 
												"Form Bertahan\nEfek \"Form\" akan berubah menjadi biru setelah Warrior mendapatkan kerusakan dari musuh. Dalam kondisi ini, Warrior mendapatkan poin Form dari:\n" + 
												"- Mendapatkan kerusakan dari musuh: mendapatkan 10 poin.\n" +  
												"- Mendapatkan kerusakan kritis dari musuh: mendapatkan 40 poin.\n" +  
												"- Mendapatkan kerusakan maut dari musuh: mendapatkan 200 poin.\n" +  
												"Mengonsumsi semua poin dalam tipe ini akan memberikan Warrior Super-Armor selama durasi tertentu.\n\n" + 
												"Form Restorasi\nEfek \"Form\" akan berubah menjadi hijau setelah Warrior disembuhkan. Dalam kondisi ini, Warrior mendapatkan poin Form dari:\n" + 
												"- Mendapatkan regenerasi HP: mendapatkan 20 poin.\n" +  
												"- Mendapatkan pemulihan HP: mendapatkan 50 poin.\n" +   
												"Mengonsumsi semua poin dalam tipe ini akan memulihkan HP Warrior dengan persentase sedang.\n\n" + 
												"Setelah poin Form nya mencapai maksimum, tahan tombol BATK untuk mengonsumsi semua poinnya.",

												"Warrior adapts to its own conditions on the field. This effect is called \"Form\", which is a point-based effect and is capped at 200 points. It is divided into 3 types:\n\n"+
												"Attack Form\nThe \"Form\" will turn red after Warrior damages enemies. In this form, Warrior gains Form points by:\n" + 
												"- Dealing damage to enemies: gain 5 points.\n" + 
												"- Inflicting critical hits on enemies: gain 15 points.\n" + 
												"- Defeating an enemy: gain 25 points.\n" + 
												"Consuming all points in this type will increase the Warrior's Attack for a certain duration.\n\n" + 
												"Defense Form\nThe \"Form\" will turn blue after the Warrior takes damage from enemies. In this form, Warrior Warrior gains Form points by:\n" + 
												"- Taking damage from enemies: gain 10 points.\n" + 
												"- Taking critical hits from enemies: gain 40 points.\n" + 
												"- Taking grave hits from enemies: gain 200 points.\n" + 
												"Consuming all points in this type will grant Warrior a Super-Armor for a certain duration.\n\n" + 
												"Restoration Form\nThe \"Form\" will turn green once Warrior is healed. In this form, Warrior gains Form points by:\n" + 
												"- Gaining HP regeneration: gain 20 points.\n" + 
												"- Gaining HP recovery: gain 50 points.\n" + 
												"Consuming all points in this type will restore Warrior's HP by a medium percentage.\n\n" + 
												"Once the Form points reach the maximum, hold down the BATK button to consume all its points."), 
							 adv: "", dis: "", mdf: ""}];
					tag = [{num: [7, 2, 4], text: ["Buff", "Armor", "Healing"]}];
					talent = [{s2: 0}];
					adj_count = [1];
				break;
				case 2:				// Archer
					title = ["Quiver of Convergence"];
					desc = [{text: switch_lang("Archer menyimpan anak panah spesial saat berada di lapangan. Anak panah ini disebut sebagai \"Multiplex Arrowhead\", yaitu efek yang berbasis tumpukan dan dibatasi sampai 4 tumpukan. Archer bisa mendapatkan tumpukan Multiplex Arrowheads dari:\n" + 
												"- Menghindari serangan musuh: mendapatkan 1 tumpukan.\n" + 
												"- Melakukan 5 serangan dasar: mendapatkan 1 tumpukan.\n" + 
												"- Melakukan 1 gerakan spesial: mendapatkan 1 tumpukan.\n" + 
												"- Melakukan 1 skill ultimate: mendapatkan 1 tumpukan.\n" + 
												"- Melakukan 1 skill switch-in: mendapatkan 2 tumpukan.\n" + 
												"- Melakukan 1 serangan gabungan: mendapatkan 2 tumpukan.\n\n" + 
												"Tumpukan ini bisa digunakan untuk melakukan skill turunan, menguatkan gerakan spesial, atau menguatkan skill ultimate.\n\n" + 
												"Tumpukan ini akan selalu didapatkan terlebih dahulu sebelum skill apapun mengonsumsi tumpukan ini.",

												"Archer stores special arrows while in the field. These arrows are called \"Multiplex Arrowhead\", which is a stack-based effect and is capped at 4 stacks. Archer can gain stacks of Multiplex Arrowheads by:\n" + 
												"- Dodging enemy attacks: gain 1 stack.\n" + 
												"- Performing 5 basic attacks: gain 1 stack.\n" + 
												"- Performing a special move: gain 1 stack.\n" + 
												"- Performing an ultimate skill: gain 1 stack.\n" + 
												"- Performing a switch-in skill: gain 2 stacks.\n" + 
												"- Performing a joint attack: gain 2 stacks.\n\n" + 
												"These stacks can be used to perform derivative skills, strengthen special moves, or strengthen ultimate skills.\n\n" + 
												"These stacks will always be gained first before any skill consumes these stacks."),  
							 adv: "", dis: "", mdf: ""}];
					tag = [{num: [8, 8], text: ["Enhance", "Various"]}];
					talent = [{s10: 0}];
					adj_count = [0];
				break;
				case 3:				// Medic
					title = ["Restoration Stream"];
					desc = [{text: switch_lang("Medic memiliki ramuan penyembuh yang bisa diisi ulang dari waktu ke waktu. Ramuan ini disebut sebagai \"Rejuvenating Potion\", yaitu efek yang berbasis poin dan dibatasi sampai 300 poin. Medic bisa mendapatkan poin Rejuvenating Potion dari:\n" + 
												"- Berada di lapangan: mendapatkan 10 poin per detik.\n" + 
												"- Tidak berada di lapangan: mendapatkan 3 poin per detik.\n" + 
												"- Melakukan 1 skill turunan: mendapatkan 5 poin.\n" + 
												"- Melakukan 1 gerakan spesial: mendapatkan 20 poin.\n" + 
												"- Melakukan 1 skill ultimate: mendapatkan 60 poin.\n" + 
												"- Melakukan 1 skill switch-in: mendapatkan 50 poin.\n" +
												"- Melakukan 1 skill switch-out: mendapatkan 10 poin.\n\n" + 
												"Setelah poin Rejuvenating Potion mencapai setidaknya 100 poin, tahan tombol BATK untuk mengonsumsi semua poinnya. Setelah dikonsumsi, Medic akan mengaktifkan efek \"Rejuvenating\", meregenerasi HP rekan tim yang berada di lapangan dengan persentase rendah selama durasi tertentu. Durasi dari efek ini adalah 5% dari poin yang dikonsumsi.\n\n" + 
												"Mengonsumsi poin Rejuvenating Potion dengan poin maksimal akan menambah buff Physical DMG Reduction dan Acid DMG Reduction dengan durasi yang sama. Efek Rejuvenating akan tetap aktif walaupun rekan tim yang berada di lapangan sudah diganti.",

												"Medic has a rechargeable healing potion that can be replenished over time. This potion is called \"Rejuvenating Potion\", which is a point-based effect and is limited to 300 points. Medic can gain points of Rejuvenating Potion by:\n" + 
												"- Staying on the field: gain 10 points per second.\n" + 
												"- Staying out of the field: gain 3 points per second.\n" + 
												"- Performing a derivative skill: gain 5 points.\n" + 
												"- Performing a special move: gain 20 points.\n" + 
												"- Performing an ultimate skill: gain 60 points.\n" + 
												"- Performing a switch-in skill: gain 50 points.\n" + 
												"- Performing a switch-out skill: gain 10 points.\n\n" + 
												"Once the Rejuvenating Potion points reach at least 100 points, hold down the BATK button to consume all its points. Once consumed, Medic will activate the \"Rejuvenating\" effect, regenerating teammates' HP on the field at a low percentage for a certain duration. The duration of this effect is 5% of the consumed points.\n\n" + 
												"Consuming Rejuvenating Potion points at maximum points will add Physical DMG Reduction and Acid DMG Reduction buffs for the same duration. The Rejuvenating effect will persist even if teammates on the field are switched."), 
							 adv: "", dis: "", mdf: ""}];
					tag = [{num: [4, 7], text: ["Healing", "Buff"]}];
					talent = [{s7: 0}];
					adj_count = [2];
				break;
			}
			
			#region		// Sk add data
			atk_cnt = array_length(title);
			unlock_lv = [1];
			atk_cost = [0];
			batk_limit = [1];
			upgraded = [0];
			chain = [{valid: 0, val: 0}];
			#endregion
		break;
		case 7:		// EXT
			var sk_retal = 0;
			var tal_retal = [0, 13, 13, 13];
			file_text_decrypt(string_lower(bots_data.name) + ".txt");
			sk_retal = ini_read_real("talents", "m"+string(tal_retal[bots_data.num]), 0);
			file_text_encrypt(string_lower(bots_data.name) + ".txt");
			
			switch (bots_data.num) {
				case 1:				// Warrior
					title = ["Wavecleaver", "Doom Drop", "Breaker Stance", "Fang Breaker", "Starfall Strike"];
					str_name = ["dodge", "retaliate", "switch_in", "switch_out", "joint"];
					atk_cnt = array_length(title);
					desc = [{text: [switch_lang("[Skill Menghindar]\n\nSetelah Warrior menghindari serangan musuh, Warrior melakukan tebasan kuat yang mengeluarkan gelombang kejut, memberikan kerusakan fisik kecil kepada musuh setiap serangan. Musuh yang terkena gelombang kejut ini akan dipukul mundur setiap kali mereka terkena serangan ini.", 
												"[Dodge Skill]\n\nAfter Warrior dodges an enemy attack, Warrior perform a powerful slash that releases a shockwave, dealing low physical damage per hit to enemies. Enemies hit by this shockwave will be knocked back each time they are hit."), 
									switch_lang("Kalau penyesuaian Jenis Penggunaan diatur ke \"Manual,\" tekan tombol BATK untuk melakukan skill ini sebelum waktu pelepasannya habis.", "If the Type of Use adjustment is set to \"Manual,\" press the BATK button to perform this skill before the release time runs out.")], 
							 adv: "", dis: "",
							 mdf: switch_lang("Skill ini disinkronkan dengan status BATK.", "This skill is synchronized with the BATK statuses.")}, 
							{text: [switch_lang("[Skill Pembalasan]\n\nSetelah Warrior menghindari serangan ultimate musuh, Warrior melompat ke musuh dan melakukan tusukan ke bawah, menguras HP penyerang dengan persentase rendah dan membuat penyerang tersentak.", 
												"[Retaliate Skill]\n\nAfter Warrior dodges an enemy's ultimate attack, Warrior jumps towards the attacker and performs a downward stab, depleting the attacker's HP at a low percentage and causing flinch to the attacker."), 
												switch_lang("Kalau penyesuaian Jenis Penggunaan diatur ke \"Manual,\" tekan tombol BATK untuk melakukan skill ini sebelum waktu pelepasannya habis.", "If the Type of Use adjustment is set to \"Manual,\" press the BATK button to perform this skill before the release time runs out.")], 
							 adv: "", dis: "", mdf: ""},
							{text: [switch_lang("[Skill Switch-In]\n\nWarrior mengambil ancang-ancang sebentar. Setelah itu, Warrior melakukan pukulan berat ke musuh, memberikan kerusakan fisik masif dan memukul mundur musuh.", 
												"[Switch-In Skill]\n\nWarrior takes a stance for a short period of time. Afterwards, Warrior performs a heavy blow to enemies, dealing massive physical damage and causing knockback to enemies."), 
									switch_lang("Tersedia setelah ada rekan tim yang terpukul mundur oleh musuh. Gunakan skill ini dengan cara:\n- Menekan logo Warrior saat tombolnya berkedip.\n- Menahan logo Warrior saat tombolnya berkedip dan berputar.", 
												"Available after a teammate is knocked back by enemies. Use this skill by:\n- Pressing the Warrior logo while the button is blinking.\n- Holding down the Warrior logo while the button is blinking and rotating.")], 
							 adv: "", dis: "",
							 mdf: switch_lang("Skill ini disinkronkan dengan status BATK.", "This skill is synchronized with the BATK statuses.")}, 
							{text: [switch_lang("[Skill Switch-Out]\n\nWarrior menusuk musuh yang ditargetkan dan melanjutkannya dengan uppercut untuk melempar musuh ke udara, memberikan kerusakan fisik sedang kepada musuh.", 
												"[Switch-Out Skill]\n\nWarrior impales the targeted enemy and follows with an uppercut to throw the enemy airborne, dealing moderate physical damage to the enemy."), 
									switch_lang("Akan terpicu secara otomatis setelah rekan tim melakukan serangan switch-in miliknya.", "Will trigger automatically after a teammate uses its switch-in attack.")], 
							 adv: "", dis: "",
							 mdf: switch_lang("Skill ini disinkronkan dengan status BATK.", "This skill is synchronized with the BATK statuses.")}, 
							{text: [switch_lang("[Serangan Gabungan]\n\nWarrior melompat dan melakukan 2 tebasan cepat, memberikan kerusakan fisik sedang kepada musuh. Setelah itu, Warrior menghantam ke arah musuh, memberikan kerusakan fisik tinggi ke semua musuh di dalam radius dan menjatuhkan mereka.", 
												"[Joint Attack]\n\nWarrior jumps and performs 2 quick slashes, dealing moderate physical damage to enemies. Afterwards, Warrior slams into enemies, dealing high physical damage to all enemies within the radius and knocking them down."), 
									switch_lang("Tebasan kedua akan membanting musuh ke tanah. Tersedia setelah ada musuh yang terlempar ke udara dan Warrior tidak berada di lapangan. Kalau penyesuaian Jenis Penggunaan diatur ke \"Manual,\" tekan logo Warrior saat tombolnya berputar untuk melakukan skill ini.",
												"The second slash will send the enemy to the ground. Available after an enemy is knocked airborne and Warrior is not on the field. If the Type of Use adjustment is set to manual, press the Warrior logo while the button is rotating to use this skill.")], 
							 adv: "", dis: "",
							 mdf: switch_lang("Skill ini disinkronkan dengan status SPMV.", "This skill is synchronized with the SPMV statuses.")}, 
							];
					tag = [{num: [1, 3, 5, 8], text: ["Damage", "Multi", "Knockback", "Rapid"]},
						   {num: [1, 3, 5, 9], text: ["Damage", "Single", "Flinch", "Surefire"]},
						   {num: [1, 3, 5], text: ["Damage", "Multi", "Knockback"]},
						   {num: [1, 3, 5], text: ["Damage", "Single", "Airborne"]},
						   {num: [1, 3, 5], text: ["Damage", "AoE", "Knockdown"]}
						];
					unlock_lv = [1, sk_retal, 10, 20, 30];
					talent = [{}, {}, {}, {}, {}];
					batk_limit = [1, 1, 1, 1, 1];
					upgraded = [0, 0, 0, 0, 0];
					adj_count = [1, 1, 0, 0, 1];
				break;
				case 2:				// Archer
					title = ["Twin Talon Shot", "Soulsnare Shot", "Cylone Shot", "Skyfall Barrage", "Predator's Trilogy"];
					str_name = ["dodge", "retaliate", "switch_in", "switch_out", "joint"];
					atk_cnt = array_length(title);
					desc = [{text: [switch_lang("[Skill Menghindar]\n\nSetelah Archer menghindari serangan musuh, Archer menembakkan 2 anak panah berat ke target, memberikan kerusakan fisik tinggi kepada musuh dan membuat musuh tersentak.", 
												"[Dodge Skill]\n\nAfter Archer dodges an enemy attack, Archer shoots 2 heavy arrows at once towards the target, dealing high physical damage to enemies and causing enemies to flinch."), 
									switch_lang("Kalau penyesuaian Jenis Penggunaan diatur ke \"Manual,\" tekan tombol BATK untuk melakukan skill ini sebelum waktu pelepasannya habis.", "If the Type of Use adjustment is set to \"Manual,\" press the BATK button to perform this skill before the release time runs out.")], 
							 adv: "", dis: "",
							 mdf: switch_lang("Skill ini disinkronkan dengan status BATK.", "This skill is synchronized with the BATK statuses.")}, 
							{text: [switch_lang("[Skill Pembalasan]\n\nSetelah Archer menghindari serangan ultimate musuh, Archer meluncurkan dirinya ke arah penyerang dan melakukan tembakkan jarak dekat, menguras HP penyerang dengan persentase rendah dan membuat penyerang terbanting.", 
												"[Retaliate Skill]\n\nAfter Archer dodges an enemy's ultimate attack, Archer launches itself at the attacker and performs a close range shot, depleting the attacker's HP at a low percentage and knock the attacker down."), 
												switch_lang("Kalau penyesuaian Jenis Penggunaan diatur ke \"Manual,\" tekan tombol BATK untuk melakukan skill ini sebelum waktu pelepasannya habis.", "If the Type of Use adjustment is set to \"Manual,\" press the BATK button to perform this skill before the release time runs out.")], 
							 adv: "", dis: "", mdf: ""},
							{text: [switch_lang("[Skill Switch-In]\n\nArcher menembakkan anak panah yang berputar ke target sebanyak 2 kali, memberikan kerusakan fisik sedang saat terkena musuh dan memberikan kerusakan fisik sangat kecil saat berputar pada musuh.", 
												"[Switch-In Skill]\n\nArcher shoots a spinning arrow twice at the target, dealing moderate physical damage on hit and dealing tiny physical damage per hit while spinning on the enemy."), 
									switch_lang("Tersedia setelah ada musuh yang diperlambat. Gunakan skill ini dengan cara:\n- Menekan logo Archer saat tombolnya berkedip.\n- Menahan logo Archer saat tombolnya berkedip dan berputar.", 
												"Available after an enemy is slowed down. Use this skill by:\n- Pressing the Archer logo while the button is blinking.\n- Holding down the Archer logo while the button is blinking and rotating.")], 
							 adv: "", dis: "",
							 mdf: switch_lang("Skill ini disinkronkan dengan status DERV.", "This skill is synchronized with the DERV statuses.")}, 
							{text: [switch_lang("[Skill Switch-Out]\n\nArcher menembakkan anak panah yang banyak ke langit. Setelah penundaan selama beberapa detik, anak panah ini jatuh ke area target satu per satu dengan kecepatan yang sangat tinggi, memberikan kerusakan fisik sangat kecil kepada musuh untuk setiap anak panah.", 
												"[Switch-Out Skill]\n\nArcher shoots a great number of arrows into the sky. After a short delay, the arrows fell into the targeted area one by one at very high speed, dealing tiny physical damage per arrow to enemies."), 
									switch_lang("Akan terpicu secara otomatis setelah rekan tim melakukan serangan switch-in miliknya.", "Will trigger automatically after a teammate uses its switch-in attack.")], 
							 adv: "", dis: "",
							 mdf: switch_lang("Skill ini disinkronkan dengan status SPMV.", "This skill is synchronized with the SPMV statuses.")}, 
							{text: [switch_lang("[Serangan Gabungan]\n\nArcher menembakkan beberapa anak panah sekaligus sebanyak 3 kali ke target, memberikan kerusakan fisik sangat kecil kepada musuh untuk setiap anak panah.", 
												"[Joint Attack]\n\nArcher shoots multiple arrows at once three times at the target, dealing tiny physical damage per arrow to the enemy."), 
									switch_lang("Tersedia setelah ada musuh yang terpukul mundur dan Archer tidak berada di lapangan. Kalau penyesuaian Jenis Penggunaan diatur ke \"Manual,\" tekan logo Archer saat tombolnya berputar untuk melakukan skill ini.",
												"Available after an enemy is knocked back and Archer is not on the field. If the Type of Use adjustment is set to manual, press the Archer logo while the button is rotating to use this skill.")], 
							 adv: "", dis: "",
							 mdf: switch_lang("Skill ini disinkronkan dengan status SPMV.", "This skill is synchronized with the SPMV statuses.")}, 
							];
					tag = [{num: [1, 3, 5], text: ["Damage", "Single", "Flinch"]},
						   {num: [1, 3, 5, 9], text: ["Damage", "Single", "Knockdown", "Surefire"]},
						   {num: [1, 3, 8], text: ["Damage", "Single", "Rapid"]},
						   {num: [1, 3, 8], text: ["Damage", "Single", "Rapid"]},
						   {num: [1, 3, 8], text: ["Damage", "Single", "Rapid"]}
						];
					unlock_lv = [1, sk_retal, 10, 20, 30];
					talent = [{}, {}, {}, {}, {}];
					batk_limit = [1, 1, 1, 1, 1];
					upgraded = [0, 0, 0, 0, 0];
					adj_count = [1, 1, 0, 0, 1];
				break;
				case 3:				// Medic
					title = ["Stasis Flask", "Abyss Flask", "Life Bloom", "Revital Burst", "Caustic Drip"];
					str_name = ["dodge", "retaliate", "switch_in", "switch_out", "joint"];
					atk_cnt = array_length(title);
					desc = [{text: [switch_lang("[Skill Menghindar]\n\nSetelah Medic menghindari serangan musuh, Medic melempar botol kaca yang berisi zat lengket ke penyerang, melambatkan penyerang selama durasi tertentu.", 
												"[Dodge Skill]\n\nAfter Medic dodges an enemy attack, Medic throws a glass bottle filled with a sticky substance at the attacker, slowing down the attacker for a certain duration."), 
									switch_lang("Kalau penyesuaian Jenis Penggunaan diatur ke \"Manual,\" tekan tombol BATK untuk melakukan skill ini sebelum waktu pelepasannya habis.", "If the Type of Use adjustment is set to \"Manual,\" press the BATK button to perform this skill before the release time runs out.")], 
							 adv: "", dis: "",
							 mdf: switch_lang("Skill ini disinkronkan dengan status SPMV.", "This skill is synchronized with the SPMV statuses.")}, 
							{text: [switch_lang("[Skill Pembalasan]\n\nSetelah Medic menghindari serangan ultimate musuh, Medic melempar botol kaca yang berisi zat korosif ke penyerang, mengurasi HP penyerang dengan persentase rendah.", 
												"[Retaliate Skill]\n\nAfter Medic dodges an enemy's ultimate attack, Medic throws a a glass bottle filled with corrosive substance at the attacker, depleting the attacker's HP at a low percentage."), 
									switch_lang("Kalau penyesuaian Jenis Penggunaan diatur ke \"Manual,\" tekan tombol BATK untuk melakukan skill ini sebelum waktu pelepasannya habis.", "If the Type of Use adjustment is set to \"Manual,\" press the BATK button to perform this skill before the release time runs out.")], 
							 adv: "", dis: "", mdf: ""},
							{text: [switch_lang("[Skill Switch-In]\n\nMedic melempar ramuan penyembuh yang terkondensasi ke rekan tim yang ditukar, memulihkan HP rekan tim dengan persentase sedang.", 
												"[Switch-In Skill]\n\nMedic throws a condensed healing potion at the switched teammate, restoring the teammate's HP at a medium percentage."), 
									switch_lang("Tersedia setelah ada rekan tim yang menerima serangan kritis dari musuh dan HP rekan tim tersebut di bawah 70% dari HP maksimumnya. Gunakan skill ini dengan cara:\n- Menekan logo Medic saat tombolnya berkedip.\n- Menahan logo Medic saat tombolnya berkedip dan berputar.", 
												"Available after a teammate receives a critical hit from enemies and the teammate's HP is below 70% of its maximum HP. Use this skill by:\n- Pressing the Medic logo while the button is blinking.\n- Holding down the Medic logo while the button is blinking and rotating.")], 
							 adv: "", dis: "",
							 mdf: switch_lang("Skill ini disinkronkan dengan status SPMV.", "This skill is synchronized with the SPMV statuses.")}, 
							{text: [switch_lang("[Skill Switch-Out]\n\nMedic melempar ramuan penyembuh ke tanah yang dekat dengan rekan tim, menyembuhkan rekan tim dengan persentase rendah per detik selama durasi tertentu.", 
												"[Switch-Out Skill]\n\nMedic throws a healing potion on the ground close to the teammate, healing the teammate at a low percentage per second for a certain duration."), 
									switch_lang("Akan terpicu secara otomatis setelah rekan tim melakukan serangan switch-in miliknya.", "Will trigger automatically after a teammate uses its switch-in attack.")], 
							 adv: "", dis: "",
							 mdf: switch_lang("Skill ini disinkronkan dengan status SPMV.", "This skill is synchronized with the SPMV statuses.")}, 
							{text: [switch_lang("[Serangan Gabungan]\n\nMedic melempar botol asam ke target. Kalau botol ini mengenai musuh, botol tersebut akan pecah dan mengikis musuh, memberikan kerusakan asam kecil per serangan selama durasi tertentu.", 
												"[Joint Attack]\n\nMedic throws an acid vial at the target. If this vial hit on enemies, it will break and erode them, dealing low acid damage per hit for a certain duration."), 
									switch_lang("Tersedia setelah ada musuh yang terpukul mundur dan Medic tidak berada di lapangan. Kalau penyesuaian Jenis Penggunaan diatur ke \"Manual,\" tekan logo Medic saat tombolnya berputar untuk melakukan skill ini.",
												"Available after an enemy is knocked back and Medic is not on the field. If the Type of Use adjustment is set to manual, press the Medic logo while the button is rotating to use this skill.")], 
							 adv: "", dis: "",
							 mdf: switch_lang("Skill ini disinkronkan dengan status SPMV.", "This skill is synchronized with the SPMV statuses.")}, 
							];
					tag = [{num: [6, 3], text: ["Slow", "Single"]},
						   {num: [1, 3, 9], text: ["Damage", "Multi", "Surefire"]},
						   {num: [4], text: ["Healing"]},
						   {num: [4], text: ["Healing"]},
						   {num: [1, 3], text: ["DoT", "Multi"]}
						];
					unlock_lv = [1, sk_retal, 10, 20, 30];
					talent = [{}, {}, {}, {}, {}];
					batk_limit = [1, 1, 1, 1, 1];
					upgraded = [0, 0, 0, 0, 0];
					adj_count = [1, 1, 0, 0, 1];

					file_text_decrypt(string_lower(bots_data.name) + ".txt");
					var up = ini_read_real("talents", "s5", 0);
					file_text_encrypt(string_lower(bots_data.name) + ".txt");
					if (up) {
						title[0] = "Stasis Bolt";
						desc[0].text = switch_lang("[Skill Menghindar]\n\nSetelah Medic menghindari serangan musuh, Medic melempar dart korosif ke penyerang, memberikan kerusakan asam kecil per detik dan melambatkan penyerang selama durasi tertentu.", 
												   "[Dodge Skill]\n\nAfter Medic dodges an enemy attack, Medic throws a corrosive dart at the attacker, dealing low acid damage per second and slowing down the attacker for a certain duration.");
						tag[0] = {num: [1, 3, 6], text: ["DoT", "Single", "Slow"]};
						upgraded[0] = 5;
						talent[0].s5 = 1;
					}
				break;
				
			}
		break;
	}
	
	if (type != 1) {
		for (var i = 0; i < atk_cnt; i++) {
			array_push(atk_cost, 0);
			array_push(chain, {valid: 0, val: 0});
		}
	}
	if (type != 3) {
		for (var i = 0; i < atk_cnt; i++) {
			array_push(sk_mod, {sect_a: [], sect_b: []});
		}
	}
	
	file_text_decrypt(string_lower(bots_data.name) + ".txt");
	for (var i = 0; i < atk_cnt; i++) {
		array_push(level, ini_read_real(sect_name[type], atk_name[i] + "_lv", 1));
		if (bots_data.level >= unlock_lv[i]) {
			array_push(unlocked, 1);
		} else {
			array_push(unlocked, 0);
		}
				
		var talent_names = struct_get_names(talent[i]);
		for (var j = 0; j < array_length(talent_names); j++) {
			talent[i][$ talent_names[j]] = ini_read_real("talents", talent_names[j], 0);
		}
	}
	file_text_encrypt(string_lower(bots_data.name) + ".txt");
	
	var db = ds_grid_create(17, atk_cnt+20);
	for (var i = 0; i < atk_cnt; i++) {
		var arr_val = [i+1, ((type == bots_skill_intg && i == 0) ? spr_intg[bots_data.num] : spr[bots_data.num][type]), title[i], desc[i], tag[i], unlocked[i], level[i], unlock_lv[i], 
					   talent[i], atk_cost[i], batk_limit[i], type, upgraded[i], adj_count[i], chain[i], sk_mod[i], ((array_length(str_name) > 0) ? str_name[i] : "")];
		for (var j = 0; j < array_length(arr_val); j++) {
			ds_grid_set(db, j, i, arr_val[j]);
		}
	}
	delete bots_data;
	ds_grid_destroy(db_wp);
	return db;
}

function db_create_skill_attr(db_skill, class_num, type, skill_num) {
	// class_num dimulai dari 1
	// db_skill = db_batk, db_derv, ..., db_exsk
	// type = 1-batk, 2-derv, 3-spmv, 4-ulti, 5-intg, 6-uniq, 7-ext
	var result = [];
	result[0] = [2, 2, 2, 2, 2, 2];
	result[1] = [switch_lang("Pengali", "Multiplier"), switch_lang("Kekuatan Interupsi", "Interruption Power"), switch_lang("Bonus Pengabaian Interupsi", "Ignore Interruption Bonus"), switch_lang("Jenis Pukulan", "Hit Type"), switch_lang("Jenis Kerusakan", "Damage Type"), switch_lang("Jenis Cooldown", "Cooldown Type")];
	
	var skill_data = db_get_row(db_skill, 6, skill_num);
	switch (type) {
		case 1:		// BATK
			switch (class_num) {
				case 1:		// Warrior
					switch (skill_num) {
						case 1:			// Slash
							result[2] = [switch_lang("ATK", "ATK"), 40, 10, switch_lang("Banyak", "Multi"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 2:			// Blow
							result[2] = [switch_lang("ATK", "ATK"), 65, 15, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 3:			// Thrust
							result[2] = [switch_lang("ATK", "ATK"), 45, 20, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 4:			// Double Slash
							result[2] = [switch_lang("ATK", "ATK"), 40, 10, switch_lang("Banyak", "Multi"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 5:			// Twin Thrust
							result[2] = [switch_lang("ATK", "ATK"), 45, 10, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 6:			// Uppercut
							result[2] = [switch_lang("ATK", "ATK"), 65, 15, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 7:			// Spinning Slash
							result[2] = [switch_lang("ATK", "ATK"), 50, 15, switch_lang("Banyak", "Multi"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 8:			// Slam
							result[2] = [switch_lang("ATK", "ATK"), 55, 10, switch_lang("Efek Area", "AoE"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 9:			// Charged Slash
							result[2] = [switch_lang("ATK", "ATK"), 70, 45, switch_lang("Banyak", "Multi"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 10:		// Smash
							result[2] = [switch_lang("ATK", "ATK"), 65, 20, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 11:		// Stab
							result[2] = [switch_lang("ATK", "ATK"), 70, 30, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
					}
				break;
				case 2:		// Archer
					switch (skill_num) {
						case 1:			// Ordinary Shot
							result[2] = [switch_lang("ATK", "ATK"), 30, 10, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 2:			// Heavy Shot
							result[2] = [switch_lang("ATK", "ATK"), 65, 15, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 3:			// Multi Shot
							result[2] = [switch_lang("ATK", "ATK"), 35, 20, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 4:			// Double Shot
							result[2] = [switch_lang("ATK", "ATK"), 35, 20, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 5:			// Parallel Shot
							result[2] = [switch_lang("ATK", "ATK"), 35, 20, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 6:			// Acidic Shot
							result[2] = [switch_lang("ATK", "ATK"), 45, 10, switch_lang("Tunggal", "Single"), switch_lang("Asam", "Acid"), switch_lang("Timeout", "Timeout")]; break;
						case 7:			// Piercing Shot
							result[2] = [switch_lang("ATK", "ATK"), 40, 10, switch_lang("Banyak", "Multi"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 8:			// Charged Shot
							result[2] = [switch_lang("ATK", "ATK"), 70, 40, switch_lang("Banyak", "Multi"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 9:			// Explosive Shot
							result[2] = [switch_lang("ATK", "ATK"), 65, 20, switch_lang("Efek Area", "AoE"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
					}
				break;
				case 3:		// Medic
					switch (skill_num) {
						case 1:			// Slash
							result[2] = [switch_lang("ATK", "ATK"), 35, 10, switch_lang("Banyak", "Multi"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 2:			// Blow
							result[2] = [switch_lang("ATK", "ATK"), 60, 15, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 3:			// Thrust
							result[2] = [switch_lang("ATK", "ATK"), 40, 15, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 4:			// Acidic Slash
							result[2] = [switch_lang("ATK", "ATK"), 35, 10, switch_lang("Banyak", "Multi"), switch_lang("Asam", "Acid"), switch_lang("Timeout", "Timeout")]; break;
						case 5:			// Uppercut
							result[2] = [switch_lang("ATK", "ATK"), 60, 15, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 6:			// Spinning Slash
							result[2] = [switch_lang("ATK", "ATK"), 50, 15, switch_lang("Banyak", "Multi"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 7:			// Stab
							result[2] = [switch_lang("ATK", "ATK"), 60, 15, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 8:			// Drag Out
							result[2] = [switch_lang("ATK", "ATK"), 70, 20, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
					}
				break;
			}
		break;	
		case 2:		// DERV
			switch (class_num) {
				case 1:		// Warrior
					switch (skill_num) {
						case 1:			// Self Repair
							result[2] = [switch_lang("Tidak Ada", "None"), 0, 0, switch_lang("Tidak Ada", "None"), switch_lang("Tidak Ada", "None"), switch_lang("Tidak Ada", "None")]; break;
						case 2:			// Redirect
							result[1][2] = switch_lang("Pengabaian Interupsi-Subs", "Ignore Interruption-Subs");
							result[2] = [switch_lang("Tidak Ada", "None"), 100, 100, switch_lang("Tunggal", "Single"), switch_lang("Tidak Ada", "None"), switch_lang("Timeout", "Timeout")]; break;
					}
				break;
				case 2:		// Archer
					switch (skill_num) {
						case 1:			// Follow-up Shot
							result[2] = [switch_lang("ATK", "ATK"), 50, 20, switch_lang("Bervariasi", "Various"), switch_lang("Bervariasi", "Various"), switch_lang("Tidak Ada", "None")]; break;
						case 2:			// Twister Shot
							result[2] = [switch_lang("ATK", "ATK"), 60, 20, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Tidak Ada", "None")]; break;
					}
				break;
				case 3:		// Medic
					switch (skill_num) {
						case 1:			// Dart/Serum Throw
							result[2] = [switch_lang("ATK", "ATK"), 40, 0, switch_lang("Tunggal", "Single"), switch_lang("Bervariasi", "Various"), switch_lang("Timeout", "Timeout")]; break;
					}
				break;
			}
		break;
		case 3:		// SPMV
			result[1][2] = switch_lang("Pengabaian Interupsi-Subs", "Ignore Interruption-Subs");
			switch (class_num) {
				case 1:		// Warrior
					switch (skill_num) {
						case 1:			// Determination
							result[2] = [switch_lang("Tidak Ada", "None"), 0, 70, switch_lang("Tidak Ada", "None"), switch_lang("Tidak Ada", "None"), switch_lang("Timeout", "Timeout")]; break;
						case 2:			// Sidecut
							result[2] = [switch_lang("ATK", "ATK"), 40, 70, switch_lang("Banyak", "Multi"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 3:			// Parry & Counter
							result[2] = [switch_lang("ATK", "ATK"), 100, 100, switch_lang("Banyak", "Multi"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
					}
				break;
				case 2:		// Archer
					switch (skill_num) {
						case 1:			// Flick Shot
							result[2] = [switch_lang("ATK", "ATK"), 70, 100, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						case 2:			// Spike/Electric Shot
							if (!sign(skill_data.upgraded)) {
								result[2] = [switch_lang("ATK", "ATK"), 70, 100, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")];
							} else {
								result[2] = [switch_lang("ATK", "ATK"), 100, 100, switch_lang("Banyak", "Multi"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")];
							}
							break;
						case 3:			// Half-Moon Shot
							result[2] = [switch_lang("ATK", "ATK"), 50, 100, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Penggunaan BATK", "BATK Usage")]; break;
						case 4:			// Supercharged Shot
							result[2] = [switch_lang("ATK", "ATK"), 90, 100, switch_lang("Banyak", "Multi"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
					}
				break;
				case 3:		// Medic
					switch (skill_num) {
						case 1:			// Acid Vial
							result[2] = [switch_lang("Tidak Ada", "None"), 0, 70, switch_lang("Tidak Ada", "None"), switch_lang("Tidak Ada", "None"), switch_lang("Timeout Dinamis", "Dynamic Timeout")]; break;
						case 2:			// Healing Potion
							result[2] = [switch_lang("HP Maks", "Max HP"), 0, 70, switch_lang("Tidak Ada", "None"), switch_lang("Tidak Ada", "None"), switch_lang("Timeout", "Timeout")]; break;
						case 3:			// Diminisher Knife
							result[2] = [switch_lang("ATK", "ATK"), 70, 70, switch_lang("Tunggal", "Single"), switch_lang("Bervariasi", "Various"), switch_lang("Timeout", "Timeout")]; break;
					}
				break;
			}
		break;
		case 4:		// ULTI
			result[1][2] = switch_lang("Pengabaian Interupsi-Subs", "Ignore Interruption-Subs");
			switch (class_num) {
				case 1:		// Warrior
					switch (skill_num) {
						case 1:			// Whirlwind Cut
							result[2] = [switch_lang("ATK", "ATK"), 80, 100, switch_lang("Banyak", "Multi"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						break;
						case 2:			// Devastating Ambush 
							result[2] = [switch_lang("ATK", "ATK"), 100, 100, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						break;
						case 3:			// Daunting Shockwaves
							result[2] = [switch_lang("Tidak Ada", "None"), 0, 100, switch_lang("Tidak Ada", "None"), switch_lang("Tidak Ada", "None"), switch_lang("Timeout Dinamis", "Dynamic Timeout")]; break;
						break;
					}
				break;
				case 2:		// Archer
					switch (skill_num) {
						case 1:			// Cloudburst Volley
							result[2] = [switch_lang("ATK", "ATK"), 60, 100, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						break;
						case 2:			// Harpoon Breakout
							result[2] = [switch_lang("ATK", "ATK"), 70, 100, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						break;
						case 3:			// Pinpoint Shooting
							result[2] = [switch_lang("ATK", "ATK"), 80, 100, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout Dinamis", "Dynamic Timeout")]; break;
						break;
					}
				break;
				case 3:		// Medic
					switch (skill_num) {
						case 1:			// Medical Kit
							result[2] = [switch_lang("HP Maks", "Max HP"), 0, 100, switch_lang("Tidak Ada", "None"), switch_lang("Tidak Ada", "None"), switch_lang("Timeout", "Timeout")]; break;
						break;
						case 2:			// Corrosive Concentration 
							result[2] = [switch_lang("ATK", "ATK"), 50, 100, switch_lang("Banyak", "Multi"), switch_lang("Asam", "Acid"), switch_lang("Timeout", "Timeout")]; break;
						break;
						case 3:			// CBC Booster
							result[2] = [switch_lang("HP Maks", "Max HP"), 0, 100, switch_lang("Tidak Ada", "None"), switch_lang("Tidak Ada", "None"), switch_lang("Timeout", "Timeout")]; break;
						break;
					}
				break;
			}
		break;
		case 5:		// INTG
		case 6:		// UNIQ
			result = [[], [], []];
		break;
		case 7:		// EXT
			result[1][2] = switch_lang("Pengabaian Interupsi-Subs", "Ignore Interruption-Subs");
			switch (class_num) {
				case 1:		// Warrior
					switch (skill_num) {
						case 1:			// Wavecleaver
							result[2] = [switch_lang("ATK", "ATK"), 70, 100, switch_lang("Banyak", "Multi"), switch_lang("Fisik", "Physical"), switch_lang("Tidak Ada", "None")]; break;
						break;
						case 2:			// Doom Drop
							result[2] = [switch_lang("HP Maks Musuh", "Enemy's Max HP"), 100, 100, switch_lang("Tunggal", "Single"), switch_lang("Absolut", "Absolute"), switch_lang("Tidak Ada", "None")]; break;
						break;
						case 3:			// Breaker Stance
							result[2] = [switch_lang("ATK", "ATK"), 70, 100, switch_lang("Banyak", "Multi"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						break;
						case 4:			// Fang Breaker
							result[2] = [switch_lang("ATK", "ATK"), 70, 100, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Tidak Ada", "None")]; break;
						break;
						case 5:			// Starfall Strike
							result[2] = [switch_lang("ATK", "ATK"), 70, 100, switch_lang("Efek Area", "AoE"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						break;
					}
				break;
				case 2:		// Archer
					switch (skill_num) {
						case 1:			// Twin Talon Shot
							result[2] = [switch_lang("ATK", "ATK"), 70, 100, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Tidak Ada", "None")]; break;
						break;
						case 2:			// Soulsnare Shot
							result[2] = [switch_lang("HP Maks Musuh", "Enemy's Max HP"), 100, 100, switch_lang("Tunggal", "Single"), switch_lang("Absolut", "Absolute"), switch_lang("Tidak Ada", "None")]; break;
						break;
						case 3:			// Cylone Shot
							result[2] = [switch_lang("ATK", "ATK"), 70, 100, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						break;
						case 4:			// Skyfall Barrage
							result[2] = [switch_lang("ATK", "ATK"), 40, 100, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Tidak Ada", "None")]; break;
						break;
						case 5:			// Predator's Trilogy
							result[2] = [switch_lang("ATK", "ATK"), 70, 100, switch_lang("Tunggal", "Single"), switch_lang("Fisik", "Physical"), switch_lang("Timeout", "Timeout")]; break;
						break;
					}
				break;
				case 3:		// Medic
					switch (skill_num) {
						case 1:			// Stasis Flask/Bolt
							if (!sign(skill_data.upgraded)) {
								result[2] = [switch_lang("Tidak Ada", "None"), 70, 100, switch_lang("Tunggal", "Single"), switch_lang("Tidak Ada", "None"), switch_lang("Tidak Ada", "None")]; 
							} else {
								result[2] = [switch_lang("ATK", "ATK"), 70, 100, switch_lang("Tunggal", "Single"), switch_lang("Asam", "Acid"), switch_lang("Tidak Ada", "None")]; 
							}
							break;
						break;
						case 2:			// Abyss Flask
							result[2] = [switch_lang("HP Maks Musuh", "Enemy's Max HP"), 100, 100, switch_lang("Tunggal", "Single"), switch_lang("Absolut", "Absolute"), switch_lang("Tidak Ada", "None")]; break;
						break;
						case 3:			// Life Bloom
							result[2] = [switch_lang("HP Maks", "Max HP"), 0, 100, switch_lang("Tidak Ada", "None"), switch_lang("Tidak Ada", "None"), switch_lang("Timeout", "Timeout")]; break;
						break;
						case 4:			// Revital Burst
							result[2] = [switch_lang("HP Maks", "Max HP"), 0, 100, switch_lang("Tidak Ada", "None"), switch_lang("Tidak Ada", "None"), switch_lang("Tidak Ada", "None")]; break;
						break;
						case 5:			// Caustic Drip
							result[2] = [switch_lang("ATK", "ATK"), 40, 100, switch_lang("Banyak", "Multi"), switch_lang("Asam", "Acid"), switch_lang("Timeout", "Timeout")]; break;
						break;
					}
				break;
			}
		break;
	}
	delete skill_data;
	return result;
}

function db_create_skill_dtl(db_skill, class_num, type, skill_num) {
	// class_num dimulai dari 1
	// db_skill = db_batk, db_derv, ..., db_exsk
	// type = 1-batk, 2-derv, 3-spmv, 4-ulti, 5-intg, 6-uniq, 7-ext
	var dtl_type = [];
	var dtl_text = [];
	var dtl_val = [];
	var attr = [];
	var opt = [];
	var val_mod = [];
	
	var skill_data = db_get_row(db_skill, 6, skill_num);
	switch (type) {
		case 1:		// BATK
			switch (class_num) {
				case 1:		// Warrior
					switch (skill_num) {
						case 1:			// Slash
							dtl_type = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kerusakan Rantai I", "Chain I Damage"), switch_lang("Kerusakan Rantai II", "Chain II Damage"), switch_lang("Kerusakan Rantai III", "Chain III Damage"), switch_lang("Kerusakan Rantai IV", "Chain IV Damage"), 
										switch_lang("Kerusakan Rantai V", "Chain V Damage"), switch_lang("Kerusakan Rantai VI", "Chain VI Damage"), switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 40, low: 3, mid: 1.5, high: 4}, {init: 45, low: 3, mid: 1.5, high: 4}, {init: 50, low: 3, mid: 1.5, high: 4}, {init: 55, low: 3, mid: 1.5, high: 4}, {init: 65, low: 4, mid: 1.5, high: 4}, 
									   {init: 75, low: 4, mid: 1.5, high: 5}, {init: 90, low: 4, mid: 1.5, high: 5}, {init: 3, low: 0, mid: 0, high: 0}, {init: 50, low: 0, mid: 0, high: 0}, {init: 0.1, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, 
									{unlocked: skill_data.talent.m7, affix: "", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m8, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, 
									   {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 2:			// Blow
							dtl_type = [1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Damage Output", "Damage Output"), switch_lang("Kekuatan Knockback", "Knockback Power"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 120, low: 8, mid: 3, high: 10}, {init: 5, low: 0, mid: 0, high: 0}, {init: 100, low: 0, mid: 0, high: 0}, {init: 0.5, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 36, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 35], amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 3:			// Thrust
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Armor Penetration", "Armor Penetration"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 70, low: 4, mid: 2, high: 4}, {init: 40, low: 0, mid: 0, high: 0}, {init: 0.2, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 36, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 4:			// Double Slash
							dtl_type = [1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kerusakan Rantai I", "Chain I Damage"), switch_lang("Kerusakan Rantai II", "Chain II Damage"), switch_lang("Kerusakan Rantai III", "Chain III Damage"), 
										switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 45, low: 3, mid: 1.5, high: 4}, {init: 50, low: 3, mid: 1.5, high: 4}, {init: 55, low: 4, mid: 1.5, high: 4}, {init: 65, low: 4, mid: 1.5, high: 5}, 
									   {init: 3, low: 0, mid: 0, high: 0}, {init: 50, low: 0, mid: 0, high: 0}, {init: 0.2, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "2*", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "2*", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "2*", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m9, affix: "2*", suffix: "%", upgradeable: 0}, 
									{unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, 
									   {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 5:			// Twin Thrust
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Armor Penetration", "Armor Penetration"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 45, low: 3, mid: 1.5, high: 4}, {init: 40, low: 0, mid: 0, high: 0}, {init: 0.2, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "2*", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 36, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 6:			// Uppercut
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kekuatan Airborne", "Airborne Power"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 70, low: 5, mid: 1.5, high: 5}, {init: 100, low: 0, mid: 0, high: 0}, {init: 0.5, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 35], amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 7:			// Spinning Slash
							dtl_type = [1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 55, low: 4, mid: 1.5, high: 5}, {init: 4, low: 0, mid: 0, high: 0}, {init: 50, low: 0, mid: 0, high: 0}, {init: 0.2, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "2*", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 8:			// Slam
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kekuatan Knockback", "Knockback Power"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 125, low: 10, mid: 3.5, high: 10}, {init: 80, low: 0, mid: 0, high: 0}, {init: 0.5, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 35], amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 9:			// Charged Slash
							dtl_type = [1, 1, 1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Tak Terisi", "Uncharged Damage"), switch_lang("Kerusakan Tahap I", "Phase I Damage"), switch_lang("Kerusakan Tahap II", "Phase II Damage"), switch_lang("Kerusakan Tahap III", "Phase III Damage"), switch_lang("Kekuatan Knockback", "Knockback Power"), 
										switch_lang("Ambang Batas Pengisian", "Charge Threshold"), switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 70, low: 5, mid: 1.5, high: 5}, {init: 100, low: 5, mid: 2, high: 5}, {init: 130, low: 7, mid: 2.5, high: 8}, {init: 185, low: 10, mid: 3, high: 11}, {init: 50, low: 0, mid: 0, high: 0}, 
									   {init: switch_lang("Sedang", "Medium"), low: 0, mid: 0, high: 0}, {init: 4, low: 0, mid: 0, high: 0}, {init: 50, low: 0, mid: 0, high: 0}, {init: 2, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m10, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: switch_lang("Tahap * ", "Phase * "), suffix: "%", upgradeable: 0}, 
									{unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 35], amp_num: 0}, 
									   {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 10:		// Smash
							dtl_type = [1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kerusakan Rantai I", "Chain I Damage"), switch_lang("Kerusakan Rantai II", "Chain II Damage"), switch_lang("Kerusakan Rantai III", "Chain III Damage"), switch_lang("Kekuatan Knockback", "Knockback Power"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 80, low: 5, mid: 2, high: 5}, {init: 90, low: 5, mid: 2, high: 5}, {init: 115, low: 7, mid: 2, high: 8}, {init: 150, low: 9, mid: 2.5, high: 9}, {init: 70, low: 0, mid: 0, high: 0}, {init: 0.5, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m11, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 35], amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 11:		// Stab
							dtl_type = [1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Bonus Kerusakan BATK", "BATK DMG Bonus"), switch_lang("Armor Penetration", "Armor Penetration"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 180, low: 13, mid: 4, high: 14}, {init: 40, low: 0, mid: 0, high: 0}, {init: 80, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 1}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 36, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
					}
				break;
				case 2:		// Archer
					switch (skill_num) {
						case 1:			// Ordinary Shot
							dtl_type = [1, 1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kerusakan Rantai I", "Chain I Damage"), switch_lang("Kerusakan Rantai II", "Chain II Damage"), switch_lang("Kerusakan Rantai III", "Chain III Damage"), 
										switch_lang("Kerusakan Rantai IV", "Chain IV Damage"), switch_lang("Kerusakan Rantai V", "Chain V Damage"), switch_lang("Kerusakan Rantai VI", "Chain VI Damage"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 25, low: 3, mid: 1.5, high: 3}, {init: 30, low: 3, mid: 1.5, high: 3}, {init: 35, low: 3, mid: 1.5, high: 4}, {init: 15, low: 2, mid: 1, high: 3}, 
									   {init: 55, low: 4, mid: 1.5, high: 4}, {init: 15, low: 3, mid: 1.5, high: 3}, {init: 75, low: 4, mid: 1.5, high: 5}, {init: 0.1, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "2*", suffix: "%", upgradeable: 0}, 
									{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m6, affix: "3*", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m7, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, 
									   {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 2:			// Heavy Shot
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Damage Output", "Damage Output"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 110, low: 8, mid: 3, high: 8}, {init: 5, low: 0, mid: 0, high: 0}, {init: 0.5, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 36, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 3:			// Multi Shot
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Jumlah Anak Panah", "Arrow Count"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 25, low: 2, mid: 1, high: 2}, {init: 2, low: 0.2, mid: 0.1, high: 0.1}, {init: 0.2, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 36, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 4:			// Double Shot
							dtl_type = [1, 1];
							dtl_text = [switch_lang("Kerusakan Dasar", "Base Damage"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 60, low: 1.5, mid: 1, high: 1.5}, {init: 0.2, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 5:			// Parallel Shot
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Jumlah Anak Panah", "Arrow Count"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 25, low: 2, mid: 1, high: 2}, {init: 2, low: 0.2, mid: 0.1, high: 0.1}, {init: 0.3, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 36, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 6:			// Acidic Shot
							dtl_type = [1, 1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kerusakan Rantai I", "Chain I Damage"), switch_lang("Kerusakan Rantai II", "Chain II Damage"), switch_lang("Kerusakan Rantai III", "Chain III Damage"), 
										switch_lang("Kerusakan Rantai IV", "Chain IV Damage"), switch_lang("Kerusakan Rantai V", "Chain V Damage"), switch_lang("Kerusakan Rantai VI", "Chain VI Damage"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 25, low: 3, mid: 1.5, high: 3}, {init: 30, low: 3, mid: 1.5, high: 3}, {init: 35, low: 3, mid: 1.5, high: 4}, {init: 15, low: 2, mid: 1, high: 3}, 
									   {init: 55, low: 4, mid: 1.5, high: 4}, {init: 15, low: 3, mid: 1.5, high: 3}, {init: 75, low: 4, mid: 1.5, high: 5}, {init: 0.2, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "2*", suffix: "%", upgradeable: 0}, 
									{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m8, affix: "3*", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m9, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, 
									   {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 7:			// Piercing Shot
							dtl_type = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kerusakan Rantai I", "Chain I Damage"), switch_lang("Kerusakan Rantai II", "Chain II Damage"), switch_lang("Kerusakan Rantai III", "Chain III Damage"), switch_lang("Kerusakan Rantai IV", "Chain IV Damage"), 
										switch_lang("Kerusakan Rantai V", "Chain V Damage"), switch_lang("Kerusakan Rantai VI", "Chain VI Damage"), switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 25, low: 3, mid: 1.5, high: 3}, {init: 30, low: 3, mid: 1.5, high: 3}, {init: 35, low: 3, mid: 1.5, high: 4}, {init: 15, low: 2, mid: 1, high: 3}, {init: 55, low: 4, mid: 1.5, high: 4}, 
									   {init: 15, low: 3, mid: 1.5, high: 3}, {init: 75, low: 4, mid: 1.5, high: 5}, {init: 2, low: 0, mid: 0, high: 0}, {init: 0, low: 0, mid: 0, high: 0}, {init: 0.2, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "2*", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, 
									{unlocked: skill_data.talent.m10, affix: "3*", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m10, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, 
									   {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 8:			// Charged Shot
							dtl_type = [1, 1, 1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Tak Terisi", "Uncharged Damage"), switch_lang("Kerusakan Tahap I", "Phase I Damage"), switch_lang("Kerusakan Tahap II", "Phase II Damage"), switch_lang("Kerusakan Tahap III", "Phase III Damage"), switch_lang("Kekuatan\nKnockback", "Knockback Power"), 
										switch_lang("Ambang Batas Pengisian", "Charge Threshold"), switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 60, low: 5, mid: 1.5, high: 5}, {init: 90, low: 5, mid: 2, high: 6}, {init: 130, low: 8, mid: 3, high: 8}, {init: 200, low: 10, mid: 4, high: 11}, {init: 50, low: 0, mid: 0, high: 0}, 
									   {init: switch_lang("Tinggi", "High"), low: 0, mid: 0, high: 0}, {init: 2, low: 0, mid: 0, high: 0}, {init: 0, low: 0, mid: 0, high: 0}, {init: 2, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m11, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: switch_lang("Tahap * ", "Phase * "), suffix: "%", upgradeable: 0}, 
									{unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: switch_lang("Tahap + ", "Phase + "), suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 35], amp_num: 0}, 
									   {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 9:			// Explosive Shot
							dtl_type = [1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Radius Ledakan", "Explosion Radius"), switch_lang("Kekuatan Knockback", "Knockback Power"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 110, low: 8, mid: 3, high: 8}, {init: 100, low: 0, mid: 0, high: 0}, {init: 80, low: 0, mid: 0, high: 0}, {init: 2, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 36, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 35], amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
					}
				break;
				case 3:		// Medic
					switch (skill_num) {
						case 1:			// Slash
							dtl_type = [1, 1, 1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kerusakan Rantai I", "Chain I Damage"), switch_lang("Kerusakan Rantai II", "Chain II Damage"), switch_lang("Kerusakan Rantai III", "Chain III Damage"), switch_lang("Kerusakan Rantai IV", "Chain IV Damage"), 
										switch_lang("Kerusakan Rantai V", "Chain V Damage"), switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 12, low: 1, mid: 0.5, high: 1}, {init: 15, low: 1, mid: 0.5, high: 1}, {init: 18, low: 1, mid: 1, high: 1}, {init: 22, low: 1.5, mid: 1, high: 1}, {init: 13, low: 1, mid: 0.5, high: 1}, 
									   {init: 35, low: 2, mid: 1, high: 2}, {init: 3, low: 0, mid: 0, high: 0}, {init: 50, low: 0, mid: 0, high: 0}, {init: 0.1, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m7, affix: "2*", suffix: "%", upgradeable: 0}, 
									{unlocked: skill_data.talent.m8, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, 
									   {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 2:			// Blow
							dtl_type = [1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Damage Output", "Damage Output"), switch_lang("Kekuatan Knockback", "Knockback Power"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 60, low: 4, mid: 1.5, high: 3}, {init: 5, low: 0, mid: 0, high: 0}, {init: 100, low: 0, mid: 0, high: 0}, {init: 0.5, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 36, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 35], amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 3:			// Thrust
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Armor Penetration", "Armor Penetration"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 25, low: 2, mid: 1.5, high: 2}, {init: 40, low: 0, mid: 0, high: 0}, {init: 0.2, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 36, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 4:			// Acidic Slash
							dtl_type = [1, 1, 1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kerusakan Rantai I", "Chain I Damage"), switch_lang("Kerusakan Rantai II", "Chain II Damage"), switch_lang("Kerusakan Rantai III", "Chain III Damage"), switch_lang("Kerusakan Rantai IV", "Chain IV Damage"), 
										switch_lang("Kerusakan Rantai V", "Chain V Damage"), switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 12, low: 1, mid: 0.5, high: 1}, {init: 15, low: 1, mid: 0.5, high: 1}, {init: 18, low: 1, mid: 1, high: 1}, {init: 22, low: 1.5, mid: 1, high: 1}, {init: 13, low: 1, mid: 0.5, high: 1}, 
									   {init: 35, low: 2, mid: 1, high: 2}, {init: 3, low: 0, mid: 0, high: 0}, {init: 50, low: 0, mid: 0, high: 0}, {init: 0.1, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m7, affix: "2*", suffix: "%", upgradeable: 0}, 
									{unlocked: skill_data.talent.m8, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, 
									   {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 5:			// Uppercut
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kekuatan Airborne", "Airborne Power"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 35, low: 3, mid: 1.5, high: 2}, {init: 70, low: 0, mid: 0, high: 0}, {init: 0.5, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 35], amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 6:			// Spinning Slash
							dtl_type = [1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Rantai I", "Chain I Damage"), switch_lang("Kerusakan Rantai II", "Chain II Damage"), switch_lang("Attack Speed", "Attack Speed"), switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 12, low: 1, mid: 0.5, high: 1}, {init: 15, low: 1, mid: 1, high: 1}, {init: 8, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}, {init: 70, low: 0, mid: 0, high: 0}, {init: 0.3, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "2*", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m9, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 36, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 7:			// Stab
							dtl_type = [1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kerusakan Rantai I", "Chain I Damage"), switch_lang("Kerusakan Rantai II", "Chain II Damage"), switch_lang("Kerusakan Rantai III", "Chain III Damage"), switch_lang("Armor Penetration", "Armor Penetration"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 18, low: 1, mid: 1, high: 1}, {init: 25, low: 1.5, mid: 1, high: 1}, {init: 35, low: 2, mid: 1, high: 1.5}, {init: 50, low: 3, mid: 1, high: 2}, {init: 60, low: 0, mid: 0, high: 0}, {init: 0.2, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m10, affix: "", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m11, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 1}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 8:			// Drag Out
							dtl_type = [1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Rantai I", "Chain I Damage"), switch_lang("Kerusakan Rantai II", "Chain II Damage"), switch_lang("Kerusakan Rantai III", "Chain III Damage"), switch_lang("Armor Penetration", "Armor Penetration"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 50, low: 3, mid: 1, high: 3}, {init: 70, low: 5, mid: 2, high: 4}, {init: 110, low: 8, mid: 3.5, high: 7}, {init: 80, low: 0, mid: 0, high: 0}, {init: 1, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m10, affix: "", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m11, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 1}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
					}
				break;
				
			}
		break;
		case 2:		// DERV
			switch (class_num) {
				case 1:		// Warrior
					switch (skill_num) {
						case 1:			// Self Repair
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Jumlah Penyembuhan", "Healing Amount"), switch_lang("Interval", "Interval"), switch_lang("Jeda", "Delay")];
							dtl_val = [{init: 100, low: 150, mid: 50, high: 150}, {init: 5, low: -0.2, mid: -0.1, high: -0.1}, {init: 3, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: [11, 40], amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
						case 2:			// Redirect
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Attack Speed", "Attack Speed"), switch_lang("Durasi Buff", "Buff Duration"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 10, low: 1, mid: 0.5, high: 0.15}, {init: 3, low: 0.2, mid: 0.1, high: 0.3}, {init: 4, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 40, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [30, 39], amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
					}
				break;
				case 2:		// Archer
					switch (skill_num) {
						case 1:			// Follow-up Shot
							dtl_type = [1, 1];
							dtl_text = [switch_lang("Skala Kerusakan", "Damage Scale"), switch_lang("Skala Kekuatan CC", "CC Power Scale")];
							dtl_val = [{init: 25, low: 3, mid: 1, high: 2}, {init: 70, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}];
							opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
						case 2:			// Twister Shot
							dtl_type = [1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kerusakan Putaran", "Spin Damage"), switch_lang("Jumlah Putaran", "Spin Count"), switch_lang("Interval", "Interval")];
							dtl_val = [{init: 65, low: 4, mid: 1.5, high: 4}, {init: 12, low: 1, mid: 0.5, high: 1}, {init: 4, low: 0, mid: 0, high: 0}, {init: 0.15, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 37, scale_num: 38, amp_num: 0}, {inv: 0, flat_num: 37, scale_num: 38, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
					}
				break;
				case 3:		// Medic
					switch (skill_num) {
						case 1:			// Dart/Serum Throw
							dtl_type = [1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Akurasi", "Accuracy"), switch_lang("Tumpukan Maksimum", "Maximum Stacks"), switch_lang("Cooldown", "Cooldown"), switch_lang("Tipe Perangkat", "Device Type")];
							dtl_val = [{init: 30, low: 4, mid: 1.5, high: 3}, {init: 70, low: 0, mid: 0, high: 0}, {init: 2, low: 0.2, mid: 0.1, high: 0.1}, {init: 4, low: 0, mid: 0, high: 0}, {init: switch_lang("Ofensif", "Offensive"), low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 1}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 37, scale_num: 38, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 40, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
					}
				break;
			}
		break;
		case 3:		// SPMV
			switch (class_num) {
				case 1:		// Warrior
					switch (skill_num) {
						case 1:			// Determination
							dtl_type = [1, 1, 1, 1];
							dtl_text = [switch_lang("Attack Scale", "Attack Scale"), switch_lang("Defense Scale", "Defense Scale"), switch_lang("Durasi Buff", "Buff Duration"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 10, low: 1, mid: 0.5, high: 0.15}, {init: 10, low: 1, mid: 0.5, high: 0.15}, {init: 7, low: 0, mid: 0, high: 0}, {init: 10, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 44, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 44, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [30, 43], amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 2:			// Sidecut
							dtl_type = [1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Tumpukan Maks", "Max Stacks"), switch_lang("Attack Speed", "Attack Speed"), switch_lang("Durasi Buff", "Buff Duration"), 
										switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 65, low: 5, mid: 2, high: 5}, {init: 2, low: 0.1, mid: 0.1, high: 0.1}, {init: 20, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}, 
									   {init: 2, low: 0, mid: 0, high: 0}, {init: 20, low: 0, mid: 0, high: 0}, {init: 5, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "2*", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, 
									{unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 44, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 44, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [30, 43], amp_num: 0}, 
									   {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 3:			
							if (!sign(skill_data.upgraded)) {	// Parry
								dtl_type = [1, 1, 1, 1];
								dtl_text = [switch_lang("Physical DMG RDC", "Physical DMG RDC"), switch_lang("Durasi Maks", "Max Duration"), switch_lang("Bonus Physical DMG RDC", "Physical DMG RDC Bonus"), switch_lang("Cooldown", "Cooldown")];
								dtl_val = [{init: 40, low: 2.8, mid: 1, high: 2.1}, {init: 7, low: 0.5, mid: 0.25, high: 0.5}, {init: 15, low: 0, mid: 0, high: 0}, {init: 5, low: 0, mid: 0, high: 0}];
								attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
								opt = [{fixed: 1, rounded: 1, subs: 1}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
								val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 43, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
							} else {							// Parry & Counter
								dtl_type = [1, 1, 1, 1, 1, 1, 1, 1];
								dtl_text = [switch_lang("Physical DMG RDC", "Physical DMG RDC"), switch_lang("Durasi Maks", "Max Duration"), switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kekuatan Knockback", "Knockback Power"), 
											switch_lang("Bonus Physical DMG RDC", "Physical DMG RDC Bonus"), switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Cooldown", "Cooldown")];
								dtl_val = [{init: 40, low: 2.8, mid: 1, high: 2.1}, {init: 7, low: 0.5, mid: 0.25, high: 0.5}, {init: 200, low: 30, mid: 8, high: 11}, {init: 150, low: 0, mid: 0, high: 0}, 
										   {init: 15, low: 0, mid: 0, high: 0}, {init: 1, low: 0, mid: 0, high: 0}, {init: 50, low: 0, mid: 0, high: 0}, {init: 5, low: 0, mid: 0, high: 0}];
								attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, 
										{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
								opt = [{fixed: 1, rounded: 1, subs: 1}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
									   {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
								val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 43, amp_num: 0}, {inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 44], amp_num: 0}, 
										   {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
							}
						break;
					}
				break;
				case 2:		// Archer
					switch (skill_num) {
						case 1:			// Flick Shot
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kekuatan Dash", "Dash Power"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 125, low: 12, mid: 4, high: 8}, {init: 150, low: 0, mid: 0, high: 0}, {init: 6, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 2:			
							if (!sign(skill_data.upgraded)) {	// Spike Shot
								dtl_type = [1, 1, 1, 1, 1, 1, 1];
								dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Jumlah Panah", "Arrow Count"), switch_lang("Durasi Slow", "Slow Duration"), switch_lang("Efektivitas Slow", "Slow Effectiveness"), 
											switch_lang("Attack Scale", "Attack Scale"), switch_lang("Durasi Buff", "Buff Duration"), switch_lang("Cooldown", "Cooldown")];
								dtl_val = [{init: 40, low: 2, mid: 1, high: 2}, {init: 3, low: 0.2, mid: 0.1, high: 0.3}, {init: 5, low: 0.2, mid: 0.1, high: 0.1}, {init: 30, low: 0, mid: 0, high: 0}, 
										   {init: 15, low: 0, mid: 0, high: 0}, {init: 5, low: 0, mid: 0, high: 0}, {init: 12, low: 0, mid: 0, high: 0}];
								attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, 
										{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
								opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
									   {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
								val_mod = [{inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 44, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 43, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 44, amp_num: 0}, 
										   {inv: 0, flat_num: 0, scale_num: 44, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [30, 43], amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
							} else {							// Electric Shot
								dtl_type = [1, 1, 1, 1, 1, 1, 1, 1];
								dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Radius Ledakan", "Explosion Radius"), switch_lang("Durasi Stun", "Stun Duration"), switch_lang("Attack Scale", "Attack Scale"), 
											switch_lang("Durasi Buff", "Buff Duration"), switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Cooldown", "Cooldown")];
								dtl_val = [{init: 200, low: 65, mid: 7, high: 10}, {init: 100, low: 3.9, mid: 1, high: 1}, {init: 2, low: 0.2, mid: 0.1, high: 0.1}, {init: 15, low: 0, mid: 0, high: 0}, 
										   {init: 5, low: 0, mid: 0, high: 0}, {init: 20, low: 0, mid: 0, high: 0}, {init: 50, low: 0, mid: 0, high: 0}, {init: 15, low: 0, mid: 0, high: 0}];
								attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, 
										{unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
								opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
									   {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
								val_mod = [{inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 44, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 43, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 44, amp_num: 0}, 
										   {inv: 0, flat_num: 0, scale_num: [30, 43], amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
							}
						break;
						case 3:		// Half-Moon Shot
							dtl_type = [1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Tinggi Lompatan", "Jump Height"), switch_lang("Attack Speed", "Attack Speed"), switch_lang("Durasi Buff", "Buff Duration"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 65, low: 5, mid: 2, high: 5}, {init: 60, low: 6, mid: 2, high: 4}, {init: 20, low: 0, mid: 0, high: 0}, {init: 7, low: 0, mid: 0, high: 0}, {init: 30, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}];
							val_mod = [{inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 44, amp_num: 0}, {inv: 0, flat_num: [30, 43], scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 4:		// Supercharged Shot
							dtl_type = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Tak Terisi", "Uncharged Damage"), switch_lang("Kerusakan Tahap I", "Phase I Damage"), switch_lang("Kerusakan Tahap II", "Phase II Damage"), switch_lang("Kerusakan Tahap III", "Phase III Damage"), switch_lang("Kerusakan Tahap IV", "Phase IV Damage"), switch_lang("Kerusakan Overcharge", "Overcharge Damage"), 
										switch_lang("Kekuatan\nKnockback", "Knockback Power"), switch_lang("Ambang Batas Pengisian", "Charge Threshold"), switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 100, low: 5, mid: 2, high: 6}, {init: 140, low: 7, mid: 2.5, high: 7}, {init: 200, low: 18, mid: 5, high: 11}, {init: 350, low: 30, mid: 6, high: 15}, {init: 600, low: 43, mid: 7, high: 26}, {init: 200, low: 30, mid: 7.5, high: 15}, 
									   {init: 50, low: 0, mid: 0, high: 0}, {init: switch_lang("Sangat Tinggi", "Very High"), low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}, {init: 0, low: 0, mid: 0, high: 0}, {init: 40, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m11, affix: "", suffix: "%", upgradeable: 0}, {unlocked: skill_data.talent.m11, affix: "", suffix: "%", upgradeable: 0}, 
									{unlocked: 1, affix: switch_lang("Tahap * ", "Phase * "), suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: switch_lang("Tahap + ", "Phase + "), suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, {inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, {inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, {inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, {inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, {inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, 
									   {inv: 0, flat_num: 0, scale_num: [28, 43], amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
					}
				break;
				case 3:		// Medic
					switch (skill_num) {
						case 1:			// Acid Vial
							dtl_type = [1, 1, 1, 1];
							dtl_text = [switch_lang("Acid DMG Bonus", "Acid DMG Bonus"), switch_lang("Durasi Maks", "Max Duration"), switch_lang("Cooldown", "Cooldown"), switch_lang("Jenis Perangkat", "Device Type")];
							dtl_val = [{init: 10, low: 1.4, mid: 0.5, high: 0.5}, {init: 4, low: 0.75, mid: 0, high: 0.5}, {init: 1, low: 0, mid: 0, high: 0}, {init: switch_lang("Utilitas", "Utility"), low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 44, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 43, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
						case 2:			// Healing Potion
							dtl_type = [1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Regenerasi HP", "HP Regeneration"), switch_lang("Durasi", "Duration"), switch_lang("Interval", "Interval"), switch_lang("Cooldown", "Cooldown"), switch_lang("Jenis Perangkat", "Device Type")];
							dtl_val = [{init: 2, low: 0, mid: 0, high: 0}, {init: 4, low: 0.5, mid: 0, high: 0.5}, {init: 1, low: 0, mid: 0, high: 0}, {init: 15, low: 0, mid: 0, high: 0}, {init: switch_lang("Penyembuh", "Healing"), low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}];
							opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 43, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
						case 3:			// Diminisher Knife
							dtl_type = [1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Pengali Debuff", "Debuff Multiplier"), switch_lang("Akurasi", "Accuracy"), switch_lang("Cooldown", "Cooldown"), switch_lang("Jenis Perangkat", "Device Type")];
							dtl_val = [{init: 100, low: 16, mid: 3, high: 5}, {init: 1, low: 0.07, mid: 0.025, high: 0.025}, {init: 75, low: 0, mid: 0, high: 0}, {init: 15, low: 0, mid: 0, high: 0}, {init: switch_lang("Ofensif", "Offensive"), low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}, {fixed: 1, rounded: 1, subs: 1}, {fixed: 0, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 44, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
					}
				break;
			}
		break;
		case 4:		// ULTI
			switch (class_num) {
				case 1:		// Warrior
					switch (skill_num) {
						case 1:			// Whirlwind Cut
							dtl_type = [1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Putaran", "Spin Damage"), switch_lang("Kerusakan Pukulan", "Blow Damage"), switch_lang("Jumlah Putaran", "Spin Count"), switch_lang("Kekuatan Knockback", "Knockback Power"), 
										switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 150, low: 16, mid: 3, high: 4}, {init: 500, low: 80, mid: 15, high: 40}, {init: 4, low: 0.2, mid: 0.1, high: 0.1}, {init: 150, low: 0, mid: 0, high: 0}, 
									   {init: 5, low: 0, mid: 0, high: 0}, {init: 50, low: 2, mid: 1, high: 1.5}, {init: 30, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, 
									{unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 45, scale_num: 46, amp_num: 0}, {inv: 0, flat_num: 45, scale_num: 46, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 48, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 47], amp_num: 0}, 
									   {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 2:			// Devastating Ambush
							dtl_type = [1, 1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Tusukan", "Thrust Damage"), switch_lang("Kerusakan Uppercut", "Uppercut Damage"), switch_lang("Kerusakan Terkaman", "Pounce Damage"), switch_lang("Kekuatan Airborne", "Airborne Power"), 
										switch_lang("Armor Penetration", "Armor Penetration"), switch_lang("Physical DMG Output", "Physical DMG Output"), switch_lang("Ambang Batas Pengisian", "Charge Threshold"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 150, low: 16, mid: 3, high: 7}, {init: 200, low: 14, mid: 5, high: 7}, {init: 1000, low: 210, mid: 50, high: 50}, {init: 150, low: 0, mid: 0, high: 0}, 
									   {init: 80, low: 0, mid: 0, high: 0}, {init: 30, low: 0, mid: 0, high: 0}, {init: switch_lang("Ekstrim", "Extreme"), low: 0, mid: 0, high: 0}, {init: 45, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, 
									{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 0, rounded: 1, subs: 1}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 45, scale_num: 46, amp_num: 0}, {inv: 0, flat_num: 45, scale_num: 46, amp_num: 0}, {inv: 0, flat_num: 45, scale_num: 46, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 47], amp_num: 0}, 
									   {inv: 0, flat_num: 0, scale_num: 48, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 48, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 3:			// Daunting Shockwaves
							dtl_type = [1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Skala Kerusakan", "Damage Scale"), switch_lang("Efektivitas Impair", "Impair Effectiveness"), switch_lang("Durasi Debuff", "Debuff Duration"), switch_lang("Critical Buildup", "Critical Buildup"), 
										switch_lang("Critical Damage", "Critical Damage"), switch_lang("Durasi Maks", "Max Duration"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 10, low: 0, mid: 0, high: 0}, {init: 10, low: 1, mid: 0.3, high: 0.7}, {init: 7, low: 0, mid: 0, high: 0}, {init: 5, low: 0.5, mid: 0.1, high: 0.2}, 
									   {init: 10, low: 1.5, mid: 0.5, high: 0.4}, {init: 10, low: 1.4, mid: 0.5, high: 0.4}, {init: 2, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, 
									{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}];
							opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 48, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 47, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 48, amp_num: 0}, 
									   {inv: 0, flat_num: 0, scale_num: 48, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [30, 47], amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
					}
				break;
				case 2:		// Archer
					switch (skill_num) {
						case 1:			// Cloudburst Volley
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Jumlah Anak Panah", "Arrow Count"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 40, low: 3, mid: 1, high: 3}, {init: 15, low: 2, mid: 0.5, high: 2}, {init: 45, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 48, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 2:			// Harpoon Breakout
							dtl_type = [1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kerusakan Ledakan", "Burst Damage"), switch_lang("Tumpukan Maks", "Max Stacks"), 
										switch_lang("Armor Damage", "Armor Damage"), switch_lang("Efektivitas Slow", "Slow Effectiveness"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 120, low: 7, mid: 2.5, high: 6}, {init: 480, low: 32, mid: 6, high: 20}, {init: 2, low: 0.2, mid: 0.1, high: 0.1}, 
									   {init: 35, low: 6, mid: 2, high: 4}, {init: 40, low: 0, mid: 0, high: 0}, {init: 6, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}, 
									{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, 
								   {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 45, scale_num: 46, amp_num: 0}, {inv: 0, flat_num: 45, scale_num: 46, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, 
									   {inv: 0, flat_num: 0, scale_num: 48, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 48, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
						case 3:			// Pinpoint Shooting
							dtl_type = [1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kekuatan Knockback", "Knockback Power"), switch_lang("Attack Scale", "Attack Scale"), switch_lang("Critical Damage", "Critical Damage"), 
										switch_lang("Kehilangan Pertahanan", "Defense Loss"), switch_lang("Durasi Maks", "Max Duration"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 150, low: 17, mid: 4, high: 17}, {init: 100, low: 0, mid: 0, high: 0}, {init: 20, low: 3, mid: 2, high: 3}, {init: 15, low: 2, mid: 1, high: 2}, 
									   {init: 90, low: -4, mid: -1.5, high: -2}, {init: 10, low: 1.4, mid: 0.5, high: 0.4}, {init: 2.5, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, 
									{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 45, scale_num: 46, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 47], amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 48, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 48, amp_num: 0}, 
									   {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [30, 47], amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
					}
				break;
				case 3:		// Medic
					switch (skill_num) {
						case 1:			// Medical Kit
							dtl_type = [1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Jumlah Penyembuhan", "Healing Amount"), switch_lang("Physical DMG Output", "Physical DMG Output"), switch_lang("Acid DMG Output", "Acid DMG Output"), switch_lang("Durasi Buff", "Buff Duration"), 
										switch_lang("Jumlah Kode", "Code Count"), switch_lang("Cooldown", "Cooldown"), switch_lang("Jenis Perangkat", "Device Type")];
							dtl_val = [{init: 15, low: 3.8, mid: 0.5, high: 1.5}, {init: 5, low: 1, mid: 0.4, high: 0.5}, {init: 5, low: 1, mid: 0.4, high: 0.5}, {init: 7, low: 1, mid: 0.25, high: 0.25}, 
									   {init: 4, low: 0, mid: 0, high: 0}, {init: 30, low: 0, mid: 0, high: 0}, {init: switch_lang("Penyembuh", "Healing"), low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, 
									{unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}];
							opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 48, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 48, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [30, 47], amp_num: 0}, 
									   {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
						case 2:			// Corrosive Concentration
							dtl_type = [1, 1, 1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan DoT", "DoT Damage"), switch_lang("Durasi", "Duration"), switch_lang("Interval", "Interval"), switch_lang("Acid DMG Reduction", "Acid DMG Reduction"), 
										switch_lang("Durasi Buff", "Buff Duration"), switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Cooldown", "Cooldown"), switch_lang("Jenis Perangkat", "Device Type")];
							dtl_val = [{init: 70, low: 4, mid: 1.5, high: 5}, {init: 7, low: 0.5, mid: 0.25, high: 0.25}, {init: 0.5, low: 0, mid: 0, high: 0}, {init: 35, low: 0, mid: 0, high: 0}, 
									   {init: 15, low: 0, mid: 0, high: 0}, {init: 50, low: 0, mid: 0, high: 0}, {init: 50, low: 0, mid: 0, high: 0}, {init: 45, low: 0, mid: 0, high: 0}, {init: switch_lang("Ofensif", "Offensive"), low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, 
									{unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}];
							opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 0, rounded: 0, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 47, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 48, amp_num: 0}, 
									   {inv: 0, flat_num: 0, scale_num: [30, 47], amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
							
							if (skill_data.talent.s14) {
								array_insert(dtl_type, 1, 1);
								array_insert(dtl_text, 1, switch_lang("Kerusakan Gas", "Gas Damage"));
								array_insert(dtl_val, 1, {init: 1200, low: 190, mid: 45, high: 50});
								array_insert(attr, 1, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0});
								array_insert(opt, 1, {fixed: 1, rounded: 1, subs: 0});
								array_insert(val_mod, 1, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0});
							}
						break;
						case 3:			// CBC Booster
							file_text_decrypt("medic.txt");
							var skill_adj2 = ini_read_real("sk_ulti", "type3_adj2", 1);
							file_text_encrypt("medic.txt");

							dtl_type = [1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Regenerasi HP", "HP Regeneration"), switch_lang("Interval", "Interval"), switch_lang("Pengali Buff", "Buff Multiplier"), switch_lang("Durasi Buff", "Buff Duration"), 
										switch_lang("Jumlah Kode", "Code Count"), switch_lang("Cooldown", "Cooldown"), switch_lang("Jenis Perangkat", "Device Type")];
							dtl_val = [{init: 1, low: 0, mid: 0, high: 0}, {init: 1, low: 0, mid: 0, high: 0}, {init: 1, low: 0.075, mid: 0.025, high: 0.05}, {init: 7, low: 0.75, mid: 0, high: 0.25}, 
									   {init: 7, low: 0, mid: 0, high: 0}, {init: 35, low: 0, mid: 0, high: 0}, {init: switch_lang("Utilitas", "Utility"), low: 0, mid: 0, high: 0}];
							attr = [{unlocked: !sign(skill_adj2-1), affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, 
									{unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}];
							opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, 
								   {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 48, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [30, 47], amp_num: 0}, 
									   {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
					}
				break;
			}
		break;
		case 5:		// INTG
			switch (skill_num) {
				case 1:			// Role
					switch (class_num) {
						case 1:			// Warrior
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Efektivitas Slow", "Slow Effectiveness"), switch_lang("Agility", "Agility"), switch_lang("Defense Scale", "Defense Scale")];
							dtl_val = [{init: 30, low: 0, mid: 0, high: 0}, {init: 30, low: 0, mid: 0, high: 0}, {init: 20, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 52, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 52, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
						case 2:			// Archer
							dtl_type = [1, 1, 1, 1];
							dtl_text = [switch_lang("Attack Scale", "Attack Scale"), switch_lang("Tumpukan Maks", "Max Stacks"), switch_lang("Cooldown", "Cooldown"), switch_lang("Jenis Cooldown", "Cooldown Type")];
							dtl_val = [{init: 8, low: 0, mid: 0, high: 0}, {init: 5, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}, {init: switch_lang("Timeout\nOtomatis", "Auto Timeout"), low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 52, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 52, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
						case 3:			// Medic
							dtl_type = [1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Jumlah Penyembuhan", "Healing Amount"), switch_lang("Pengali", "Multiplier"), switch_lang("Jenis Perangkat", "Device Type"), switch_lang("Cooldown", "Cooldown"), switch_lang("Jenis Cooldown", "Cooldown Type")];
							dtl_val = [{init: 7, low: 0, mid: 0, high: 0}, {init: switch_lang("HP Maks", "Max HP"), low: 0, mid: 0, high: 0}, {init: switch_lang("Penyembuh", "Healing"), low: 0, mid: 0, high: 0}, {init: 15, low: 0, mid: 0, high: 0}, {init: switch_lang("Timeout", "Timeout"), low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 52, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
					}
				break;
				case 2:			// Grave Hit Module
					dtl_type = [1, 1];
					dtl_text = [switch_lang("Grave Buildup", "Grave Buildup"), switch_lang("Grave Damage", "Grave Damage")];
					dtl_val = [{init: 5, low: 0, mid: 0, high: 0}, {init: 100, low: 0, mid: 0, high: 0}];
					attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}];
					opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}];
					val_mod = [{inv: 0, flat_num: 0, scale_num: 52, amp_num: 0}, {inv: 0, flat_num: 49, scale_num: 50, amp_num: 0}];
				break;
				case 3:			// Superbody Module
					dtl_type = [1, 1, 1];
					dtl_text = [switch_lang("Poin Armor", "Armor Points"), switch_lang("Pengali", "Multiplier"), switch_lang("Durasi Memudar", "Fade Duration")];
					dtl_val = [{init: 35, low: 0, mid: 0, high: 0}, {init: switch_lang("HP Hilang", "Lost HP"), low: 0, mid: 0, high: 0}, {init: 15, low: 0, mid: 0, high: 0}];
					attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
					opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
					val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 51, amp_num: 0}];
				break;
				case 4:			// Sequence Breaker Module
					dtl_type = [1, 1, 1, 1, 1, 1];
					dtl_text = [switch_lang("Kekuatan Knockback", "Knockback Power"), switch_lang("Poin Armor", "Armor Points"), switch_lang("Pengali", "Multiplier"), switch_lang("Durasi Memudar", "Fade Duration"), switch_lang("Cooldown", "Cooldown"), switch_lang("Jenis Cooldown", "Cooldown Type")];
					dtl_val = [{init: 75, low: 0, mid: 0, high: 0}, {init: 10, low: 0, mid: 0, high: 0}, {init: switch_lang("HP Maks", "Max HP"), low: 0, mid: 0, high: 0}, {init: 10, low: 0, mid: 0, high: 0}, {init: 10, low: 0, mid: 0, high: 0}, {init: switch_lang("Timeout", "Timeout"), low: 0, mid: 0, high: 0}];
					attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}];
					opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}, {fixed: 0, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}];
					val_mod = [{inv: 0, flat_num: 0, scale_num: [28, 51], amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 26, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 51, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
				break;
				case 5:			// Substitute Care Module
					dtl_type = [1, 1, 1, 1];
					dtl_text = [switch_lang("Jumlah Penyembuhan", "Healing Amount"), switch_lang("Pengali", "Multiplier"), switch_lang("Cooldown", "Cooldown"), switch_lang("Jenis Cooldown", "Cooldown Type")];
					dtl_val = [{init: 5, low: 0, mid: 0, high: 0}, {init: switch_lang("HP Maks", "Max HP"), low: 0, mid: 0, high: 0}, {init: 5, low: 0, mid: 0, high: 0}, {init: switch_lang("Timeout\nOtomatis", "Auto Timeout"), low: 0, mid: 0, high: 0}];
					attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}];
					opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}];
					val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
				break;
				case 6:			// Fatalism Prevention Module
					dtl_type = [1, 1, 1, 1, 1, 1];
					dtl_text = [switch_lang("Regenerasi HP", "HP Regeneration"), switch_lang("Pengali", "Multiplier"), switch_lang("Interval", "Interval"), switch_lang("Durasi", "Duration"), switch_lang("Cooldown", "Cooldown"), switch_lang("Jenis Cooldown", "Cooldown Type")];
					dtl_val = [{init: 1, low: 0, mid: 0, high: 0}, {init: switch_lang("HP Maks", "Max HP"), low: 0, mid: 0, high: 0}, {init: 0.2, low: 0, mid: 0, high: 0}, {init: 4, low: 0, mid: 0, high: 0}, {init: 90, low: 0, mid: 0, high: 0}, {init: switch_lang("Timeout", "Timeout"), low: 0, mid: 0, high: 0}];
					attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}];
					opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}];
					val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
				break;
			}
		break;
		case 6: 	// UNIQ
			switch (class_num) {
				case 1:		// Warrior
					dtl_type = [1, 1, 1, 1, 1, 1, 1];
					dtl_text = [switch_lang("Poin Maksimum", "Maximum Points"), switch_lang("Attack", "Attack"), switch_lang("Poin Armor", "Armor Points"), switch_lang("Jumlah Penyembuhan", "Healing Amount"), 
								switch_lang("Durasi Buff", "Buff Duration"), switch_lang("Durasi Memudar", "Fade Duration"), switch_lang("Pengali", "Multiplier")];
					dtl_val = [{init: 200, low: 0, mid: 0, high: 0}, {init: 13, low: 0, mid: 0, high: 0}, {init: 20, low: 0, mid: 0, high: 0}, {init: 15, low: 0, mid: 0, high: 0}, 
							   {init: 10, low: 0, mid: 0, high: 0}, {init: 10, low: 0, mid: 0, high: 0}, {init: switch_lang("HP Maks", "Max HP"), low: 0, mid: 0, high: 0}];
					attr = [{unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "Level * ", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, 
							{unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}];
					opt = [{fixed: 1, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, 
						   {fixed: 1, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}];
					val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, 
							   {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
				break;
				case 2:		// Archer
					dtl_type = [1];
					dtl_text = [switch_lang("Tumpukan Maks", "Max Stacks")];
					dtl_val = [{init: 4, low: 0, mid: 0, high: 0}];
					attr = [{unlocked: 1, affix: "", suffix: "x", upgradeable: 0}];
					opt = [{fixed: 1, rounded: 0, subs: 0}];
					val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
				break;
				case 3:		// Medic
					dtl_type = [1, 1, 1, 1, 1, 1, 1];
					dtl_text = [switch_lang("Poin Maksimum", "Maximum Points"), switch_lang("Regenerasi HP", "HP Regeneration"), switch_lang("Interval", "Interval"), switch_lang("Physical DMG Reduction", "Physical DMG Reduction"), 
								switch_lang("Acid DMG Reduction", "Acid DMG Reduction"), switch_lang("Durasi Maks", "Max Duration"), switch_lang("Pengali", "Multiplier")];
					dtl_val = [{init: 300, low: 0, mid: 0, high: 0}, {init: 2, low: 0, mid: 0, high: 0}, {init: 1, low: 0, mid: 0, high: 0}, {init: 15, low: 0, mid: 0, high: 0}, 
							   {init: 15, low: 0, mid: 0, high: 0}, {init: 30, low: 0, mid: 0, high: 0}, {init: switch_lang("HP Maks", "Max HP"), low: 0, mid: 0, high: 0}];
					attr = [{unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, 
							{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}];
					opt = [{fixed: 1, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, 
						   {fixed: 1, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, {fixed: 1, rounded: 0, subs: 0}];
					val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, 
							   {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
				break;
			}
		break;
		case 7:		// EXT
			switch (class_num) {
				case 1:		// Warrior
					switch (skill_num) {
						case 1:			// Wavecleaver
							dtl_type = [1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Interval", "Interval"), switch_lang("Kekuatan Knockback", "Knockback Power"), switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Waktu Pelepasan Maks", "Max Release Time")];
							dtl_val = [{init: 35, low: 0, mid: 0, high: 0}, {init: 0.2, low: 0, mid: 0, high: 0}, {init: 40, low: 0, mid: 0, high: 0}, {init: 5, low: 0, mid: 0, high: 0}, {init: 50, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "5 * ", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 35], amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
						case 2:			// Doom Drop
							dtl_type = [1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Waktu Pelepasan Maks", "Max Release Time")];
							dtl_val = [{init: 10, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
						case 3:			// Breaker Stance
							dtl_type = [1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kekuatan Knockback", "Knockback Power"), switch_lang("Target Maksimal", "Max Target"), switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Waktu Pelepasan Maks", "Max Release Time"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 325, low: 0, mid: 0, high: 0}, {init: 150, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}, {init: 50, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}, {init: 5, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 35], amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
						case 4:			// Fang Breaker
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Tusukan", "Impale Damage"), switch_lang("Kerusakan Uppercut", "Uppercut Damage"), switch_lang("Kekuatan Airborne", "Airborne Power")];
							dtl_val = [{init: 120, low: 0, mid: 0, high: 0}, {init: 100, low: 0, mid: 0, high: 0}, {init: 100, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 35]}];
						break;
						case 5:			// Starfall Strike
							dtl_type = [1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Tebasan", "Slash Damage"), switch_lang("Kerusakan Hantaman", "Slam Damage"), switch_lang("Kekuatan Knockdown", "Knockdown Power"), switch_lang("Waktu Pelepasan Maks", "Max Release Time"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 60, low: 0, mid: 0, high: 0}, {init: 400, low: 0, mid: 0, high: 0}, {init: 100, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}, {init: 12, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "2*", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, {inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: [28, 35], amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
					}
				break;
				case 2:		// Archer
					switch (skill_num) {
						case 1:			// Twin Talon Shot
							dtl_type = [1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Waktu Pelepasan Maks", "Max Release Time")];
							dtl_val = [{init: 150, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "2*", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}];
							val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
						case 2:			// Soulsnare Shot
							dtl_type = [1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Waktu Pelepasan Maks", "Max Release Time")];
							dtl_val = [{init: 10, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
						case 3:			// Cyclone Shot 
							dtl_type = [1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Kerusakan Putaran", "Spin Damage"), switch_lang("Jumlah Putaran", "Spin Count"), switch_lang("Interval", "Interval"), switch_lang("Waktu Pelepasan Maks", "Max Release Time"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 90, low: 0, mid: 0, high: 0}, {init: 20, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}, {init: 0.2, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}, {init: 5, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "2*", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "2*", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}];
							val_mod = [{inv: 0, flat_num: 37, scale_num: 38, amp_num: 0}, {inv: 0, flat_num: 37, scale_num: 38, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
						case 4:			// Skyfall Barrage
							dtl_type = [1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Jumlah Anak Panah", "Arrow Count")];
							dtl_val = [{init: 25, low: 0, mid: 0, high: 0}, {init: 15, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}];
							opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 0, rounded: 1, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 44, amp_num: 0}];
						break;
						case 5:			// Predator's Trilogy
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Jumlah Anak Panah", "Arrow Count"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 70, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}, {init: 12, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "3*", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "x", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, {fixed: 0, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 1, flat_num: 0, scale_num: 7, amp_num: 0}];
						break;
					}
				break;
				case 3:		// Medic
					switch (skill_num) {
						case 1:			// Stasis Flask/Bolt
							if (!sign(skill_data.upgraded)) {
								dtl_type = [1, 1, 1];
								dtl_text = [switch_lang("Efektivitas Slow", "Slow Effectiveness"), switch_lang("Durasi", "Duration"), switch_lang("Waktu Pelepasan Maks", "Max Release Time")];
								dtl_val = [{init: 60, low: 0, mid: 0, high: 0}, {init: 5, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}];
								attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
								opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}];
								val_mod = [{inv: 0, flat_num: 0, scale_num: 36, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
							} else {
								dtl_type = [1, 1, 1, 1, 1];
								dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Interval", "Interval"), switch_lang("Efektivitas Slow", "Slow Effectiveness"), switch_lang("Durasi", "Duration"), switch_lang("Waktu Pelepasan Maks", "Max Release Time")];
								dtl_val = [{init: 70, low: 0, mid: 0, high: 0}, {init: 1, low: 0, mid: 0, high: 0}, {init: 60, low: 0, mid: 0, high: 0}, {init: 5, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}];
								attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
								opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, {fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}];
								val_mod = [{inv: 0, flat_num: 33, scale_num: 34, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 36, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
							}
						break;
						case 2:			// Abyss Flask
							dtl_type = [1, 1];
							dtl_text = [switch_lang("Kerusakan Serangan", "Hit Damage"), switch_lang("Waktu Pelepasan Maks", "Max Release Time")];
							dtl_val = [{init: 10, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 0, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
						case 3:			// Life Bloom
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Jumlah Penyembuhan", "Healing Amount"), switch_lang("Waktu Pelepasan Maks", "Max Release Time"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 15, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}, {init: 5, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
						case 4:			// Revital Burst
							dtl_type = [1, 1, 1];
							dtl_text = [switch_lang("Jumlah Penyembuhan", "Healing Amount"), switch_lang("Interval", "Interval"), switch_lang("Durasi", "Duration")];
							dtl_val = [{init: 2, low: 0, mid: 0, high: 0}, {init: 0.5, low: 0, mid: 0, high: 0}, {init: 5, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, {fixed: 1, rounded: 1, subs: 0}];
							val_mod = [{inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
						case 5:			// Caustic Drip
							dtl_type = [1, 1, 1, 1, 1, 1, 1];
							dtl_text = [switch_lang("Kerusakan DoT", "DoT Damage"), switch_lang("Interval", "Interval"), switch_lang("Durasi", "Duration"), switch_lang("Target Maksimal", "Max Target"), 
										switch_lang("Skala Kerusakan Berlebih", "Excess Damage Scale"), switch_lang("Waktu Pelepasan Maks", "Max Release Time"), switch_lang("Cooldown", "Cooldown")];
							dtl_val = [{init: 50, low: 0, mid: 0, high: 0}, {init: 0.5, low: 0, mid: 0, high: 0}, {init: 5, low: 0, mid: 0, high: 0}, {init: 4, low: 0, mid: 0, high: 0}, 
									   {init: 50, low: 0, mid: 0, high: 0}, {init: 3, low: 0, mid: 0, high: 0}, {init: 12, low: 0, mid: 0, high: 0}];
							attr = [{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "", upgradeable: 0}, 
									{unlocked: 1, affix: "", suffix: "%", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}, {unlocked: 1, affix: "", suffix: "s", upgradeable: 0}];
							opt = [{fixed: 0, rounded: 1, subs: 0}, {fixed: 1, rounded: 0, subs: 0}, {fixed: 0, rounded: 0, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, 
								   {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}, {fixed: 1, rounded: 1, subs: 0}];
							val_mod = [{inv: 0, flat_num: 41, scale_num: 42, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 43, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, 
									   {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}, {inv: 0, flat_num: 0, scale_num: 0, amp_num: 0}];
						break;
					}
				break;
			}
		break;
	}
	var add_attr = db_create_skill_attr(db_skill, class_num, type, skill_num);
	dtl_type = array_concat(dtl_type, add_attr[0]);
	dtl_text = array_concat(dtl_text, add_attr[1]);
	dtl_val = array_concat(dtl_val, add_attr[2]);
	
	var dtl_cnt = array_length(dtl_type);
	var db = ds_grid_create(8, dtl_cnt);
	if (array_length(add_attr[0]) > 0) {
		for (var i = 0; i < dtl_cnt - 6; i++) {
			var val = [i+1, skill_num, dtl_type[i], dtl_text[i], dtl_val[i], attr[i], opt[i], val_mod[i]];
			for (var j = 0; j < array_length(val); j++) {
				ds_grid_set(db, j, i, val[j]);
			}
		}
		for (var k = dtl_cnt - 6; k < dtl_cnt; k++) {
			var opt_subs = 0;
			if (string_pos("-Subs", dtl_text[k]) > 0) {
				opt_subs = 1;
				dtl_text[k] = string_delete(dtl_text[k], string_pos("-Subs", dtl_text[k]), 5);
			}
		
			var val = [k+1, skill_num, dtl_type[k], dtl_text[k], dtl_val[k], 0, opt_subs];
			for (var j = 0; j < array_length(val); j++) {
				ds_grid_set(db, j, k, val[j]);
			}
		}
	} else {
		for (var i = 0; i < dtl_cnt; i++) {
			var val = [i+1, skill_num, dtl_type[i], dtl_text[i], dtl_val[i], attr[i], opt[i], val_mod[i]];
			for (var j = 0; j < array_length(val); j++) {
				ds_grid_set(db, j, i, val[j]);
			}
		}
	}
	delete skill_data;
	return db;
}

