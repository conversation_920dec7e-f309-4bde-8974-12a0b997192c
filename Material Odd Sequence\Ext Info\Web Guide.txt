ganti ke domain:
  custom404.html
  news.php
  notes.php

update notes
  build
  version = demo (#37641B), full (#002966), prime (#381B64), exclusive (#806600), beta (#641C1C)
  date
  status = stable, beta, critical, recommended, hotfix, deprecated

bug reports
  build
  date
  reporter
  severity = low, medium, high, critical
  title
  status = unconfirmed, confirmed, fixed internally, delayed, rejected

suggestions
  date
  name
  priority = low, medium, high
  title
  status = accepted, completed, in consideration, to be announced, rejected

feature requests
  date
  name
  priority = low, medium high
  title
  status = accepted, completed, in consideration, to be announced

  RewriteCond %{HTTP_REFERER} !^http://lefinitas.com/.*$      [NC]
  RewriteCond %{HTTP_REFERER} !^http://lefinitas.com$      [NC]
  RewriteCond %{HTTP_REFERER} !^http://odd-sequence.lefinitas.com/.*$      [NC]
  RewriteCond %{HTTP_REFERER} !^http://odd-sequence.lefinitas.com$      [NC]
  RewriteCond %{HTTP_REFERER} !^http://www.lefinitas.com/.*$      [NC]
  RewriteCond %{HTTP_REFERER} !^http://www.lefinitas.com$      [NC]
  RewriteCond %{HTTP_REFERER} !^http://www.odd-sequence.lefinitas.com/.*$      [NC]
  RewriteCond %{HTTP_REFERER} !^http://www.odd-sequence.lefinitas.com$      [NC]
  RewriteCond %{HTTP_REFERER} !^https://lefinitas.com/.*$      [NC]
  RewriteCond %{HTTP_REFERER} !^https://lefinitas.com$      [NC]
  RewriteCond %{HTTP_REFERER} !^https://odd-sequence.lefinitas.com/.*$      [NC]
  RewriteCond %{HTTP_REFERER} !^https://odd-sequence.lefinitas.com$      [NC]
  RewriteCond %{HTTP_REFERER} !^https://www.lefinitas.com/.*$      [NC]
  RewriteCond %{HTTP_REFERER} !^https://www.lefinitas.com$      [NC]
  RewriteCond %{HTTP_REFERER} !^https://www.odd-sequence.lefinitas.com/.*$      [NC]
  RewriteCond %{HTTP_REFERER} !^https://www.odd-sequence.lefinitas.com$      [NC]

  RewriteRule .*\.(jpg|jpeg|gif|png|bmp|zip)$ - [F,NC]
  
  RewriteCond %{HTTP_HOST} ^lefinitas\.com$ [NC]
  RewriteRule ^(.*)$ https://www.lefinitas.com/$1 [L,R=301]
