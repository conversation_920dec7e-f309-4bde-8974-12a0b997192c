Battle
------
    1 - Bo<PERSON> {
        data = db_get_row(db_bots, db_type_bots, class_num) {
            1 - num: int, 
            2 - name: string, 
            3 - unlocked: int, 
            4 - level: int, 
            5 - xp: int, 
            6 - weapon: int, 
            7 - core: int, 
            8 - bearing: int, 
            9 - crust: int, 
            10 - sprite: Asset.GMSprite[], 
            11 - sprite_w: int, 
            12 - derv: int, 
            13 - spmv: int, 
            14 - spmv_mod: int, 
            15 - ulti: int, 
            16 - batk_set: int, 
            17 - alt_batk_set: int, 
            18 - air_batk_set: int,
            19 - stg_obj: Asset.GMObject,
            20 - stg_spr : {
                    wp_spr : Asset.GMSprite,
                    wp_frm: int,                       // 0 = get from weapon - 1
                    wp_acc_spr : Asset.GMSprite[],
                    wp_acc_frm: int[]                  // 0 = get from weapon - 1
                }
        } + {
            frame: int,
            desc: string,
            mc_desc: string,
            ts_desc: string,
            ts_active: boolean,
            stats_info: {
                stat_title: string[],
                stat_val: int[],
                info_title: string[],
                stat_standard: int[],
                stat_present: int[],
                info_data: string[]
            }
        }

        stat = db_get_stats_val(db_bots, bots_data, class_num, db_type_bots) {
            [stats_name: string]: {
                num: int,
                val: int 
            }

            // All Stats.txt
        }

        stats = data_bots_stats_tohash(stats_struct) {
            [stats_name: int]: int          // hashed
        }
        
        batk = bots_get_batk(db_bots, db_batk, db_stats, bots_stat, class_num, bots_batk_norm) {
            num1 : { 
                type: int, 
                chain_num: int,
                adj : { 
                    [numX: string]: int     // X = 1, 2, 3, ...
                }, 
                sk_type: int,
                level: int,
                eff_level: int,
                upgraded: int,
                sprite: Asset.GMSprite,
                talent: {
                    [code: string]: boolean
                }
                
                index: {
                    target_cnt: int,
                    dmg_excess?: int,
                    cd: int,

                    dmg?: int | int[],            // Anim chain
                    dot?: int | int[],            
                    heal?: int | int[],
                    regen?: int | int[],          // min = hp drop
                    armor?: int | int[],

                    buff_eff?: int | int[],
                    buff_amp?: int | int[],
                    buff_dur?: int | int[],       // -1-specialized, 0-in_use, >0-normal
                    debuff_eff?: int | int[],
                    debuff_amp?: int | int[],
                    debuff_dur?: int | int[],     // -1-specialized, 0-in_use, >0-normal
                    cc_eff?: int | int[],

                    cnt?: int,                      // Multi-attack in 1 anim
                    spd?: real,
                    eff?: int,
                    amp?: int,
                    dur?: int,
                    max_dur?: int,
                    fade_dur?: int,
                    interval?: int,
                    spd?: int,
                    point?: int,
                    stack?: int,
                    total?: int,                    // Attack repeat count
                    threshold?: int,
                    max_chain?: int,                // Anim chain (no parent struct set)
                    max_phase?: int,
                    max_stack?: int,
                    max_point?: int,
                    radius?: int,                   // multiplier
                    angle?: int,
                    recoil?: int,
                    delay?: int,
                    device_type?: int,

                    dir?: int,
                    jump?: int,                     // multiplier
                    move_hor?: int,                 // multiplier
                    move_ver?: int,                 // multiplier
                    stats_ovr?: int | int[],
                },
                
                multiplier: string,
                int_power: int,
                ignint_bonus: int,
                ignint_subs: int,
                target: skill_target_type,
                target_cnt: int,                    // 1 = single, >1 = multi, 0 = aoe, -1 = none
                dmg_type: skill_dmg_type,
                dmg_excess?: int,
                cd_type: skill_cd_type,

                buff?: stats_list | stats_list[],
                buff_target?: skill_target_type | skill_target_type[],        // default = skill_target_type.own
                debuff?: stats_ailment | stats_ailment[],
                debuff_target?: skill_target_type | skill_target_type[],      // default = skill_target_type.enemy
                cc?: stats_ailment | stats_ailment[],
                cc_target?: skill_target_type | skill_target_type[],          // default = skill_target_type.enemy
                stats_ovr_type?: stats_list | stats_list[],
                stats_ovr?: {
                    [name: string]: int
                }
                crit_hit?: int,                   // -1 = never, 0 = normal, >0 = always, -2 = conditional
                grave_hit?: int,                  // -1 = never, 0 = normal, >0 = always, -2 = conditional
                move_hor_delay?: real,                   
                move_ver_delay?: real,                   
                dash?: boolean,                   
                dodge?: boolean,                   
                auto_dodge_m?: boolean,                   
                auto_dodge_r?: boolean,                   
                no_dmg?: boolean,                   
                no_reset?: boolean,                   
                auto_active?: boolean,                   
                ext?: <any>,                   
                max_reduce?: int,                   
                immune?: stats_mod | stats_mod[],                   
                immune_debuff?: boolean,                   
                immune_cc?: boolean

                // (dari struct "index")
            }, 
            ...
        }

        alt_batk = bots_get_batk(db_bots, db_batk, db_stats, bots_stat, class_num, bots_batk_alt);
        air_batk = bots_get_batk(db_bots, db_batk, db_stats, bots_stat, class_num, bots_batk_air);

        derv = bots_get_skill(db_bots, db_skill, db_stats, bots_stat, class_num, bots_skill_derv, 0, db_batk) {
            type: int, 
            chain_num: int,
            adj : { 
                [numX: string]: int
            }, 
            sk_type: int,
            level: int,
            // ...
        }
        spmv = bots_get_skill(db_bots, db_skill, db_stats, bots_stat, class_num, bots_skill_spmv, skill_num);
        spmv_mod = bots_get_skill(db_bots, db_skill, db_stats, bots_stat, class_num, bots_skill_spmv, skill_num) | -1;
        ulti = bots_get_skill(db_bots, db_skill, db_stats, bots_stat, class_num, bots_skill_ulti, skill_num);
        intg = bots_get_skill(db_bots, db_skill, db_stats, bots_stat, class_num, bots_skill_intg, 1);
        uniq = bots_get_skill(db_bots, db_skill, db_stats, bots_stat, class_num, bots_skill_uniq, 1);

        module = bots_get_skill(db_bots, db_skill, db_stats, bots_stat, class_num, bots_skill_intg, 2) {
            grave: {
                buildup: int,
                dmg: int
            },
            // ...
        };
        ext = bots_get_skill(db_bots, db_skill, db_stats, bots_stat, class_num, bots_skill_ext, 0);


		// In-stage
        state = new bots_state() {
            ready: boolean,
            dodge: boolean,
            battle: boolean,

            idle: boolean,
            moving_right: boolean,
            moving_left: boolean,
            moving_up: boolean,
            moving_down: boolean,
            sprinting: boolean,
            dashing: boolean,
            sliding: boolean,
            jumping: boolean,
            falling: boolean,
            drawing: boolean,
            attacking: boolean,
            hitting: boolean,
			interrupting: boolean,
			defeating: boolean,
			healing: boolean,
            charging: boolean,
            slamming: boolean,
            blocking: boolean,
			parrying: boolean,

            mid_air: boolean,
            on_field: boolean,
            off_field: boolean,
            switching_in: boolean,
            switching_out: boolean,
            joint_attacking: boolean,

            defeated: boolean,
			interrupted: boolean,
            attacked: int,
			healed: int,
            dodged: int,
            switched_in: boolean,
            switched_out: boolean,

            slowed: boolean,
            weakened: boolean,
            impaired: boolean,
            broken: boolean,
            softened: boolean,
            enervated: boolean,
            cracked: boolean,
            exhausted: boolean,
            devitalized: boolean,
            fractured: boolean,
            languished: boolean,
            eroded: boolean,

            flinched: boolean,
            bound: boolean,
            knocked_back: boolean,
            knocked_air: boolean,
            knocked_down: boolean,
            silenced: boolean,
            stunned: boolean,
            paralyzed: boolean,
            neutralized: boolean,

			get(key: string): boolean | int,
			set(key: string, value: boolean | int): void,
			toggle(key: string): void
        }

        attr = {
            max_xp: int,
			hp: int,			    // real hp
			hp_amp: int,
			atk_amp: int,
			def_amp: int,
			spd: real,              // real spd
            spd_amp: real,          // base spd
			last_spd: real,
            charge_pt: int,
			charge_lv: int,
			crit_pt: int,
			grave_pt: int,
            super_armor: real,
            max_super_armor: real,
            barrier: real,
            max_barrier: real,

			type: int,			    // 1-normal, 2-enhanced, 3-elite, 4-boss, 5-superboss
            range: int,			    // 1-melee, 2-ranged, 3-hybrid
			role: int,			    // 1-all_rounder, 2-attacker, 3-defender, 4-healer, 5-supporter, 6-specialist
			dir_x: int,			    // 1-right, 0-idle, -1-left
			dir_y: int,			    // 1-down, 0-idle, -1-up
            scale_x: real,
            scale_y: real,
            rot: real,
            col: Constant.Colour,
            alpha: real,

            move_step: int,
            idle_step: int,
            idle_alpha: real,
            land_block: int,
            jump_y: real,				// y sebelum lompat (top-down)
            last_dir_x: int,
            last_dir_y: int,
            nonzero_dir_x: int,
            nonzero_dir_y: int,
            last_x: real,
            last_y: real,
            add_x: real,				// reset per step
            add_y: real,				// reset per step
            diff_h: real,				// Jarak y ke bayangan

			deal_hit: int,
			deal_hit_normal: int,
			deal_hit_crit: int,
			deal_hit_grave: int,
			take_hit: int,
			take_hit_normal: int,
			take_hit_crit: int,
			take_hit_grave: int,
			hit_missed: int
        }

		data = ref <global.bots_all_data.data> + {
			max_xp: int

		}

		stats = ref <global.bots_all_data.stats>
        
        grav = {
            base_gravity: real,
            max_vel_s: real,
            vel_y: real,
            val: real,
            phy_step_size: real,
            accumulator: real,
            initial_jump_vel: real,
            str_mul: real,
            bots_parent: anim_speed_team | anim_speed_enemy,

            jump(jump_str: real): void,
            update(): void
        }

        dash = {
			max_gauge: int,
			gauge: int,
			cost: int,
			str: real,
			dur: real,
			delay: int,
			step: int,
			bots_parent: anim_speed_team | anim_speed_enemy,

			use(state: bots_state): boolean,
			update(state: bots_state): void
		}

        weapon = {
            drawn: boolean,
            sprite: Asset.GMSprite,
            frame: int,

            bots_parent: anim_speed_team | anim_speed_enemy,
            data: bots_data,
            state: bots_state,
            attr: bots_attr,
            type: BOTS_PROPS,

            x: real,
            y: real,
            image_angle: real,
            image_xscale: real,
            image_yscale: real,
            image_alpha: real,
            depth: int,
            speed: real,
            init_depth: int,

            anim_type: {
                [move: string]: NEXAFLUX_PRESET
            },
            anim: NexaFluxAnim,
            current_anim: NEXAFLUX_PRESET
        }

		batk = {
			chain: int,
			last_chain: int,
			total_cd: real,
			on_cd: real,
			seq: ref <global.bots_all_data.batk>
		}
		derv = ref <global.bots_all_data.derv> + {
            on_cd: real
        }
		spmv = ref <global.bots_all_data.spmv> + {
            on_cd: real
        }
		ulti = ref <global.bots_all_data.ulti> + {
            on_cd: real
        }
		intg = ref <global.bots_all_data.intg> + {
            on_cd: real
        }
		uniq = ref <global.bots_all_data.uniq> + {
            on_cd: real
        }
		module = ref <global.bots_all_data.module>
		ext = ref <global.bots_all_data.ext> {
            dodge +: {
                on_cd: real
            }
            // ...
        }
		

        

		component = BotsComponent[] ?? []
		buff = BotsBuff[] ?? []
        debuff = BotsDebuff[] ?? []
		cc = BotsCC[] ?? []

        
    }



skill_target_type = {
	none,
	own, 
	ally,
	enemy,
	team,
	all_bots
}

skill_dmg_type = {
	none,
	physical,
	acid,
	absoulte
}

skill_cd_type = {
	none,
	timeout,
	dyn_timeout
}

stats_list = {
	hp_scale = -1, 
	atk_scale = -2, 
	def_scale = -3,
	hp = 1, 
	atk = 2, 
	def = 3, 
	agility = 4,
	dmg_output = 5,
	ignore_int = 6,
	cd_rdc = 7,
	crit_bld = 8,
	crit_dmg = 9,
	crit_prt = 10,
	heal_otp = 11,
	atk_spd = 12,
	melee_do = 13,
	ranged_do = 14,
	aoe_dmg = 15,
	physical_db = 16,
	physical_do = 17,
	physical_rdc = 18,
	acid_db = 19,
	acid_do = 20,
	acid_rdc = 21,
	def_pen = 22,
	armor_pen = 23,
	armor_dmg = 24,
	armor_burst = 25,
	armor_str = 26,
	dmg_res = 27,
	cc_power = 28,
	cc_res = 29,
	buff_power = 30,
	debuff_res = 31,
	charge_spd = 32,
	batk_db = 33,
	batk_do = 34,
	batk_power = 35,
	batk_eff = 36,
	derv_db = 37,
	derv_do = 38,
	derv_power = 39,
	derv_eff = 40,
	spmv_db = 41,
	spmv_do = 42,
	spmv_power = 43,
	spmv_eff = 44,
	ulti_db = 45,
	ulti_do = 46,
	ulti_power = 47,
	ulti_eff = 48,
	intg_db = 49,
	intg_do = 50,
	intg_power = 51,
	intg_eff = 52,
	accuracy = 53,
	recoil_rdc = 54,
	ammo_cap = 55,
	mags_cap = 56,
	reload_spd = 57
}

stats_ailment = {
	debuff_slow = 1,
	debuff_weaken = 2,
	debuff_impair = 3,
	debuff_break = 4,
	debuff_soften = 5,
	debuff_enervate = 6,
	debuff_crack = 7,
	debuff_exhaust = 8,
	debuff_devitalize = 9,
	debuff_fracture = 10,
	debuff_languish = 11,
	debuff_erode = 12,

	cc_flinch = 101, 
	cc_bind = 102, 
	cc_knockback = 103, 
	cc_airborne = 104, 
	cc_knockdown = 105, 
	cc_silence = 106, 
	cc_stun = 107, 
	cc_paralyze = 108, 
	cc_neutralize = 109
}

stats_mod {
	immune_slow = 201,
	immune_weaken = 202,
	immune_impair = 203,
	immune_break = 204,
	immune_soften = 205,
	immune_enervate = 206,
	immune_crack = 207,
	immune_exhaust = 208,
	immune_devitalize = 209,
	immune_fracture = 210,
	immune_languish = 211,
	immune_erode = 212,
	immune_flinch = 213, 

	immune_bind = 214, 
	immune_knockback = 215, 
	immune_airborne = 216, 
	immune_knockdown = 217, 
	immune_silence = 218, 
	immune_stun = 219, 
	immune_paralyze = 220, 
	immune_neutralize = 221,

	cleanse_debuff = 251,
	cleanse_debuff_all = 252,

	infuse_physical = 301,
	infuse_acid = 302
}
