function total_damage(class, hit_list, target, atk_type, dmg_type, dmg_range, hit_type, base_dmg, int_power = interruption_power, show_text = global.show_dmg, buildup = 1, interruptable = 1, hit_vfx = 1, no_damage = 0, return_type = 0) {
	if (class == 0) {
		class = owner;
		var img_bot = 0;
	};
	else {
		var class_list = [0, o<PERSON><PERSON><PERSON><PERSON>, oArcher, oMedic];
		class = class_list[class];
		var img_bot = 1;
	};
	
	if (atk_type == 1) {
		var atk_type_do = class.basic_atk_do;
		var atk_type_db = class.basic_atk_db;
	};
	else if (atk_type == 2) {
		var atk_type_do = class.sp_move_do;
		var atk_type_db = class.sp_move_db;
	};
	else if (atk_type == 3) {
		var atk_type_do = class.ultimate_do;
		var atk_type_db = class.ultimate_db;
	};
	
	for (var i = 0; i < target; i++) {
		/*
		class = 0-enemy, 1-warrior, 2-archer, 3-medic
		atk_type = 1-basic_atk, 2-special_mv, 3-ultimate
		atk_dir = 1-right, 2-left, 3-all(, 4-down, 5-up)
		dmg_type = 1-physical, 2-acid, 3-absolute (, 4-super_armor, 5-barrier, 6-heal)
		dmg_range = 1-melee, 2-ranged
		hit_type = 1-single, 2-multi, 3-aoe
		return_type = 1-final damage
		*/
		if (instance_exists(hit_list[| i])) {
			if (hit_list[| i].defeated == 0) {
				if (buildup == 1) {
					class.crit_point += class.crit_buildup - hit_list[| i].crit_protection;
					if (class.crit_point >= 100) {
						class.crit_point -= 100;
						class.crit_hit += 1;
					};
					else if (class.fatal_point >= 100) {
						class.fatal_point -= 100;
						class.fatal_hit += 1;
					};
				};
		
				if (dmg_type == 1) {
					if (dmg_range == 1) {
						if (hit_type == 1 || hit_type == 2) {
							if (class.fatal_hit > 0) {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.physical_do+class.crit_damage)+class.physical_dmg_bonus+atk_type_db)/100)*(class.atk - hit_list[| i].def);
								hit_list[| i].damaged_strike_type = 3;
								var strike_type = 3;
								var abs_dmg = round((((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.physical_do+class.crit_damage)+class.physical_dmg_bonus+atk_type_db)/100)*class.atk);
							};
							else if (class.fatal_hit == 0 && class.crit_hit > 0) {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.physical_do+class.crit_damage)+class.physical_dmg_bonus+atk_type_db)/100)*(class.atk - hit_list[| i].def);
								hit_list[| i].damaged_strike_type = 2;
								class.crit_hit -= 1;
								class.fatal_point += class.fatal_buildup;
								var strike_type = 2;
								if (class.fatal_point >= 100) {
									class.fatal_point -= 100;
									class.fatal_hit += 1;
									hit_list[| i].damaged_strike_type = 3;
									var strike_type = 3;
								};
								var abs_dmg = round((((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.physical_do+class.crit_damage)+class.physical_dmg_bonus+atk_type_db)/100)*class.atk);
							};
							else {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.physical_do)+class.physical_dmg_bonus+atk_type_db)/100)*(class.atk - hit_list[| i].def);
								hit_list[| i].damaged_strike_type = 1;
								var strike_type = 1;
								var abs_dmg = round((((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.physical_do)+class.physical_dmg_bonus+atk_type_db)/100)*class.atk);
							};
						};
						else if (hit_type == 3) {
							var origin = bbox_left + sprite_width/2;
							var half_origin = sprite_width/4;
							var origin_right = origin + half_origin;
							var origin_left = origin - half_origin;
							if (hit_list[| i].x >= x) {
								var enemy_pos = hit_list[| i].x - hit_list[| i].sprite_width/2;
								if (enemy_pos >= origin_left && enemy_pos <= origin_right) {
									var main_area = 1;
								};
								else {
									var main_area = 0;
									var gap = abs(enemy_pos - origin_right);
								};
							};
							else {
								var enemy_pos = hit_list[| i].x + hit_list[| i].sprite_width/2;
								if (enemy_pos >= origin_left && enemy_pos <= origin_right) {
									var main_area = 1;
								};
								else {
									var main_area = 0;
									var gap = abs(enemy_pos - origin_left);
								};
							};
							if (class.fatal_hit > 0) {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.physical_do+class.crit_damage)+class.physical_dmg_bonus+atk_type_db)/100)*(class.atk - hit_list[| i].def);
								hit_list[| i].damaged_strike_type = 3;
								var strike_type = 3;
								var abs_dmg = round((((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.physical_do+class.crit_damage)+class.physical_dmg_bonus+atk_type_db)/100)*class.atk);
							};
							else if (class.fatal_hit == 0 && class.crit_hit > 0) {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.physical_do+class.crit_damage)+class.physical_dmg_bonus+atk_type_db)/100)*(class.atk - hit_list[| i].def);
								hit_list[| i].damaged_strike_type = 2;
								class.crit_hit -= 1;
								class.fatal_point += class.fatal_buildup;
								var strike_type = 2;
								if (class.fatal_point >= 100) {
									class.fatal_point -= 100;
									class.fatal_hit += 1;
									hit_list[| i].damaged_strike_type = 3;
									var strike_type = 3;
								};
								var abs_dmg = round((((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.physical_do+class.crit_damage)+class.physical_dmg_bonus+atk_type_db)/100)*class.atk)
							};
							else {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.physical_do)+class.physical_dmg_bonus+atk_type_db)/100)*(class.atk - hit_list[| i].def);
								hit_list[| i].damaged_strike_type = 1;
								var strike_type = 1;
								var abs_dmg = round((((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.physical_do)+class.physical_dmg_bonus+atk_type_db)/100)*class.atk)
							};
						};
					};
					else if (dmg_range == 2) {
						if (hit_type == 1 || hit_type == 2) {
							if (class.fatal_hit > 0) {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.physical_do+class.crit_damage)+class.physical_dmg_bonus+atk_type_db)/100)*(class.atk - hit_list[| i].def);
								hit_list[| i].damaged_strike_type = 3;
								var strike_type = 3;
								var abs_dmg = round((((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.physical_do+class.crit_damage)+class.physical_dmg_bonus+atk_type_db)/100)*class.atk);
							};
							else if (class.fatal_hit == 0 && class.crit_hit > 0) {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.physical_do+class.crit_damage)+class.physical_dmg_bonus+atk_type_db)/100)*(class.atk - hit_list[| i].def);
								hit_list[| i].damaged_strike_type = 2;
								class.crit_hit -= 1;
								class.fatal_point += class.fatal_buildup;
								var strike_type = 2;
								if (class.fatal_point >= 100) {
									class.fatal_point -= 100;
									class.fatal_hit += 1;
									hit_list[| i].damaged_strike_type = 3;
									var strike_type = 3;
								};
								var abs_dmg = round((((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.physical_do+class.crit_damage)+class.physical_dmg_bonus+atk_type_db)/100)*class.atk);
							};
							else {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.physical_do)+class.physical_dmg_bonus+atk_type_db)/100)*(class.atk - hit_list[| i].def);
								hit_list[| i].damaged_strike_type = 1;
								var strike_type = 1;
								var abs_dmg = round((((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.physical_do)+class.physical_dmg_bonus+atk_type_db)/100)*class.atk);
							};
						};
						else if (hit_type == 3) {
							var origin = bbox_left + sprite_width/2;
							var half_origin = sprite_width/4;
							var origin_right = origin + half_origin;
							var origin_left = origin - half_origin;
							if (hit_list[| i].x >= x) {
								var enemy_pos = hit_list[| i].x - hit_list[| i].sprite_width/2;
								if (enemy_pos >= origin_left && enemy_pos <= origin_right) {
									var main_area = 1;
								};
								else {
									var main_area = 0;
									var gap = abs(enemy_pos - origin_right);
								};
							};
							else {
								var enemy_pos = hit_list[| i].x + hit_list[| i].sprite_width/2;
								if (enemy_pos >= origin_left && enemy_pos <= origin_right) {
									var main_area = 1;
								};
								else {
									var main_area = 0;
									var gap = abs(enemy_pos - origin_left);
								};
							};
							if (class.fatal_hit > 0) {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.physical_do+class.crit_damage)+class.physical_dmg_bonus+atk_type_db)/100)*(class.atk - hit_list[| i].def);
								hit_list[| i].damaged_strike_type = 3;
								var strike_type = 3;
								var abs_dmg = round((((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.physical_do+class.crit_damage)+class.physical_dmg_bonus+atk_type_db)/100)*class.atk);
							};
							else if (class.fatal_hit == 0 && class.crit_hit > 0) {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.physical_do+class.crit_damage)+class.physical_dmg_bonus+atk_type_db)/100)*(class.atk - hit_list[| i].def);
								hit_list[| i].damaged_strike_type = 2;
								class.crit_hit -= 1;
								class.fatal_point += class.fatal_buildup;
								var strike_type = 2;
								if (class.fatal_point >= 100) {
									class.fatal_point -= 100;
									class.fatal_hit += 1;
									hit_list[| i].damaged_strike_type = 3;
									var strike_type = 3;
								};
								var abs_dmg = round((((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.physical_do+class.crit_damage)+class.physical_dmg_bonus+atk_type_db)/100)*class.atk);
							};
							else {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.physical_do)+class.physical_dmg_bonus+atk_type_db)/100)*(class.atk - hit_list[| i].def);
								hit_list[| i].damaged_strike_type = 1;
								var strike_type = 1;
								var abs_dmg = round((((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.physical_do)+class.physical_dmg_bonus+atk_type_db)/100)*class.atk);
							};
						};
					};
					//Final Physical Damage
					if (hit_list[| i].fixed_dmg_reduction == 0) {
						if (hit_type != 3) {
							if (target > 1 && class.fatal_hit == 0) {
								final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction)/100))*(multi_dmg_scale/100);
								if (hit_list[| i].super_armor > 0) {
									super_armor_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100))*(multi_dmg_scale/100);
									final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(multi_dmg_scale/100);
								};
								else if (hit_list[| i].barrier > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(multi_dmg_scale/100);
								};
								var rdc_dmg = final_dmg / (multi_dmg_scale/100);
							};
							else if (target > 1 && class.fatal_hit == 1) {
								final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction)/100))*((multi_dmg_scale+class.fatal_damage)/100);
								if (hit_list[| i].super_armor > 0) {
									super_armor_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100))*((multi_dmg_scale+class.fatal_damage)/100);
									final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((multi_dmg_scale+class.fatal_damage)/100);
								};
								else if (hit_list[| i].barrier > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((multi_dmg_scale+class.fatal_damage)/100);
								};
								class.fatal_hit -= 1;
								var rdc_dmg = final_dmg / ((multi_dmg_scale+class.fatal_damage)/100);
							};
							else if (class.fatal_hit == 0) {
								final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction)/100))*(dmg_scale/100);
								if (hit_list[| i].super_armor > 0) {
									super_armor_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100))*(dmg_scale/100);
									final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(dmg_scale/100);
								};
								else if (hit_list[| i].barrier > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(dmg_scale/100);
								};
								var rdc_dmg = final_dmg / (dmg_scale/100);
							};
							else {
								final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction)/100))*((dmg_scale+class.fatal_damage)/100);
								if (hit_list[| i].super_armor > 0) {
									super_armor_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100))*((dmg_scale+class.fatal_damage)/100);
									final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								else if (hit_list[| i].barrier > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								class.fatal_hit -= 1;
								var rdc_dmg = final_dmg / ((dmg_scale+class.fatal_damage)/100);
							};
						};
						else if (hit_type == 3 && main_area == 1) {
							if (class.fatal_hit == 0) {
								final_dmg = dmg_out*((100-hit_list[| i].dmg_reduction)/100);
								if (hit_list[| i].super_armor > 0) {
									super_armor_dmg = dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100);
									final_dmg = dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100);
								};
								else if (hit_list[| i].barrier > 0) {
									final_dmg = dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100);
								};
								var rdc_dmg = final_dmg;
							};
							else {
								final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction)/100))*((dmg_scale+class.fatal_damage)/100);
								if (hit_list[| i].super_armor > 0) {
									super_armor_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100))*((dmg_scale+class.fatal_damage)/100);
									final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								else if (hit_list[| i].barrier > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								class.fatal_hit -= 1;
								var rdc_dmg = final_dmg / ((dmg_scale+class.fatal_damage)/100);
							};
						};
						else if (hit_type == 3 && main_area == 0) {
							if (class.fatal_hit == 0) {
								final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction)/100))*((half_origin-gap)/half_origin) + (dmg_out*((100-hit_list[| i].dmg_reduction)/100))*(gap/half_origin)*(class.aoe_dmg_scale/100);
								if (hit_list[| i].super_armor > 0) {
									super_armor_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100))*((half_origin-gap)/half_origin) + (dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100))*(gap/half_origin)*(class.aoe_dmg_scale/100);
									final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((half_origin-gap)/half_origin) + (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(gap/half_origin)*(class.aoe_dmg_scale/100);
								};
								else if (hit_list[| i].barrier > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((half_origin-gap)/half_origin) + (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(gap/half_origin)*(class.aoe_dmg_scale/100);
								};
								var rdc_dmg = final_dmg;
							};
							else {
								final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction)/100))*((dmg_scale+class.fatal_damage)/100);
								if (hit_list[| i].super_armor > 0) {
									super_armor_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100))*((dmg_scale+class.fatal_damage)/100);
									final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								else if (hit_list[| i].barrier > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								class.fatal_hit -= 1;
								var rdc_dmg = final_dmg / ((dmg_scale+class.fatal_damage)/100);
							};
						};
					};
					else {
						if (hit_type != 3) {
							if (target > 1 && class.fatal_hit == 0) {
								final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction)/100))*(multi_dmg_scale/100);
								if (hit_list[| i].super_armor > 0) {
									super_armor_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100))*(multi_dmg_scale/100);
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(multi_dmg_scale/100);
								};
								else if (hit_list[| i].barrier > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(multi_dmg_scale/100);
								};
								var rdc_dmg = final_dmg / (multi_dmg_scale/100);
							};
							else if (target > 1 && class.fatal_hit == 1) {
								final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction)/100))*((multi_dmg_scale+class.fatal_damage)/100);
								if (hit_list[| i].super_armor > 0) {
									super_armor_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100))*((multi_dmg_scale+class.fatal_damage)/100);
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((multi_dmg_scale+class.fatal_damage)/100);
								};
								else if (hit_list[| i].barrier > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((multi_dmg_scale+class.fatal_damage)/100);
								};
								class.fatal_hit -= 1;
								var rdc_dmg = final_dmg / ((multi_dmg_scale+class.fatal_damage)/100);
							};
							else if (class.fatal_hit == 0) {
								final_dmg = dmg_out*((100-hit_list[| i].fixed_dmg_reduction)/100);
								if (hit_list[| i].super_armor > 0) {
									super_armor_dmg = dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100);
									final_dmg = dmg_out*((100-hit_list[| i].fixed_dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100);
								};
								else if (hit_list[| i].barrier > 0) {
									final_dmg = dmg_out*((100-hit_list[| i].fixed_dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100);
								};
								var rdc_dmg = final_dmg;
							};
							else {
								final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction)/100))*((dmg_scale+class.fatal_damage)/100);
								if (hit_list[| i].super_armor > 0) {
									super_armor_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100))*((dmg_scale+class.fatal_damage)/100);
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								else if (hit_list[| i].barrier > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								class.fatal_hit -= 1;
								var rdc_dmg = final_dmg / ((dmg_scale+class.fatal_damage)/100);
							};
						};
						else if (hit_type == 3 && main_area == 1) {
							if (class.fatal_hit == 0) {
								final_dmg = dmg_out*((100-hit_list[| i].fixed_dmg_reduction)/100);
								if (hit_list[| i].super_armor > 0) {
									super_armor_dmg = dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100);
									final_dmg = dmg_out*((100-hit_list[| i].fixed_dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100);
								};
								else if (hit_list[| i].barrier > 0) {
									final_dmg = dmg_out*((100-hit_list[| i].fixed_dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100);
								};
								var rdc_dmg = final_dmg;
							};
							else {
								final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction)/100))*((dmg_scale+class.fatal_damage)/100);
								if (hit_list[| i].super_armor > 0) {
									super_armor_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100))*((dmg_scale+class.fatal_damage)/100);
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								else if (hit_list[| i].barrier > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								class.fatal_hit -= 1;
								var rdc_dmg = final_dmg / ((dmg_scale+class.fatal_damage)/100);
							};
						};
						else if (hit_type == 3 && main_area == 0) {
							if (class.fatal_hit == 0) {
								final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction)/100))*((half_origin-gap)/half_origin) + (dmg_out*((100-hit_list[| i].dmg_reduction)/100))*(gap/half_origin)*(class.aoe_dmg_scale/100);
								if (hit_list[| i].super_armor > 0) {
									super_armor_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100))*((half_origin-gap)/half_origin) + (dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100))*(gap/half_origin)*(class.aoe_dmg_scale/100);
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((half_origin-gap)/half_origin) + (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(gap/half_origin)*(class.aoe_dmg_scale/100);
								};
								else if (hit_list[| i].barrier > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((half_origin-gap)/half_origin) + (dmg_out*((100-hit_list[| i].dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(gap/half_origin)*(class.aoe_dmg_scale/100);
								};
								var rdc_dmg = final_dmg;
							};
							else {
								final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction)/100))*((dmg_scale+class.fatal_damage)/100);
								if (hit_list[| i].super_armor > 0) {
									super_armor_dmg = (dmg_out*((100-hit_list[| i].dmg_reduction-hit_list[| i].resistance+class.ab)/100))*((dmg_scale+class.fatal_damage)/100);
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								else if (hit_list[| i].barrier > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_dmg_reduction-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								class.fatal_hit -= 1;
								var rdc_dmg = final_dmg / ((dmg_scale+class.fatal_damage)/100);
							};
						};
					};
					hit_list[| i].damaged_dmg_type = 1;
				};
				else if (dmg_type == 2) {
					if (dmg_range == 1) {
						if (hit_type == 1 || hit_type == 2) {
							if (class.fatal_hit > 0) {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.acid_do+class.crit_damage)+class.acidity_bonus+atk_type_db)/100)*(class.atk);
								hit_list[| i].damaged_strike_type = 3;
								var strike_type = 3;
								var abs_dmg = dmg_out;
							};
							else if (class.fatal_hit == 0 && class.crit_hit > 0) {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.acid_do+class.crit_damage)+class.acidity_bonus+atk_type_db)/100)*(class.atk);
								hit_list[| i].damaged_strike_type = 2;
								class.crit_hit -= 1;
								class.fatal_point += class.fatal_buildup;
								var strike_type = 2;
								if (class.fatal_point >= 100) {
									class.fatal_point -= 100;
									class.fatal_hit += 1;
									hit_list[| i].damaged_strike_type = 3;
									var strike_type = 3;
								};
								var abs_dmg = dmg_out;
							};
							else {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.acid_do)+class.acidity_bonus+atk_type_db)/100)*(class.atk);
								hit_list[| i].damaged_strike_type = 1;
								var strike_type = 1;
								var abs_dmg = dmg_out;
							};
						};
						else if (hit_type == 3) {
							var origin = bbox_left + sprite_width/2;
							var half_origin = sprite_width/4;
							var origin_right = origin + half_origin;
							var origin_left = origin - half_origin;
							if (hit_list[| i].x >= x) {
								var enemy_pos = hit_list[| i].x - hit_list[| i].sprite_width/2;
								if (enemy_pos >= origin_left && enemy_pos <= origin_right) {
									var main_area = 1;
								};
								else {
									var main_area = 0;
									var gap = abs(enemy_pos - origin_right);
								};
							};
							else {
								var enemy_pos = hit_list[| i].x + hit_list[| i].sprite_width/2;
								if (enemy_pos >= origin_left && enemy_pos <= origin_right) {
									var main_area = 1;
								};
								else {
									var main_area = 0;
									var gap = abs(enemy_pos - origin_left);
								};
							};
							if (class.fatal_hit > 0) {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.acid_do+class.crit_damage)+class.acidity_bonus+atk_type_db)/100)*(class.atk);
								hit_list[| i].damaged_strike_type = 3;
								var strike_type = 3;
								var abs_dmg = dmg_out;
							};
							else if (class.fatal_hit == 0 && class.crit_hit > 0) {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.acid_do+class.crit_damage)+class.acidity_bonus+atk_type_db)/100)*(class.atk);
								hit_list[| i].damaged_strike_type = 2;
								class.crit_hit -= 1;
								class.fatal_point += class.fatal_buildup;
								var strike_type = 2;
								if (class.fatal_point >= 100) {
									class.fatal_point -= 100;
									class.fatal_hit += 1;
									hit_list[| i].damaged_strike_type = 3;
									var strike_type = 3;
								};
								var abs_dmg = dmg_out;
							};
							else {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do+class.acid_do)+class.acidity_bonus+atk_type_db)/100)*(class.atk);
								hit_list[| i].damaged_strike_type = 1;
								var strike_type = 1;
								var abs_dmg = dmg_out;
							};
						};
					};
					else if (dmg_range == 2) {
						if (hit_type == 1 || hit_type == 2) {
							if (class.fatal_hit > 0) {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.acid_do+class.crit_damage)+class.acidity_bonus+atk_type_db)/100)*(class.atk);
								hit_list[| i].damaged_strike_type = 3;
								var strike_type = 3;
								var abs_dmg = dmg_out;
							};
							else if (class.fatal_hit == 0 && class.crit_hit > 0) {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.acid_do+class.crit_damage)+class.acidity_bonus+atk_type_db)/100)*(class.atk);
								hit_list[| i].damaged_strike_type = 2;
								class.crit_hit -= 1;
								class.fatal_point += class.fatal_buildup;
								var strike_type = 2;
								if (class.fatal_point >= 100) {
									class.fatal_point -= 100;
									class.fatal_hit += 1;
									hit_list[| i].damaged_strike_type = 3;
									var strike_type = 3;
								};
								var abs_dmg = dmg_out;
							};
							else {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.acid_do)+class.acidity_bonus+atk_type_db)/100)*(class.atk);
								hit_list[| i].damaged_strike_type = 1;
								var strike_type = 1;
								var abs_dmg = dmg_out;
							};
						};
						else if (hit_type == 3) {
							var origin = bbox_left + sprite_width/2;
							var half_origin = sprite_width/4;
							var origin_right = origin + half_origin;
							var origin_left = origin - half_origin;
							if (hit_list[| i].x >= x) {
								var enemy_pos = hit_list[| i].x - hit_list[| i].sprite_width/2;
								if (enemy_pos >= origin_left && enemy_pos <= origin_right) {
									var main_area = 1;
								};
								else {
									var main_area = 0;
									var gap = abs(enemy_pos - origin_right);
								};
							};
							else  {
								var enemy_pos = hit_list[| i].x + hit_list[| i].sprite_width/2;
								if (enemy_pos >= origin_left && enemy_pos <= origin_right) {
									var main_area = 1;
								};
								else {
									var main_area = 0;
									var gap = abs(enemy_pos - origin_left);
								};
							};
							if (class.fatal_hit > 0) {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.acid_do+class.crit_damage)+class.acidity_bonus+atk_type_db)/100)*(class.atk);
								hit_list[| i].damaged_strike_type = 3;
								var strike_type = 3;
								var abs_dmg = dmg_out;
							};
							else if (class.fatal_hit == 0 && class.crit_hit > 0) {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.acid_do+class.crit_damage)+class.acidity_bonus+atk_type_db)/100)*(class.atk);
								hit_list[| i].damaged_strike_type = 2;
								class.crit_hit -= 1;
								class.fatal_point += class.fatal_buildup;
								var strike_type = 2;
								if (class.fatal_point >= 100) {
									class.fatal_point -= 100;
									class.fatal_hit += 1;
									hit_list[| i].damaged_strike_type = 3;
									var strike_type = 3;
								};
								var abs_dmg = dmg_out;
							};
							else {
								dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do+class.acid_do)+class.acidity_bonus+atk_type_db)/100)*(class.atk);
								hit_list[| i].damaged_strike_type = 1;
								var strike_type = 1;
								var abs_dmg = dmg_out;
							};
						};
					};
					//Final Acid Damage
					if (hit_list[| i].fixed_tc == 0) {
						if (hit_type != 3) {
							if (target > 1 && class.fatal_hit == 0) {
								final_dmg = (dmg_out*((100-hit_list[| i].tc)/100))*(multi_dmg_scale/100);
								if (hit_list[| i].barrier > 0) {
									barrier_dmg = (dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100))*(multi_dmg_scale/100);
									final_dmg = (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(multi_dmg_scale/100);
								};
								else if (hit_list[| i].super_armor > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(multi_dmg_scale/100);
								};
								var rdc_dmg = final_dmg / (multi_dmg_scale/100);
							};
							else if (target > 1 && class.fatal_hit == 1) {
								final_dmg = (dmg_out*((100-hit_list[| i].tc)/100))*((multi_dmg_scale+class.fatal_damage)/100);
								if (hit_list[| i].barrier > 0) {
									barrier_dmg = (dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100))*((multi_dmg_scale+class.fatal_damage)/100);
									final_dmg = (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((multi_dmg_scale+class.fatal_damage)/100);
								};
								else if (hit_list[| i].super_armor > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((multi_dmg_scale+class.fatal_damage)/100);
								};
								class.fatal_hit -= 1;
								var rdc_dmg = final_dmg / ((multi_dmg_scale+class.fatal_damage)/100);
							};
							else if (class.fatal_hit == 0) {
								final_dmg = (dmg_out*((100-hit_list[| i].tc)/100))*(dmg_scale/100);
								if (hit_list[| i].barrier > 0) {
									barrier_dmg = (dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100))*(dmg_scale/100);
									final_dmg = (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(dmg_scale/100);
								};
								else if (hit_list[| i].super_armor > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(dmg_scale/100);
								};
								var rdc_dmg = final_dmg / (dmg_scale/100);
							};
							else {
								final_dmg = (dmg_out*((100-hit_list[| i].tc)/100))*((dmg_scale+class.fatal_damage)/100);
								if (hit_list[| i].barrier > 0) {
									barrier_dmg = (dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100))*((dmg_scale+class.fatal_damage)/100);
									final_dmg = (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								else if (hit_list[| i].super_armor > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								class.fatal_hit -= 1;
								var rdc_dmg = final_dmg / ((dmg_scale+class.fatal_damage)/100);
							};
						};
						else if (hit_type == 3 && main_area == 1) {
							if (class.fatal_hit == 0) {
								final_dmg = dmg_out*((100-hit_list[| i].tc)/100);
								if (hit_list[| i].barrier > 0) {
									barrier_dmg = dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100);
									final_dmg = dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100);
								};
								else if (hit_list[| i].super_armor > 0) {
									final_dmg = dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100);
								};
								var rdc_dmg = final_dmg;
							};
							else {
								final_dmg = (dmg_out*((100-hit_list[| i].tc)/100))*((dmg_scale+class.fatal_damage)/100);
								if (hit_list[| i].barrier > 0) {
									barrier_dmg = (dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100))*((dmg_scale+class.fatal_damage)/100);
									final_dmg = (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								else if (hit_list[| i].super_armor > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								class.fatal_hit -= 1;
								var rdc_dmg = final_dmg / ((dmg_scale+class.fatal_damage)/100);
							};
						};
						else if (hit_type == 3 && main_area == 0) {
							if (class.fatal_hit == 0) {
								final_dmg = (dmg_out*((100-hit_list[| i].tc)/100))*((half_origin-gap)/half_origin) + (dmg_out*((100-hit_list[| i].tc)/100))*(gap/half_origin)*(class.aoe_dmg_scale/100);
								if (hit_list[| i].barrier > 0) {
									barrier_dmg = (dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100))*((half_origin-gap)/half_origin) + (dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100))*(gap/half_origin)*(class.aoe_dmg_scale/100);
									final_dmg = (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((half_origin-gap)/half_origin) + (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(gap/half_origin)*(class.aoe_dmg_scale/100);
								};
								else if (hit_list[| i].super_armor > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((half_origin-gap)/half_origin) + (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(gap/half_origin)*(class.aoe_dmg_scale/100);
								};
								var rdc_dmg = final_dmg;
							};
							else {
								final_dmg = (dmg_out*((100-hit_list[| i].tc)/100))*((dmg_scale+class.fatal_damage)/100);
								if (hit_list[| i].barrier > 0) {
									barrier_dmg = (dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100))*((dmg_scale+class.fatal_damage)/100);
									final_dmg = (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								else if (hit_list[| i].super_armor > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								class.fatal_hit -= 1;
								var rdc_dmg = final_dmg / ((dmg_scale+class.fatal_damage)/100);
							};
						};
					};
					else {
						if (hit_type != 3) {
							if (target > 1 && class.fatal_hit == 0) {
								final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc)/100))*(multi_dmg_scale/100);
								if (hit_list[| i].barrier > 0) {
									barrier_dmg = (dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100))*(multi_dmg_scale/100);
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(multi_dmg_scale/100);
								};
								else if (hit_list[| i].super_armor > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(multi_dmg_scale/100);
								};
								var rdc_dmg = final_dmg / (multi_dmg_scale/100);
							};
							else if (target > 1 && class.fatal_hit == 1) {
								final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc)/100))*((multi_dmg_scale+class.fatal_damage)/100);
								if (hit_list[| i].barrier > 0) {
									barrier_dmg = (dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100))*((multi_dmg_scale+class.fatal_damage)/100);
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((multi_dmg_scale+class.fatal_damage)/100);
								};
								else if (hit_list[| i].super_armor > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((multi_dmg_scale+class.fatal_damage)/100);
								};
								class.fatal_hit -= 1;
								var rdc_dmg = final_dmg / ((multi_dmg_scale+class.fatal_damage)/100);
							};
							else if (class.fatal_hit == 0) {
								final_dmg = dmg_out*((100-hit_list[| i].fixed_tc)/100);
								if (hit_list[| i].barrier > 0) {
									barrier_dmg = dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100);
									final_dmg = dmg_out*((100-hit_list[| i].fixed_tc-(hit_list[| i].resistance*(100-class.ap)/100))/100);
								};
								else if (hit_list[| i].super_armor > 0) {
									final_dmg = dmg_out*((100-hit_list[| i].fixed_tc-(hit_list[| i].resistance*(100-class.ap)/100))/100);
								};
								var rdc_dmg = final_dmg;
							};
							else {
								final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc)/100))*((dmg_scale+class.fatal_damage)/100);
								if (hit_list[| i].barrier > 0) {
									barrier_dmg = (dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100))*((dmg_scale+class.fatal_damage)/100);
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								else if (hit_list[| i].super_armor > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								class.fatal_hit -= 1;
								var rdc_dmg = final_dmg / ((dmg_scale+class.fatal_damage)/100);
							};
						};
						else if (hit_type == 3 && main_area == 1) {
							if (class.fatal_hit == 0) {
								final_dmg = dmg_out*((100-hit_list[| i].fixed_tc)/100);
								if (hit_list[| i].barrier > 0) {
									barrier_dmg = dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100);
									final_dmg = dmg_out*((100-hit_list[| i].fixed_tc-(hit_list[| i].resistance*(100-class.ap)/100))/100);
								};
								else if (hit_list[| i].super_armor > 0) {
									final_dmg = dmg_out*((100-hit_list[| i].fixed_tc-(hit_list[| i].resistance*(100-class.ap)/100))/100);
								};
								var rdc_dmg = final_dmg;
							};
							else {
								final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc)/100))*((dmg_scale+class.fatal_damage)/100);
								if (hit_list[| i].barrier > 0) {
									barrier_dmg = (dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100))*((dmg_scale+class.fatal_damage)/100);
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								else if (hit_list[| i].super_armor > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								class.fatal_hit -= 1;
								var rdc_dmg = final_dmg / ((dmg_scale+class.fatal_damage)/100);
							};
						};
						else if (hit_type == 3 && main_area == 0) {
							if (class.fatal_hit == 0) {
								final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc)/100))*((half_origin-gap)/half_origin) + (dmg_out*((100-hit_list[| i].tc)/100))*(gap/half_origin)*(class.aoe_dmg_scale/100);
								if (hit_list[| i].barrier > 0) {
									barrier_dmg = (dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100))*((half_origin-gap)/half_origin) + (dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100))*(gap/half_origin)*(class.aoe_dmg_scale/100);
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((half_origin-gap)/half_origin) + (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(gap/half_origin)*(class.aoe_dmg_scale/100);
								};
								else if (hit_list[| i].super_armor > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((half_origin-gap)/half_origin) + (dmg_out*((100-hit_list[| i].tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*(gap/half_origin)*(class.aoe_dmg_scale/100);
								};
								var rdc_dmg = final_dmg;
							};
							else {
								final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc)/100))*((dmg_scale+class.fatal_damage)/100);
								if (hit_list[| i].barrier > 0) {
									barrier_dmg = (dmg_out*((100-hit_list[| i].tc-hit_list[| i].resistance+class.ab)/100))*((dmg_scale+class.fatal_damage)/100);
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								else if (hit_list[| i].super_armor > 0) {
									final_dmg = (dmg_out*((100-hit_list[| i].fixed_tc-(hit_list[| i].resistance*(100-class.ap)/100))/100))*((dmg_scale+class.fatal_damage)/100);
								};
								class.fatal_hit -= 1;
								var rdc_dmg = final_dmg / ((dmg_scale+class.fatal_damage)/100);
							};
						};
					};
					hit_list[| i].damaged_dmg_type = 2;
				};
				else if (dmg_type == 3) {
					if (dmg_range == 1) {
						if (hit_type == 1 || hit_type == 2) {
							dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do)+atk_type_db)/100)*(class.atk);
							var abs_dmg = dmg_out;
						};
						else if (hit_type == 3) {
							var origin = bbox_left + sprite_width/2;
							var half_origin = sprite_width/4;
							var origin_right = origin + half_origin;
							var origin_left = origin - half_origin;
							if (hit_list[| i].x >= x) {
								var enemy_pos = hit_list[| i].x - hit_list[| i].sprite_width/2;
								if (enemy_pos >= origin_left && enemy_pos <= origin_right) {
									var main_area = 1;
								};
								else {
									var main_area = 0;
									var gap = abs(enemy_pos - origin_right);
								};
							};
							else {
								var enemy_pos = hit_list[| i].x + hit_list[| i].sprite_width/2;
								if (enemy_pos >= origin_left && enemy_pos <= origin_right) {
									var main_area = 1;
								};
								else {
									var main_area = 0;
									var gap = abs(enemy_pos - origin_left);
								};
							};
							dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.melee_do)+atk_type_db)/100)*(class.atk);
							var abs_dmg = dmg_out;
						};
					};
					else if (dmg_range == 2) {
						if (hit_type == 1 || hit_type == 2) {
							dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do)+atk_type_db)/100)*(class.atk);
							var abs_dmg = dmg_out;
						};
						else if (hit_type == 3) {
							var origin = bbox_left + sprite_width/2;
							var half_origin = sprite_width/4;
							var origin_right = origin + half_origin;
							var origin_left = origin - half_origin;
							if (hit_list[| i].x >= x) {
								var enemy_pos = hit_list[| i].x - hit_list[| i].sprite_width/2;
								if (enemy_pos >= origin_left && enemy_pos <= origin_right) {
									var main_area = 1;
								};
								else {
									var main_area = 0;
									var gap = abs(enemy_pos - origin_right);
								};
							};
							else {
								var enemy_pos = hit_list[| i].x + hit_list[| i].sprite_width/2;
								if (enemy_pos >= origin_left && enemy_pos <= origin_right) {
									var main_area = 1;
								};
								else {
									var main_area = 0;
									var gap = abs(enemy_pos - origin_left);
								};
							};
							dmg_out = (((base_dmg/100)*(class.dmg_output+atk_type_do+class.ranged_do)+atk_type_db)/100)*(class.atk);
							var abs_dmg = dmg_out;
						};
					};
					//Final Absolute Damage
					if (hit_type != 3) {
						if (target > 1) {
							final_dmg = dmg_out*(multi_dmg_scale/100);
							var rdc_dmg = final_dmg / (multi_dmg_scale/100);
						};
						else {
							final_dmg = dmg_out*(dmg_scale/100);
							var rdc_dmg = final_dmg / (dmg_scale/100);
						};
					};
					else if (hit_type == 3 && main_area == 1) {
						final_dmg = dmg_out;
						var rdc_dmg = final_dmg;
					};
					else if (hit_type == 3 && main_area == 0) {
						final_dmg = dmg_out*((half_origin-gap)/half_origin) + dmg_out*(gap/half_origin)*(class.aoe_dmg_scale/100);
						var rdc_dmg = final_dmg;
					};
				
					if (class.crit_hit == 1) {
						class.crit_hit -= 1;
						class.fatal_point += class.fatal_buildup;
						if (class.fatal_point >= 100) {
							class.fatal_point -= 100;
							class.fatal_hit += 1;
						};
					};
					hit_list[| i].damaged_dmg_type = 3;
					var strike_type = 3;
				};
		
				//Damage musuh, damage text musuh
				if (no_damage == 0) {
					if (hit_list[| i].dashing == 0) {
						if (dmg_type == 1 && hit_list[| i].super_armor > 0) {
							super_armor_dmg = round(super_armor_dmg);
							super_armor_dmg = clamp(super_armor_dmg, 0, 9999999);
							hit_list[| i].super_armor -= super_armor_dmg;
							hit_list[| i].last_dmg = super_armor_dmg;
							if (hit_list[| i].super_armor < 0) {
								var armor_breach = hit_list[| i].super_armor;
								hit_list[| i].super_armor = 0;
								final_dmg += abs(armor_breach/2);
							};
							if (show_text >= 1) {
								var dmg_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oDamage);
								dmg_text.changed = 1;
								dmg_text.enemy_id = hit_list[| i];
								dmg_text.damage = super_armor_dmg;
								dmg_text.strike_type = strike_type;
								dmg_text.dmg_type = 4;
								dmg_text.vspeed -= random_range(3, 5);
								dmg_text.hspeed += random_range(-2, 2);
							};
						};
						if (dmg_type == 2 && hit_list[| i].barrier > 0) {
							barrier_dmg = round(barrier_dmg);
							barrier_dmg = clamp(barrier_dmg, 0, 9999999);
							hit_list[| i].barrier -= barrier_dmg;
							hit_list[| i].last_dmg = barrier_dmg;
							if (hit_list[| i].barrier < 0) {
								var barrier_breach = hit_list[| i].barrier;
								hit_list[| i].barrier = 0;
								final_dmg += abs(barrier_breach/2);
							};
							if (show_text >= 1) {
								var dmg_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oDamage);
								dmg_text.changed = 1;
								dmg_text.enemy_id = hit_list[| i];
								dmg_text.damage = barrier_dmg;
								dmg_text.strike_type = strike_type;
								dmg_text.dmg_type = 5;
								dmg_text.vspeed -= random_range(3, 5);
								dmg_text.hspeed += random_range(-2, 2);
							};
						};
						final_dmg = round(final_dmg);
						final_dmg = clamp(final_dmg, 0, 999999999);
						class.total_dmg += final_dmg;
						hit_list[| i].hp -= final_dmg;
						hit_list[| i].last_dmg = final_dmg;
						//hit_list[| i].hp_trail = hit_list[| i].hp + final_dmg;
						if (hit_list[| i].defeated == 0) {
							hit_list[| i].flash_alpha = 1;
						};
						hit_list[| i].hit = 1;
						if (img_bot == 1) {
							oStageComplete.total_dmg += final_dmg;
						};
						hit_list[| i].deducted_dmg += round(abs_dmg) - round(rdc_dmg);
						if (img_bot == 0 || (class.id == hit_list[| i])) {
							oStageComplete.deducted_dmg += round(abs_dmg) - round(rdc_dmg);
						};
						if (show_text >= 1) {
							var dmg_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oDamage);
							dmg_text.changed = 1;
							dmg_text.enemy_id = hit_list[| i];
							dmg_text.damage = final_dmg;
							dmg_text.strike_type = strike_type;
							dmg_text.dmg_type = dmg_type;
							dmg_text.vspeed -= random_range(3, 5);
							dmg_text.hspeed += random_range(-2, 2);
						};
				
						//Kasih jenis serangan buat IMG-Bot
						if (strike_type == 1) {
							class.normal_strike = 1;
						};
						else if (strike_type == 2) {
							class.crit_strike = 1;
						};
						else if (strike_type == 3) {
							class.fatal_strike = 1;
						};
						class.strike_atk_type = atk_type;
						hit_list[| i].hit_strike_type = strike_type;
						hit_list[| i].hit_dmg_type = dmg_type;
				
						//Tambah combo hit
						if (buildup == 1) {
							if (img_bot == 1) {
								oCombo.combo += 1;
								oCombo.combo_reset = 0;
								oCombo.scale = 2;
							};
						};
					};
					else {
						hit_list[| i].dodge += 1;
						if (show_text >= 1) {
							var vfx_text = instance_create_depth(x, y, -992, oVFXText);
							vfx_text.text = "Miss"
							vfx_text.vspeed -= random_range(3, 5);
						}
						
					};
				};
		
				//Interupt musuh
				if (interruptable == 1) {
					if (hit_list[| i].dashing == 0) {
						if (hit_list[| i].fixed_ignore_interruption > 0) {
							if (int_power > hit_list[| i].fixed_ignore_interruption) {
								hit_list[| i].interrupted = 1;
								if (atk_dir == 1) {
									hit_list[| i].hspeed = random_range(2, 4);
								};
								else if (atk_dir == 2) {
									hit_list[| i].hspeed = random_range(-4, -2);
								};
								else {
									if (hit_list[| i].x >= x) {
										hit_list[| i].hspeed = random_range(2, 4);
									};
									else {
										hit_list[| i].hspeed = random_range(-4, -2);
									};
								};
								hit_list[| i].last_spd = hit_list[| i].hspeed;
								hit_list[| i].stop = 1;
							};
						};
						else {
							if (int_power > hit_list[| i].ignore_interruption) {
								hit_list[| i].interrupted = 1;
								if (atk_dir == 1) {
									hit_list[| i].hspeed = random_range(2, 4);
								};
								else if (atk_dir == 2) {
									hit_list[| i].hspeed = random_range(-4, -2);
								};
								else {
									if (hit_list[| i].x >= x) {
										hit_list[| i].hspeed = random_range(2, 4);
									};
									else {
										hit_list[| i].hspeed = random_range(-4, -2);
									};
								};
								hit_list[| i].last_spd = hit_list[| i].hspeed;
								hit_list[| i].stop = 1;
							};
						};
					};
				};
		
				//Visual FX pas hit
				if (hit_vfx == 1 && global.vfx > 1) {
					if (hit_list[| i].dashing == 0) {
						if (dmg_range == 1) {
							if (strike_type == 1) {
								if (atk_dir == 1) {
									var vfx1 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									var vfx2 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									vfx1.image_angle = random_range(15, 75);
								};
								else if (atk_dir == 2) {
									var vfx1 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									var vfx2 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									vfx1.image_angle = random_range(105, 165);
								};
								else if (atk_dir == 3 || atk_dir == 4) {
									if (hit_list[| i].x >= x) {
										var vfx1 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										var vfx2 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										vfx1.image_angle = random_range(15, 75);
									};
									else {
										var vfx1 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										var vfx2 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										vfx1.image_angle = random_range(105, 165);
									};
								};
								vfx2.image_angle = vfx1.image_angle + 180;
							};
							else if (strike_type == 2) {
								if (atk_dir == 1) {
									var vfx1 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									var vfx2 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									var vfx3 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									vfx1.image_angle = random_range(15, 75);
								};
								else if (atk_dir == 2) {
									var vfx1 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									var vfx2 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									var vfx3 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									vfx1.image_angle = random_range(105, 165);
								};
								else if (atk_dir == 3 || atk_dir == 4) {
									if (hit_list[| i].x >= x) {
										var vfx1 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										var vfx2 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										var vfx3 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										vfx1.image_angle = random_range(15, 75);
									};
									else {
										var vfx1 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										var vfx2 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										var vfx3 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										vfx1.image_angle = random_range(105, 165);
									};
								};
								vfx2.image_angle = vfx1.image_angle + 120;
								vfx3.image_angle = vfx1.image_angle + 240;
							};
							else if (strike_type == 3) {
								if (atk_dir == 1) {
									var vfx1 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									var vfx2 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									var vfx3 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									var vfx4 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									vfx1.image_angle = random_range(15, 75);
								};
								else if (atk_dir == 2) {
									var vfx1 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									var vfx2 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									var vfx3 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									var vfx4 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
									vfx1.image_angle = random_range(105, 165);
								};
								else if (atk_dir == 3 || atk_dir == 4) {
									if (hit_list[| i].x >= x) {
										var vfx1 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										var vfx2 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										var vfx3 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										var vfx4 = instance_create_depth(hit_list[| i].x - abs((class.bbox_right - hit_list[| i].bbox_left)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										vfx1.image_angle = random_range(15, 75);
									};
									else {
										var vfx1 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										var vfx2 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										var vfx3 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										var vfx4 = instance_create_depth(hit_list[| i].x - abs((class.bbox_left - hit_list[| i].bbox_right)/2), hit_list[| i].y, hit_list[| i].depth - 10, oMeleeHitVFX1);
										vfx1.image_angle = random_range(105, 165);
									};
								};
								vfx2.image_angle = vfx1.image_angle + 90;
								vfx3.image_angle = vfx1.image_angle + 180;
								vfx4.image_angle = vfx1.image_angle + 270;
							};
						};
					};
				};
			};
		};
		if (return_type == 1) {
			return final_dmg;
		};
		else if (return_type == 2) {
			return main_area;
		};
	};
};