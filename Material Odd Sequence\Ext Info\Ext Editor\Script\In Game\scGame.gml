function grav_create(strength, bound_to = noone, max_vel = 5, mul = 0){
	// strength = gravity (px / frame^2) (rec <= 0.5)
	// bound_to = inst_id
	// max_vel = max velocity (px / frame) (rec <= 10)
	return {
		str : strength,
		vel_y : 0,
		val : 0,			// result
		mul : mul,
		bound_to : bound_to,
		max_vel : max_vel
	};
}

function grav_update(grav_str) {
	// grav_str = struct grav dari grav_create()
	grav_str.vel_y += grav_str.str * gamespd_multiplier(grav_str.mul);
	grav_str.vel_y = min(grav_str.vel_y, grav_str.max_vel * gamespd_multiplier(grav_str.mul));
	grav_str.val += grav_str.vel_y * gamespd_multiplier(grav_str.mul);
	if (grav_str.bound_to != noone) {
		if (instance_exists(grav_str.bound_to)) {
			grav_str.bound_to.y += grav_str.val;
		}
	}
	
	
}

function battle_cam_scroll_create(cam_id) {
	var cam_str = {
		cam : cam_id,
		scrolling : 0,
        last_mouse_x : 0,
        last_mouse_y : 0,
		area_x : 0,
		area_y : 0,
		vel_x : 0,  
		vel_y : 0,  
        deceleration : 0.6
	};
	cam_str.area_x = camera_get_view_x(cam_id) + camera_get_view_width(cam_id)/2;
	cam_str.area_y = camera_get_view_y(cam_id) + camera_get_view_height(cam_id)/2;
	return cam_str;
}

function battle_cam_scroll(scroll_str) {
	// scroll_str = dari battle_cam_scroll_create()
	if (mouse_check_button_pressed(mb_left) && scroll_str.scrolling == 0) {
		scroll_str.scrolling = 1;
		scroll_str.last_mouse_x = mouse_x;
		scroll_str.last_mouse_y = mouse_y;
		scroll_str.vel_x = 0;
		scroll_str.vel_y = 0;
	}
	
	if (scroll_str.scrolling == 1) {
		var dx = scroll_str.last_mouse_x - mouse_x;
		var dy = scroll_str.last_mouse_y - mouse_y;
		
		var prev_scroll_area_x = scroll_str.area_x; 
		var prev_scroll_area_y = scroll_str.area_y; 
		
		scroll_str.area_x += dx;
		scroll_str.area_y += dy;
		
		scroll_str.vel_x = (scroll_str.area_x - prev_scroll_area_x) * gamespd_multiplier(0, 1);
		scroll_str.vel_y = (scroll_str.area_y - prev_scroll_area_y) * gamespd_multiplier(0, 1);
		
		scroll_str.last_mouse_x = mouse_x;
		scroll_str.last_mouse_y = mouse_y;
		
		if (mouse_check_button_released(mb_left)) {
		    scroll_str.scrolling = 0;
		}
	} else {
		if (abs(scroll_str.vel_x) > 0.1) {
		    scroll_str.area_x += scroll_str.vel_x;
		    scroll_str.vel_x *= scroll_str.deceleration; 
		}
		if (abs(scroll_str.vel_y) > 0.1) {
		    scroll_str.area_y += scroll_str.vel_y;
		    scroll_str.vel_y *= scroll_str.deceleration; 
		}
		
		if (scroll_str.area_x < camera_get_view_width(scroll_str.cam)/2) {
		    scroll_str.area_x += ((camera_get_view_width(scroll_str.cam)/2) - scroll_str.area_x) / 10 * gamespd_multiplier();
		    scroll_str.vel_x = 0; 
		} else if (scroll_str.area_x > room_width - (camera_get_view_width(scroll_str.cam)/2)) {
		    scroll_str.area_x += ((room_width - (camera_get_view_width(scroll_str.cam)/2)) - scroll_str.area_x) / 10 * gamespd_multiplier();
		    scroll_str.vel_x = 0; 
		}
		
		if (scroll_str.area_y < camera_get_view_height(scroll_str.cam)/2) {
		    scroll_str.area_y += ((camera_get_view_height(scroll_str.cam)/2) - scroll_str.area_y) / 10 * gamespd_multiplier();
		    scroll_str.vel_y = 0; 
		} else if (scroll_str.area_y > room_height - (camera_get_view_height(scroll_str.cam)/2)) {
		    scroll_str.area_y += ((room_height - (camera_get_view_height(scroll_str.cam)/2)) - scroll_str.area_y) / 10 * gamespd_multiplier();
		    scroll_str.vel_y = 0; 
		}
	}
}

function time_to_step(real_time) {
	return (real_time * game_get_speed(gamespeed_fps));
}

function step_to_time(step) {
	return (step / game_get_speed(gamespeed_fps));
}

/*function BotsAllData(_data, _attr, _state, _stats) constructor {
    data = _data;		// db_get_row(db_bots, db_type_bots, class_num)
    attr = _attr;		// BotsAttr()
    state = _state;		// BotsState()
    stats = _stats;		// data_bots_stats_tohash(stats_struct)

    static destroy = function() {
        delete self;
    }
}*/

function bots_damage(bots_id, weapon_data, atk_data, atk_range, buildup = true, skip_self = true) {
	// bots_id = inst_id | [inst1_id, inst2_id, ...]
	// weapon_data = Botsweapon_data(_bots_parent, _data)
    // atk_data = struct -> batk.num1, derv, ...
	// atk_range = Vec4()
    // buildup = boolean

	var source_id = noone;
	var target_id = noone;
	var target_list = ds_list_create();		// for temporary storage of target list from collision_rectangle_list()
	var targets = [];						// targets = [target1_id, target2_id, ...]
	var target_cnt = 0;

	var damages = [];
	var dmg_excess = false;

	if (!is_array(bots_id)) {
		bots_id = [bots_id];
	}
	source_id = bots_id[0];

	// set the raw targets
	switch (atk_data.target) {
		case skill_target_type.own:
			array_push(targets, bots_id[0]);
			break;

		case skill_target_type.ally:
			for (var i = 1; i < array_length(bots_id); i++) {
				array_push(targets, bots_id[i]);
			}
			break;

		case skill_target_type.enemy:
			target_cnt = collision_rectangle_list(
				weapon_data.x + atk_range.x, weapon_data.y + atk_range.y, weapon_data.x + atk_range.x + atk_range.w, weapon_data.y + atk_range.y + atk_range.h,
				oStgEnemy, false, true, target_list, true
			);
			for (var i = 0; i < target_cnt; i++) {
				array_push(targets, target_list[| i]);
			}
			break;

		case skill_target_type.team:
			target_cnt = collision_rectangle_list(
				weapon_data.x + atk_range.x, weapon_data.y + atk_range.y, weapon_data.x + atk_range.x + atk_range.w, weapon_data.y + atk_range.y + atk_range.h,
				oStgTeam, false, true, target_list, true
			);
			for (var i = 0; i < target_cnt; i++) {
				array_push(targets, target_list[| i]);
			}
			break;

		case skill_target_type.all_bots:
			target_cnt = collision_rectangle_list(
				weapon_data.x + atk_range.x, weapon_data.y + atk_range.y, weapon_data.x + atk_range.x + atk_range.w, weapon_data.y + atk_range.y + atk_range.h,
				oStgBots, false, true, target_list, true
			);
			for (var i = 0; i < target_cnt; i++) {
				array_push(targets, target_list[| i]);
			}
			break;
	}
	target_cnt = array_length(targets);

	for (var i = 0; i < target_cnt; i++) {
		target_id = targets[i];

		// skip self and non-existing instances
		if (skip_self && target_id == bots_id) {
			continue;
		}
		if (!instance_exists(target_id)) {
			continue;
		}

	}
	
	ds_list_destroy(target_list);
}
