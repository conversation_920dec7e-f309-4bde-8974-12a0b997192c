#include <regex>
#include <string>
#include <vector>
#include <cstring>
#include <mutex>
#include <memory>
#include <stdexcept>

// Required DLL export definition
#ifdef _WIN64
    #define func extern "C" __declspec(dllexport)
#else
    #define func extern "C"
#endif

// Thread-safe result storage
class ResultManager {
private:
    static std::mutex mutex;
    static std::string lastResult;
    static std::string lastError;
    
public:
    static const char* setResult(const std::string& result) {
        std::lock_guard<std::mutex> lock(mutex);
        lastResult = result;
        return lastResult.c_str();
    }
    
    static const char* setError(const std::string& error) {
        std::lock_guard<std::mutex> lock(mutex);
        lastError = error;
        return nullptr;
    }
    
    static const char* getLastError() {
        std::lock_guard<std::mutex> lock(mutex);
        return lastError.c_str();
    }
};

std::mutex ResultManager::mutex;
std::string ResultManager::lastResult;
std::string ResultManager::lastError;

// Get the last error
func const char* regex_get_last_error() {
    return ResultManager::getLastError();
}

// 1. Match at beginning (full string match) - returns "true" or "false" as string
func const char* regex_match(const char* pattern, const char* input) {
    try {
        auto re = std::make_unique<std::regex>(pattern);
        bool result = std::regex_match(std::string(input), *re);
        return ResultManager::setResult(result ? "true" : "false");
    } catch (const std::regex_error& e) {
        ResultManager::setError("Regex error: " + std::string(e.what()));
        return ResultManager::setResult("error");
    }
}

// 2. Search for first match (anywhere in string) - returns "true" or "false" as string
func const char* regex_search(const char* pattern, const char* input) {
    try {
        auto re = std::make_unique<std::regex>(pattern);
        bool result = std::regex_search(std::string(input), *re);
        return ResultManager::setResult(result ? "true" : "false");
    } catch (const std::regex_error& e) {
        ResultManager::setError("Regex error: " + std::string(e.what()));
        return ResultManager::setResult("error");
    }
}

// 3. Find all matches - returns a single string with matches separated by |
func const char* regex_find_all(const char* pattern, const char* input) {
    try {
        std::string result;
        std::string str(input);
        auto re = std::make_unique<std::regex>(pattern);
        
        auto it = std::sregex_iterator(str.begin(), str.end(), *re);
        auto end = std::sregex_iterator();
        
        for (; it != end; ++it) {
            // Check if there's a capture group and use it instead of the whole match
            if (it->size() > 1) {
                result += (*it)[1].str() + "|";  // Use the first capture group
            } else {
                result += it->str() + "|";  // Fallback to the whole match
            }
        }
        
        // Return empty string if no matches found
        if (result.empty()) {
            return ResultManager::setResult("");
        }
        
        // Remove trailing newline if exists
        if (!result.empty() && result.back() == '|') {
            result.pop_back();
        }
        
        return ResultManager::setResult(result);
    } catch (const std::regex_error& e) {
        ResultManager::setError("Regex error: " + std::string(e.what()));
        return ResultManager::setResult("error");
    }
}

// 4. Replace matches
func const char* regex_replace_all(const char* pattern, const char* input, const char* replacement) {
    try {
        auto re = std::make_unique<std::regex>(pattern);
        std::string result = std::regex_replace(std::string(input), *re, std::string(replacement));
        return ResultManager::setResult(result);
    } catch (const std::regex_error& e) {
        ResultManager::setError("Regex error: " + std::string(e.what()));
        return ResultManager::setResult("error");
    }
}

// 5. Split input based on regex - 
func const char* regex_split(const char* pattern, const char* input) {
    try {
        std::string str(input);
        std::string result;
        auto re = std::make_unique<std::regex>(pattern);
        
        // Tokenize the string using regex as delimiter
        std::vector<std::string> tokens;
        std::sregex_token_iterator iter(str.begin(), str.end(), *re, -1);
        std::sregex_token_iterator end;
        
        for (; iter != end; ++iter) {
            std::string token = *iter;
            result += token + "|";
        }
        
        // Remove trailing newline if exists
        if (!result.empty() && result.back() == '|') {
            result.pop_back();
        }
        
        return ResultManager::setResult(result);
    } catch (const std::regex_error& e) {
        ResultManager::setError("Regex error: " + std::string(e.what()));
        return ResultManager::setResult("error");
    }
}


// 6. Extract groups from first match
func const char* regex_extract_groups(const char* pattern, const char* input, const char* separator) {
    try {
        std::string result;
        std::string str(input);
        auto re = std::make_unique<std::regex>(pattern);
        
        std::smatch matches;
        if (std::regex_search(str, matches, *re)) {
            for (size_t i = 0; i < matches.size(); i++) {
                if (i > 0) result += separator;
                result += matches[i].str();
            }
        }
        
        return ResultManager::setResult(result);
    } catch (const std::regex_error& e) {
        ResultManager::setError("Regex error: " + std::string(e.what()));
        return ResultManager::setResult("error");
    }
}

// 7. Count matches - returns count as string
func const char* regex_count_matches(const char* pattern, const char* input) {
    try {
        std::string str(input);
        auto re = std::make_unique<std::regex>(pattern);
        
        auto it = std::sregex_iterator(str.begin(), str.end(), *re);
        auto end = std::sregex_iterator();
        
        int count = static_cast<int>(std::distance(it, end));
        return ResultManager::setResult(std::to_string(count));
    } catch (const std::regex_error& e) {
        ResultManager::setError("Regex error: " + std::string(e.what()));
        return ResultManager::setResult("error");
    }
}

// 8. Test if pattern is valid - returns "true" or "false" as string
func const char* regex_is_valid(const char* pattern) {
    try {
        auto re = std::make_unique<std::regex>(pattern);
        return ResultManager::setResult("true");
    } catch (const std::regex_error& e) {
        ResultManager::setError("Regex error: " + std::string(e.what()));
        return ResultManager::setResult("false");
    }
}

// 9. Replace first match only
func const char* regex_replace_first(const char* pattern, const char* input, const char* replacement) {
    try {
        std::string str(input);
        auto re = std::make_unique<std::regex>(pattern);
        
        std::smatch matches;
        if (std::regex_search(str, matches, *re)) {
            str.replace(matches.position(), matches.length(), replacement);
        }
        
        return ResultManager::setResult(str);
    } catch (const std::regex_error& e) {
        ResultManager::setError("Regex error: " + std::string(e.what()));
        return ResultManager::setResult("error");
    }
}

// 10. Find all matches with their positions
func const char* regex_find_all_with_positions(const char* pattern, const char* input) {
    try {
        std::string result;
        std::string str(input);
        auto re = std::make_unique<std::regex>(pattern);
        
        auto it = std::sregex_iterator(str.begin(), str.end(), *re);
        auto end = std::sregex_iterator();
        
        for (; it != end; ++it) {
            result += std::to_string(it->position()) + ":" + 
                      std::to_string(it->length()) + ":" + 
                      it->str() + "|";
        }
        
        // Remove trailing newline if exists
        if (!result.empty() && result.back() == '|') {
            result.pop_back();
        }
        
        return ResultManager::setResult(result);
    } catch (const std::regex_error& e) {
        ResultManager::setError("Regex error: " + std::string(e.what()));
        return ResultManager::setResult("error");
    }
}
