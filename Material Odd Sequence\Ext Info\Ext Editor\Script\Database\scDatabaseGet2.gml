function db_get_skill_up(db_skill, class_num, type, skill_num) {
	// class_num dimulai dari 1
	// db_skill = db_batk, db_derv, ..., db_exsk
	// type = 1-batk, 2-derv, 3-spmv, 4-ulti, 5-intg, 6-uniq, 7-ext
	
	var result = {item_num: [], item_cost: []};

	var skill_data = db_get_row(db_skill, 6, skill_num);
	switch (type) {
		case 1:		// BATK
			switch (class_num) {
				case 1:			// Warrior
					switch (skill_num) {
						case 1:			// Slash
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9]; break;
								case 4: result.item_num = [35, 8, 7]; break;
								case 5: result.item_num = [35, 20, 8]; break;
								case 6: result.item_num = [36, 18, 21]; break;
								case 7: result.item_num = [36, 27, 20]; break;
								case 8: result.item_num = [37, 22, 30]; break;
								case 9: result.item_num = [37, 33, 27]; break;
							}
						break;
						case 2:			// Blow
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 8]; break;
								case 4: result.item_num = [35, 7, 9]; break;
								case 5: result.item_num = [35, 14, 8]; break;
								case 6: result.item_num = [36, 11, 12]; break;
								case 7: result.item_num = [36, 23, 12]; break;
								case 8: result.item_num = [37, 30, 26]; break;
								case 9: result.item_num = [37, 32, 29]; break;
							}
						break;
						case 3:			// Thrust
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9]; break;
								case 4: result.item_num = [35, 8, 7]; break;
								case 5: result.item_num = [35, 20, 8]; break;
								case 6: result.item_num = [36, 18, 21]; break;
								case 7: result.item_num = [36, 27, 20]; break;
								case 8: result.item_num = [37, 22, 30]; break;
								case 9: result.item_num = [37, 33, 27]; break;
							}
						break;
						case 4:			// Double Slash
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9]; break;
								case 4: result.item_num = [35, 8, 7]; break;
								case 5: result.item_num = [35, 17, 8]; break;
								case 6: result.item_num = [36, 21, 12]; break;
								case 7: result.item_num = [36, 22, 10]; break;
								case 8: result.item_num = [37, 26, 30]; break;
								case 9: result.item_num = [37, 33, 25]; break;
							}
						break;
						case 5:			// Twin Thrust
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 7]; break;
								case 4: result.item_num = [35, 9, 8]; break;
								case 5: result.item_num = [35, 16, 9]; break;
								case 6: result.item_num = [36, 18, 20]; break;
								case 7: result.item_num = [36, 28, 10]; break;
								case 8: result.item_num = [37, 30, 24]; break;
								case 9: result.item_num = [37, 31, 23]; break;
							}
						break;
						case 6:			// Uppercut
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 8]; break;
								case 4: result.item_num = [35, 7, 9]; break;
								case 5: result.item_num = [35, 12, 8]; break;
								case 6: result.item_num = [36, 14, 13]; break;
								case 7: result.item_num = [36, 27, 17]; break;
								case 8: result.item_num = [37, 26, 30]; break;
								case 9: result.item_num = [37, 32, 28]; break;
							}
						break;
						case 7:			// Spinning Slash
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 7]; break;
								case 4: result.item_num = [35, 9, 8]; break;
								case 5: result.item_num = [35, 17, 9]; break;
								case 6: result.item_num = [36, 18, 20]; break;
								case 7: result.item_num = [36, 26, 10]; break;
								case 8: result.item_num = [37, 24, 30]; break;
								case 9: result.item_num = [37, 31, 22]; break;
							}
						break;
						case 8:			// Slam
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 8]; break;
								case 4: result.item_num = [35, 7, 9]; break;
								case 5: result.item_num = [35, 18, 8]; break;
								case 6: result.item_num = [36, 11, 13]; break;
								case 7: result.item_num = [36, 24, 19]; break;
								case 8: result.item_num = [37, 27, 22]; break;
								case 9: result.item_num = [37, 32, 26]; break;
							}
						break;
						case 9:			// Charged Slash
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9]; break;
								case 4: result.item_num = [35, 8, 7]; break;
								case 5: result.item_num = [35, 12, 8]; break;
								case 6: result.item_num = [36, 11, 18]; break;
								case 7: result.item_num = [36, 22, 21]; break;
								case 8: result.item_num = [37, 25, 29]; break;
								case 9: result.item_num = [37, 33, 30]; break;
							}
						break;
						case 10:		// Smash
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9]; break;
								case 4: result.item_num = [35, 8, 7]; break;
								case 5: result.item_num = [35, 19, 8]; break;
								case 6: result.item_num = [36, 16, 11]; break;
								case 7: result.item_num = [36, 22, 12]; break;
								case 8: result.item_num = [37, 28, 26]; break;
								case 9: result.item_num = [37, 33, 23]; break;
							}
						break;
						case 11:		// Stab
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 7]; break;
								case 4: result.item_num = [35, 9, 8]; break;
								case 5: result.item_num = [35, 15, 7]; break;
								case 6: result.item_num = [36, 14, 10]; break;
								case 7: result.item_num = [36, 24, 11]; break;
								case 8: result.item_num = [37, 26, 29]; break;
								case 9: result.item_num = [37, 31, 23]; break;
							}
						break;
					}
				break;
				case 2:			// Archer
					switch (skill_num) {
						case 1:			// Ordinary Shot
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9]; break;
								case 4: result.item_num = [35, 8, 7]; break;
								case 5: result.item_num = [35, 18, 8]; break;
								case 6: result.item_num = [36, 15, 13]; break;
								case 7: result.item_num = [36, 28, 20]; break;
								case 8: result.item_num = [37, 22, 30]; break;
								case 9: result.item_num = [37, 33, 27]; break;
							}
						break;
						case 2:			// Heavy Shot
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 7]; break;
								case 4: result.item_num = [35, 9, 8]; break;
								case 5: result.item_num = [35, 11, 9]; break;
								case 6: result.item_num = [36, 19, 18]; break;
								case 7: result.item_num = [36, 25, 15]; break;
								case 8: result.item_num = [37, 27, 29]; break;
								case 9: result.item_num = [37, 31, 24]; break;
							}
						break;
						case 3:			// Multi Shot
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 8]; break;
								case 4: result.item_num = [35, 7, 9]; break;
								case 5: result.item_num = [35, 16, 8]; break;
								case 6: result.item_num = [36, 11, 18]; break;
								case 7: result.item_num = [36, 23, 17]; break;
								case 8: result.item_num = [37, 30, 26]; break;
								case 9: result.item_num = [37, 32, 25]; break;
							}
						break;
						case 4:			// Double Shot
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9]; break;
								case 4: result.item_num = [35, 8, 7]; break;
								case 5: result.item_num = [35, 17, 8]; break;
								case 6: result.item_num = [36, 21, 12]; break;
								case 7: result.item_num = [36, 22, 10]; break;
								case 8: result.item_num = [37, 26, 30]; break;
								case 9: result.item_num = [37, 33, 25]; break;
							}
						break;
						case 5:			// Parallel Shot
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 7]; break;
								case 4: result.item_num = [35, 9, 8]; break;
								case 5: result.item_num = [35, 21, 9]; break;
								case 6: result.item_num = [36, 15, 20]; break;
								case 7: result.item_num = [36, 28, 19]; break;
								case 8: result.item_num = [37, 30, 22]; break;
								case 9: result.item_num = [37, 31, 23]; break;
							}
						break;
						case 6:			// Acidic Shot
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 8]; break;
								case 4: result.item_num = [35, 7, 9]; break;
								case 5: result.item_num = [35, 15, 8]; break;
								case 6: result.item_num = [36, 14, 13]; break;
								case 7: result.item_num = [36, 26, 17]; break;
								case 8: result.item_num = [37, 23, 30]; break;
								case 9: result.item_num = [37, 32, 28]; break;
							}
						break;
						case 7:			// Piercing Shot
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9]; break;
								case 4: result.item_num = [35, 8, 7]; break;
								case 5: result.item_num = [35, 18, 9]; break;
								case 6: result.item_num = [36, 15, 10]; break;
								case 7: result.item_num = [36, 30, 20]; break;
								case 8: result.item_num = [37, 24, 23]; break;
								case 9: result.item_num = [37, 32, 27]; break;
							}
						break;
						case 8:			// Charged Shot
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9]; break;
								case 4: result.item_num = [35, 8, 7]; break;
								case 5: result.item_num = [35, 19, 8]; break;
								case 6: result.item_num = [36, 16, 18]; break;
								case 7: result.item_num = [36, 22, 11]; break;
								case 8: result.item_num = [37, 28, 27]; break;
								case 9: result.item_num = [37, 33, 24]; break;
							}
						break;
						case 9:			// Explosive Shot
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 7]; break;
								case 4: result.item_num = [35, 9, 8]; break;
								case 5: result.item_num = [35, 17, 9]; break;
								case 6: result.item_num = [36, 18, 20]; break;
								case 7: result.item_num = [36, 26, 15]; break;
								case 8: result.item_num = [37, 25, 30]; break;
								case 9: result.item_num = [37, 31, 22]; break;
							}
						break;
					}
				break;
				case 3:			// Medic
					switch (skill_num) {
						case 1:			// Slash
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 8]; break;
								case 4: result.item_num = [35, 7, 9]; break;
								case 5: result.item_num = [35, 12, 8]; break;
								case 6: result.item_num = [36, 14, 10]; break;
								case 7: result.item_num = [36, 30, 13]; break;
								case 8: result.item_num = [37, 26, 30]; break;
								case 9: result.item_num = [37, 32, 24]; break;
							}
						break;
						case 2:			// Blow
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 7]; break;
								case 4: result.item_num = [35, 9, 8]; break;
								case 5: result.item_num = [35, 11, 7]; break;
								case 6: result.item_num = [36, 19, 18]; break;
								case 7: result.item_num = [36, 28, 15]; break;
								case 8: result.item_num = [37, 27, 29]; break;
								case 9: result.item_num = [37, 31, 23]; break;
							}
						break;
						case 3:			// Thrust
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 8]; break;
								case 4: result.item_num = [35, 7, 9]; break;
								case 5: result.item_num = [35, 21, 8]; break;
								case 6: result.item_num = [36, 11, 18]; break;
								case 7: result.item_num = [36, 23, 20]; break;
								case 8: result.item_num = [37, 30, 25]; break;
								case 9: result.item_num = [37, 33, 26]; break;
							}
						break;
						case 4:			// Acidic Slash
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9]; break;
								case 4: result.item_num = [35, 8, 7]; break;
								case 5: result.item_num = [35, 17, 8]; break;
								case 6: result.item_num = [36, 19, 10]; break;
								case 7: result.item_num = [36, 24, 16]; break;
								case 8: result.item_num = [37, 26, 22]; break;
								case 9: result.item_num = [37, 32, 25]; break;
							}
						break;
						case 5:			// Uppercut
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 7]; break;
								case 4: result.item_num = [35, 8, 9]; break;
								case 5: result.item_num = [35, 15, 9]; break;
								case 6: result.item_num = [36, 13, 11]; break;
								case 7: result.item_num = [36, 28, 12]; break;
								case 8: result.item_num = [37, 30, 29]; break;
								case 9: result.item_num = [37, 31, 23]; break;
							}
						break;
						case 6:			// Spinning Slash
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 8]; break;
								case 4: result.item_num = [35, 7, 9]; break;
								case 5: result.item_num = [35, 12, 8]; break;
								case 6: result.item_num = [36, 14, 19]; break;
								case 7: result.item_num = [36, 26, 20]; break;
								case 8: result.item_num = [37, 23, 22]; break;
								case 9: result.item_num = [37, 33, 28]; break;
							}
						break;
						case 7:			// Stab
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9]; break;
								case 4: result.item_num = [35, 8, 7]; break;
								case 5: result.item_num = [35, 18, 7]; break;
								case 6: result.item_num = [36, 16, 13]; break;
								case 7: result.item_num = [36, 24, 15]; break;
								case 8: result.item_num = [37, 28, 27]; break;
								case 9: result.item_num = [37, 33, 26]; break;
							}
						break;
						case 8:			// Drag Out
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 7]; break;
								case 4: result.item_num = [35, 9, 8]; break;
								case 5: result.item_num = [35, 10, 9]; break;
								case 6: result.item_num = [36, 18, 20]; break;
								case 7: result.item_num = [36, 26, 16]; break;
								case 8: result.item_num = [37, 25, 30]; break;
								case 9: result.item_num = [37, 31, 27]; break;
							}
						break;
					}
				break;
			}
			
			switch (skill_data.level) {
				case 1: result.item_cost = [5]; break;
				case 2: result.item_cost = [10, 5]; break;
				case 3: result.item_cost = [3, 5]; break;
				case 4: result.item_cost = [5, 10, 5]; break;
				case 5: result.item_cost = [7, 3, 10]; break;
				case 6: result.item_cost = [2, 5, 3]; break;
				case 7: result.item_cost = [4, 3, 5]; break;
				case 8: result.item_cost = [2, 5, 3]; break;
				case 9: result.item_cost = [5, 2, 5]; break;
			}
		break;
		case 2:		// DERV
			switch (class_num) {
				case 1:			// Warrior
					switch (skill_num) {
						case 1:			// Self Healing
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9]; break;
								case 4: result.item_num = [35, 8, 7]; break;
								case 5: result.item_num = [35, 20, 8]; break;
								case 6: result.item_num = [36, 18, 21]; break;
								case 7: result.item_num = [36, 27, 20]; break;
								case 8: result.item_num = [37, 22, 30]; break;
								case 9: result.item_num = [37, 33, 27]; break;
							}
						break;
						case 2:			// Redirect
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 7]; break;
								case 4: result.item_num = [35, 9, 8]; break;
								case 5: result.item_num = [35, 17, 9]; break;
								case 6: result.item_num = [36, 18, 20]; break;
								case 7: result.item_num = [36, 26, 10]; break;
								case 8: result.item_num = [37, 24, 30]; break;
								case 9: result.item_num = [37, 31, 22]; break;
							}
						break;
					}
				break;
				case 2:			// Archer
					switch (skill_num) {
						case 1:			// Follow-up Shot
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9]; break;
								case 4: result.item_num = [35, 8, 7]; break;
								case 5: result.item_num = [35, 17, 8]; break;
								case 6: result.item_num = [36, 21, 12]; break;
								case 7: result.item_num = [36, 22, 10]; break;
								case 8: result.item_num = [37, 26, 30]; break;
								case 9: result.item_num = [37, 33, 25]; break;
							}
						break;
						case 2:			// Twister Shot
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9]; break;
								case 4: result.item_num = [35, 8, 7]; break;
								case 5: result.item_num = [35, 18, 9]; break;
								case 6: result.item_num = [36, 15, 10]; break;
								case 7: result.item_num = [36, 30, 20]; break;
								case 8: result.item_num = [37, 24, 23]; break;
								case 9: result.item_num = [37, 32, 27]; break;
							}
						break;
					}
				break;
				case 3:			// Medic
					switch (skill_num) {
						case 1:			// Dart/Serum Throw
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9]; break;
								case 4: result.item_num = [35, 8, 7]; break;
								case 5: result.item_num = [35, 17, 8]; break;
								case 6: result.item_num = [36, 19, 10]; break;
								case 7: result.item_num = [36, 24, 16]; break;
								case 8: result.item_num = [37, 26, 22]; break;
								case 9: result.item_num = [37, 32, 25]; break;
							}
						break;
					}
				break;
			}

			switch (skill_data.level) {
				case 1: result.item_cost = [10]; break;
				case 2: result.item_cost = [15, 5]; break;
				case 3: result.item_cost = [4, 5]; break;
				case 4: result.item_cost = [7, 10, 5]; break;
				case 5: result.item_cost = [10, 3, 10]; break;
				case 6: result.item_cost = [3, 5, 5]; break;
				case 7: result.item_cost = [6, 5, 5]; break;
				case 8: result.item_cost = [3, 5, 5]; break;
				case 9: result.item_cost = [7, 3, 5]; break;
			}
		break;
		case 3:		// SPMV
			switch (class_num) {
				case 1:			// Warrior
					switch (skill_num) {
						case 1:			// Determination
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9, 6]; break;
								case 4: result.item_num = [35, 8, 7, 6]; break;
								case 5: result.item_num = [35, 12, 8, 6]; break;
								case 6: result.item_num = [36, 11, 18, 9]; break;
								case 7: result.item_num = [36, 22, 21, 7]; break;
								case 8: result.item_num = [37, 25, 29, 16]; break;
								case 9: result.item_num = [37, 33, 30, 21]; break;
							}
						break;
						case 2:			// Sidecut
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9, 6]; break;
								case 4: result.item_num = [35, 8, 7, 6]; break;
								case 5: result.item_num = [35, 20, 8, 6]; break;
								case 6: result.item_num = [36, 18, 21, 8]; break;
								case 7: result.item_num = [36, 27, 20, 9]; break;
								case 8: result.item_num = [37, 22, 30, 14]; break;
								case 9: result.item_num = [37, 33, 27, 12]; break;
							}
						break;
						case 3:			// Parry
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 7, 6]; break;
								case 4: result.item_num = [35, 9, 8, 6]; break;
								case 5: result.item_num = [35, 16, 9, 6]; break;
								case 6: result.item_num = [36, 18, 20, 8]; break;
								case 7: result.item_num = [36, 28, 10, 7]; break;
								case 8: result.item_num = [37, 30, 24, 11]; break;
								case 9: result.item_num = [37, 31, 23, 10]; break;
							}
						break;
					}
				break;
				case 2:			// Archer
					switch (skill_num) {
						case 1:			// Flick Shot
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9, 6]; break;
								case 4: result.item_num = [35, 8, 7, 6]; break;
								case 5: result.item_num = [35, 18, 8, 6]; break;
								case 6: result.item_num = [36, 15, 13, 9]; break;
								case 7: result.item_num = [36, 28, 20, 7]; break;
								case 8: result.item_num = [37, 22, 30, 10]; break;
								case 9: result.item_num = [37, 33, 27, 12]; break;
							}
						break;
						case 2:			// Spike/Electric Shot
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 7, 6]; break;
								case 4: result.item_num = [35, 9, 8, 6]; break;
								case 5: result.item_num = [35, 21, 9, 6]; break;
								case 6: result.item_num = [36, 15, 20, 8]; break;
								case 7: result.item_num = [36, 28, 19, 9]; break;
								case 8: result.item_num = [37, 30, 22, 19]; break;
								case 9: result.item_num = [37, 31, 23, 21]; break;
							}
						break;
						case 3:			// Half-Moon Shot
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 8, 6]; break;
								case 4: result.item_num = [35, 7, 9, 6]; break;
								case 5: result.item_num = [35, 16, 8, 6]; break;
								case 6: result.item_num = [36, 11, 18, 9]; break;
								case 7: result.item_num = [36, 23, 17, 7]; break;
								case 8: result.item_num = [37, 30, 26, 15]; break;
								case 9: result.item_num = [37, 32, 25, 12]; break;
							}
						break;
						case 4:			// Supercharged Shot
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 9, 6]; break;
								case 4: result.item_num = [35, 8, 7, 6]; break;
								case 5: result.item_num = [35, 19, 8, 6]; break;
								case 6: result.item_num = [36, 16, 18, 9]; break;
								case 7: result.item_num = [36, 22, 20, 8]; break;
								case 8: result.item_num = [37, 28, 27, 13]; break;
								case 9: result.item_num = [37, 33, 24, 14]; break;
							}
						break;
					}
				break;
				case 3:			// Medic
					switch (skill_num) {
						case 1:			// Acid Vial
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 8, 6]; break;
								case 4: result.item_num = [35, 7, 9, 6]; break;
								case 5: result.item_num = [35, 21, 8, 6]; break;
								case 6: result.item_num = [36, 11, 18, 8]; break;
								case 7: result.item_num = [36, 23, 20, 7]; break;
								case 8: result.item_num = [37, 30, 25, 11]; break;
								case 9: result.item_num = [37, 33, 26, 10]; break;
							}
						break;
						case 2:			// Healing Potion
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 8, 6]; break;
								case 4: result.item_num = [35, 7, 9, 6]; break;
								case 5: result.item_num = [35, 12, 9, 6]; break;
								case 6: result.item_num = [36, 14, 10, 7]; break;
								case 7: result.item_num = [36, 30, 13, 8]; break;
								case 8: result.item_num = [37, 26, 30, 12]; break;
								case 9: result.item_num = [37, 32, 24, 14]; break;
							}
						break;
						case 3:			// Diminisher Knife
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [34, 6]; break;
								case 3: result.item_num = [35, 8, 6]; break;
								case 4: result.item_num = [35, 7, 9, 6]; break;
								case 5: result.item_num = [35, 12, 8, 6]; break;
								case 6: result.item_num = [36, 14, 19, 9]; break;
								case 7: result.item_num = [36, 26, 20, 7]; break;
								case 8: result.item_num = [37, 23, 22, 15]; break;
								case 9: result.item_num = [37, 33, 28, 11]; break;
							}
						break;
					}
				break;
			}

			switch (skill_data.level) {
				case 1: result.item_cost = [10]; break;
				case 2: result.item_cost = [15, 5]; break;
				case 3: result.item_cost = [4, 5, 5]; break;
				case 4: result.item_cost = [7, 10, 5, 5]; break;
				case 5: result.item_cost = [10, 3, 10, 5]; break;
				case 6: result.item_cost = [3, 5, 5, 5]; break;
				case 7: result.item_cost = [6, 5, 5, 5]; break;
				case 8: result.item_cost = [3, 5, 5, 5]; break;
				case 9: result.item_cost = [7, 3, 5, 5]; break;
			}
		break;
		case 4:		// ULTI
			switch (class_num) {
				case 1:			// Warrior
					switch (skill_num) {
						case 1:			// Whirlwind Cut
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [35, 6]; break;
								case 3: result.item_num = [35, 8, 6]; break;
								case 4: result.item_num = [35, 7, 9, 6]; break;
								case 5: result.item_num = [36, 12, 8, 6]; break;
								case 6: result.item_num = [36, 14, 13, 7]; break;
								case 7: result.item_num = [37, 27, 17, 9]; break;
								case 8: result.item_num = [37, 26, 30, 15]; break;
								case 9: result.item_num = [37, 32, 28, 13]; break;
							}
						break;
						case 2:			// Devastating Ambush
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [35, 6]; break;
								case 3: result.item_num = [35, 7, 6]; break;
								case 4: result.item_num = [35, 8, 9, 6]; break;
								case 5: result.item_num = [36, 15, 7, 6]; break;
								case 6: result.item_num = [36, 14, 10, 9]; break;
								case 7: result.item_num = [37, 24, 11, 8]; break;
								case 8: result.item_num = [37, 26, 29, 17]; break;
								case 9: result.item_num = [37, 31, 23, 11]; break;
							}
						break;
						case 3:			// Dauting Shockwaves
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [35, 6]; break;
								case 3: result.item_num = [35, 9, 6]; break;
								case 4: result.item_num = [35, 7, 8, 6]; break;
								case 5: result.item_num = [36, 17, 8, 6]; break;
								case 6: result.item_num = [36, 21, 12, 7]; break;
								case 7: result.item_num = [37, 22, 10, 8]; break;
								case 8: result.item_num = [37, 26, 30, 19]; break;
								case 9: result.item_num = [37, 33, 25, 15]; break;
							}
						break;
					}
				break;
				case 2:			// Archer
					switch (skill_num) {
						case 1:			// Cloudburst Volley
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [35, 6]; break;
								case 3: result.item_num = [35, 8, 6]; break;
								case 4: result.item_num = [35, 7, 9, 6]; break;
								case 5: result.item_num = [36, 15, 8, 6]; break;
								case 6: result.item_num = [36, 14, 13, 7]; break;
								case 7: result.item_num = [37, 26, 17, 9]; break;
								case 8: result.item_num = [37, 23, 30, 13]; break;
								case 9: result.item_num = [37, 32, 28, 18]; break;
							}
						break;
						case 2:			// Harpoon Breakout
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [35, 6]; break;
								case 3: result.item_num = [35, 7, 6]; break;
								case 4: result.item_num = [35, 9, 8, 6]; break;
								case 5: result.item_num = [36, 11, 9, 6]; break;
								case 6: result.item_num = [36, 19, 18, 8]; break;
								case 7: result.item_num = [37, 25, 15, 9]; break;
								case 8: result.item_num = [37, 27, 29, 11]; break;
								case 9: result.item_num = [37, 31, 24, 17]; break;
							}
						break;
						case 3:			// Pinpoint Shooting
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [35, 6]; break;
								case 3: result.item_num = [35, 7, 6]; break;
								case 4: result.item_num = [35, 9, 8, 6]; break;
								case 5: result.item_num = [36, 17, 9, 6]; break;
								case 6: result.item_num = [36, 18, 20, 7]; break;
								case 7: result.item_num = [37, 26, 15, 8]; break;
								case 8: result.item_num = [37, 25, 24, 15]; break;
								case 9: result.item_num = [37, 31, 22, 19]; break;
							}
						break;
					}
				break;
				case 3:			// Medic
					switch (skill_num) {
						case 1:			// Medical Kit
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [35, 6]; break;
								case 3: result.item_num = [35, 7, 6]; break;
								case 4: result.item_num = [35, 8, 9, 6]; break;
								case 5: result.item_num = [36, 15, 8, 6]; break;
								case 6: result.item_num = [36, 13, 11, 9]; break;
								case 7: result.item_num = [37, 28, 12, 7]; break;
								case 8: result.item_num = [37, 30, 29, 19]; break;
								case 9: result.item_num = [37, 31, 23, 18]; break;
							}
						break;
						case 2:			// Corrosive Concentration
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [35, 6]; break;
								case 3: result.item_num = [35, 7, 6]; break;
								case 4: result.item_num = [35, 9, 8, 6]; break;
								case 5: result.item_num = [36, 10, 9, 6]; break;
								case 6: result.item_num = [36, 18, 20, 7]; break;
								case 7: result.item_num = [37, 26, 16, 8]; break;
								case 8: result.item_num = [37, 25, 30, 11]; break;
								case 9: result.item_num = [37, 31, 27, 17]; break;
							}
						break;
						case 3:			// CBC Booster
							switch (skill_data.level) {
								case 1: result.item_num = [34]; break;
								case 2: result.item_num = [35, 6]; break;
								case 3: result.item_num = [35, 7, 6]; break;
								case 4: result.item_num = [35, 9, 8, 6]; break;
								case 5: result.item_num = [36, 11, 7, 6]; break;
								case 6: result.item_num = [36, 19, 18, 9]; break;
								case 7: result.item_num = [37, 28, 15, 8]; break;
								case 8: result.item_num = [37, 27, 29, 10]; break;
								case 9: result.item_num = [37, 31, 23, 15]; break;
							}
						break;
					}
				break;
			}

			switch (skill_data.level) {
				case 1: result.item_cost = [20]; break;
				case 2: result.item_cost = [5, 10]; break;
				case 3: result.item_cost = [7, 10, 10]; break;
				case 4: result.item_cost = [10, 10, 10, 10]; break;
				case 5: result.item_cost = [3, 5, 10, 10]; break;
				case 6: result.item_cost = [5, 7, 5, 10]; break;
				case 7: result.item_cost = [2, 5, 7, 10]; break;
				case 8: result.item_cost = [5, 7, 5, 10]; break;
				case 9: result.item_cost = [10, 5, 7, 10]; break;
			}
		break;
	}

	delete skill_data;
	return result;
}

function db_get_skill_adj(db_skill, class_num, type, skill_num, db_bots = 0) {
	// class_num dimulai dari 1
	// db_skill = db_batk, db_derv, ..., db_exsk
	// type = 1-batk, 2-derv, 3-spmv, 4-ulti, 5-intg, 6-uniq, 7-ext
	var sect_name = ["", "sk_batk", "sk_derv", "sk_spmv", "sk_ulti", "sk_intg", "sk_uniq", "sk_ext"];
	var result = {};

	var skill_data = db_get_row(db_skill, 6, skill_num);
	switch (type) {
		case 1:		// BATK
			switch (class_num) {
				case 1:			// Warrior
					switch (skill_num) {
						case 2:			// Blow
							result = {
								num1: {
									text: switch_lang("Arah", "Direction"),
									val: [switch_lang("Ke depan", "Forward"), switch_lang("Ke bawah", "Downward")],
									tooltip_text: switch_lang("Arah serangan dan arah knockback dari skill ini.\nPenyesuaian: Ke depan atau Ke bawah.", 
																"The attack direction and knockback direction of this skill.\nAdjustment: Forward or Downward."),
									bound: {adj_num: 0, linear: 0, letter: ""}
								}
							}; 
						break;
						case 3:			// Thrust
							result = {
								num1: {
									text: switch_lang("Jarak", "Distance"),
									val: [switch_lang("Dekat", "Close"), switch_lang("Sedang", "Medium"), switch_lang("Jauh", "Far"), switch_lang("Sangat Jauh", "Very Far")],
									tooltip_text: switch_lang("Jarak yang akan dilewati saat menggunakan skill ini.\nPenyesuaian: Dekat, Sedang, Jauh, atau Sangat Jauh.", 
																"The distance that will be covered while using this skill.\nAdjustment: Close, Medium, Far, or Very Far."),
									bound: {adj_num: 0, linear: 0, letter: ""}
								}
							}; 
						break;
						case 6:			// Uppercut
							result = {
								num1: {
									text: switch_lang("Lompat Otomatis", "Auto Jump"),
									val: [switch_lang("Tidak", "No"), switch_lang("Ya", "Yes")],
									tooltip_text: switch_lang("Bot ini akan melompat secara otomatis saat menggunakan skill ini.\nPenyesuaian: Tidak atau Ya.", 
																"This bot will jump automatically while using this skill.\nAdjustment: No or Yes."),
									bound: {adj_num: 0, linear: 0, letter: ""}
								}
							}; 
						break;
						case 11:		// Stab
							result = {
								num1: {
									text: switch_lang("Jarak", "Distance"),
									val: [switch_lang("Dekat", "Close"), switch_lang("Sedang", "Medium"), switch_lang("Jauh", "Far")],
									tooltip_text: switch_lang("Jarak yang akan dilewati saat menggunakan skill ini.\nPenyesuaian: Dekat, Sedang, atau Jauh.", 
																"The distance that will be covered while using this skill.\nAdjustment: Close, Medium, or Far."),
									bound: {adj_num: 0, linear: 0, letter: ""}
								}
							}; 
						break;
					}
				break;
				case 2:			// Archer
					switch (skill_num) {
						case 2:			// Heavy Shot
						case 8:			// Charged Shot
						case 9:			// Explosive Shot
							result = {
								num1: {
									text: switch_lang("Langkah Mundur", "Backstep"),
									val: [switch_lang("Dekat", "Close"), switch_lang("Sedang", "Medium"), switch_lang("Jauh", "Far")],
									tooltip_text: switch_lang("Jarak langkah mundur dari posisi awal yang akan dilewati saat menggunakan skill ini.\nPenyesuaian: Dekat, Sedang, atau Jauh.", 
																"The step back distance from the initial position that will be covered when using this skill.\nAdjustment: Close, Medium, or Far."),
									bound: {adj_num: 0, linear: 0, letter: ""}
								}
							}; 
						break;
					}
				break;
				case 3:			// Medic
					switch (skill_num) {
						case 2:			// Blow
							result = {
								num1: {
									text: switch_lang("Arah", "Direction"),
									val: [switch_lang("Ke depan", "Forward"), switch_lang("Sedikit ke atas", "Slightly upward")],
									tooltip_text: switch_lang("Arah serangan dan arah knockback dari skill ini.\nPenyesuaian: Ke depan atau Sedikit ke atas.", 
																"The attack direction and knockback direction of this skill.\nAdjustment: Forward or Slightly upward."),
									bound: {adj_num: 0, linear: 0, letter: ""}
								}
							}; 
						break;
						case 3:			// Thrust
							result = {
								num1: {
									text: switch_lang("Jarak", "Distance"),
									val: [switch_lang("Dekat", "Close"), switch_lang("Sedang", "Medium"), switch_lang("Jauh", "Far")],
									tooltip_text: switch_lang("Jarak yang akan dilewati saat menggunakan skill ini.\nPenyesuaian: Dekat, Sedang, atau Jauh.", 
																"The distance that will be covered while using this skill.\nAdjustment: Close, Medium, or Far."),
									bound: {adj_num: 0, linear: 0, letter: ""}
								}
							}; 
						break;
					}
				break;
			}
		break;
		default:
			var bots_data = db_get_row(db_bots, 1, class_num);
			file_text_decrypt(string_lower(bots_data.name) + ".txt");
			
			switch (type) {
				case 2:		// DERV
					switch (class_num) {
						case 3:			// Medic
							switch (skill_num) {
								case 1:			// Dart/Serum Throw
									result = {
										num1: {
											text: switch_lang("Jenis Kerusakan", "Damage Type"),
											val: [switch_lang("Fisik", "Physical"), switch_lang("Asam", "Acid"), switch_lang("Campuran", "Mixed")],
											tooltip_text: switch_lang("Jenis kerusakan dari skill ini.\nPenyesuaian: Fisik, Asam, atau Campuran (Fisik dan Asam).", 
																		"The damage type of this skill.\nAdjustment: Physical, Acid, or Mixed (Physical and Acid)."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 1)
										}
									}; 
								break;
							}
						break;
					}
				break;
				case 3:		// SPMV
					switch (class_num) {
						case 1:			// Warrior
							switch (skill_num) {
								case 1:			// Determination
									result = {
										num1: {
											text: switch_lang("Pengali Buff", "Buff Multiplier"),
											val: [switch_lang("0.5x", "0.5x"), switch_lang("0.75x", "0.75x"), switch_lang("1x", "1x"), switch_lang("1.25x", "1.25x"), switch_lang("1.5x", "1.5x")],
											tooltip_text: switch_lang("Jumlah pengali untuk efektivitas buff pada skill ini. Nilai yang dikalikan akan sesuai dengan nilai pada bagian detail. Penyesuaian ini berbanding lurus dengan penyesuaian Pengali Durasi dan Pengali Cooldown.\nPenyesuaian: 0.5x, 0.75x, 1x, 1.25x, atau 1.5x.", 
																	  "The number of multipliers for this skill's buff effectiveness. The multiplied value will match the value in the details section. This adjustment is directly proportional to the Duration Multiplier and Cooldown Multiplier.\nAdjustment: 0.5x, 0.75x, 1x, 1.25x, or 1.5x."),
											bound: {adj_num: [2, 3], linear: 1, letter: "A"},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 3)
										},
										num2: {
											text: switch_lang("Pengali Durasi", "Duration Multiplier"),
											val: [switch_lang("0.5x", "0.5x"), switch_lang("0.75x", "0.75x"), switch_lang("1x", "1x"), switch_lang("1.25x", "1.25x"), switch_lang("1.5x", "1.5x")],
											tooltip_text: switch_lang("Jumlah pengali untuk durasi buff pada skill ini. Nilai yang dikalikan akan sesuai dengan nilai pada bagian detail. Penyesuaian ini berbanding lurus dengan penyesuaian Pengali Buff dan Pengali Cooldown.\nPenyesuaian: 0.5x, 0.75x, 1x, 1.25x, atau 1.5x.", 
																	  "The number of multipliers for this skill's buff duration. The multiplied value will match the value in the details section. This adjustment is directly proportional to the Buff Multiplier and Cooldown Multiplier.\nAdjustment: 0.5x, 0.75x, 1x, 1.25x, or 1.5x."),
											bound: {adj_num: [1, 3], linear: 1, letter: "A"},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj2", 3)
										},
										num3: {
											text: switch_lang("Pengali Cooldown", "Cooldown Multiplier"),
											val: [switch_lang("0.5x", "0.5x"), switch_lang("0.75x", "0.75x"), switch_lang("1x", "1x"), switch_lang("1.25x", "1.25x"), switch_lang("1.5x", "1.5x")],
											tooltip_text: switch_lang("Jumlah pengali untuk cooldown pada skill ini. Nilai yang dikalikan akan sesuai dengan nilai pada bagian detail. Penyesuaian ini berbanding lurus dengan penyesuaian Pengali Durasi dan Pengali Cooldown.\nPenyesuaian: 0.5x, 0.75x, 1x, 1.25x, atau 1.5x.", 
																	  "The number of multipliers for this skill's cooldown. The multiplied value will match the value in the details section. This adjustment is directly proportional to the Buff Multiplier and Duration Multiplier.\nAdjustment: 0.5x, 0.75x, 1x, 1.25x, or 1.5x."),
											bound: {adj_num: [1, 2], linear: 1, letter: "A"},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj3", 3)
										}
									};
								break;
								case 2:		// Side Cut
									result = {
										num1: {
											text: switch_lang("Jarak", "Distance"),
											val: [switch_lang("Dekat", "Close"), switch_lang("Sedang", "Medium"), switch_lang("Jauh", "Far"), switch_lang("Sangat Jauh", "Very Far")],
											tooltip_text: switch_lang("Jarak yang akan dilewati saat menggunakan skill ini.\nPenyesuaian: Dekat, Sedang, Jauh, atau Sangat Jauh.", 
																	  "The distance that will be covered while using this skill.\nAdjustment: Close, Medium, Far, or Very Far."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 1)
										}
									}; 
								break;
								case 3:		// Parry
									result = {
										num1: {
											text: switch_lang("Pengali Durasi", "Duration Multiplier"),
											val: [switch_lang("0.7x", "0.7x"), switch_lang("1x", "1x"), switch_lang("1.3x", "1.3x")],
											tooltip_text: switch_lang("Jumlah pengali untuk durasi pada skill ini. Nilai yang dikalikan akan sesuai dengan nilai pada bagian detail. Penyesuaian ini berbanding lurus dengan penyesuaian Pengali Cooldown.\nPenyesuaian: 0.7x, 1x, atau 1.3x.", 
																	  "The number of multipliers for this skill's duration. The multiplied value will match the value in the details section. This adjustment is directly proportional to the Cooldown Multiplier.\nAdjustment: 0.7x, 1x, or 1.3x."),
											bound: {adj_num: 2, linear: 1, letter: "A"},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 2)
										},
										num2: {
											text: switch_lang("Pengali Cooldown", "Cooldown Multiplier"),
											val: [switch_lang("0.7x", "0.7x"), switch_lang("1x", "1x"), switch_lang("1.3x", "1.3x")],
											tooltip_text: switch_lang("Jumlah pengali untuk cooldown pada skill ini. Nilai yang dikalikan akan sesuai dengan nilai pada bagian detail. Penyesuaian ini berbanding lurus dengan penyesuaian Pengali Durasi.\nPenyesuaian: 0.7x, 1x, atau 1.3x.", 
																	  "The number of multipliers for this skill's cooldown. The multiplied value will match the value in the details section. This adjustment is directly proportional to the Duration Multiplier.\nAdjustment: 0.7x, 1x, or 1.3x."),
											bound: {adj_num: 1, linear: 1, letter: "A"},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj2", 2)
										}
									}; 
								break;
							}
						break;
						case 2:			// Archer
							switch (skill_num) {
								case 1:			// Flick Shot
									result = {
										num1: {
											text: switch_lang("Jarak", "Distance"),
											val: [switch_lang("Dekat", "Close"), switch_lang("Sedang", "Medium"), switch_lang("Jauh", "Far")],
											tooltip_text: switch_lang("Jarak yang akan dilewati saat menggunakan skill ini.\nPenyesuaian: Dekat, Sedang, atau Jauh.", 
																	  "The distance that will be covered while using this skill.\nAdjustment: Close, Medium, or Far."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 1)
										}
									}; 
								break;
								case 2:			// Spike/Electric Shot
									result = {
										num1: {
											text: switch_lang("Konsumsi Tumpukan Maks", "Max Consumed Stacks"),
											val: [switch_lang("1", "1"), switch_lang("2", "2"), switch_lang("3", "3")],
											tooltip_text: switch_lang("Jumlah maksimum tumpukan Multiplex Arrowhead yang bisa dikonsumsi saat menggunakan skill ini.\nPenyesuaian: 1, 2, atau 3.", 
																	  "The maximum number of Multiplex Arrowhead stacks that can be consumed when using this skill.\nAdjustment: 1, 2, or 3."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 2)
										}
									}; 
								break;
								case 3:			// Half-Moon Shot
									result = {
										num1: {
											text: switch_lang("Jarak Lompatan", "Jump Distance"),
											val: [switch_lang("Dekat", "Close"), switch_lang("Sedang", "Medium"), switch_lang("Jauh", "Far"), switch_lang("Sangat Jauh", "Very Far")],
											tooltip_text: switch_lang("Jarak lompatan yang akan dilewati saat menggunakan skill ini.\nPenyesuaian: Dekat, Sedang, Jauh, atau Sangat Jauh.", 
																	  "The jump distance that will be covered while using this skill.\nAdjustment: Close, Medium, Far, or Very Far."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 2)
										},
										num2: {
											text: switch_lang("Konsumsi Tumpukan Maks", "Max Consumed Stacks"),
											val: [switch_lang("1", "1"), switch_lang("2", "2"), switch_lang("3", "3")],
											tooltip_text: switch_lang("Jumlah maksimum tumpukan Multiplex Arrowhead yang bisa dikonsumsi saat menggunakan skill ini.\nPenyesuaian: 1, 2, atau 3.", 
																	  "The maximum number of Multiplex Arrowhead stacks that can be consumed when using this skill.\nAdjustment: 1, 2, or 3."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj2", 2)
										}
									}; 
								break;
								case 4:			// Supercharged Shot
									result = {
										num1: {
											text: switch_lang("Konsumsi Tumpukan Maks", "Max Consumed Stacks"),
											val: [switch_lang("1", "1"), switch_lang("2", "2"), switch_lang("3", "3"), switch_lang("4", "4")],
											tooltip_text: switch_lang("Jumlah maksimum tumpukan Multiplex Arrowhead yang bisa dikonsumsi saat menggunakan skill ini.\nPenyesuaian: 1, 2, 3, atau 4.", 
																	  "The maximum number of Multiplex Arrowhead stacks that can be consumed when using this skill.\nAdjustment: 1, 2, 3, or 4."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 2)
										}
									}; 
								break;
							}
						break;
						case 3:			// Medic
							switch (skill_num) {
								case 2:			// Healing Potion
									result = {
										num1: {
											text: switch_lang("Pengali Penyembuhan", "Healing Multiplier"),
											val: [switch_lang("0.5x", "0.5x"), switch_lang("1x", "1x")],
											tooltip_text: switch_lang("Jumlah pengali untuk penyembuhan pada skill ini. Nilai yang dikalikan akan sesuai dengan nilai pada bagian detail. Penyesuaian ini berbanding lurus dengan penyesuaian Pengali Interval.\nPenyesuaian: 0.5x atau 1x.", 
																	  "The number of multipliers for this skill's duration. The multiplied value will match the value in the details section. This adjustment is directly proportional to the Interval Multiplier.\nAdjustment: 0.5x or 1x."),
											bound: {adj_num: 2, linear: 1, letter: "A"},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 2)
										},
										num2: {
											text: switch_lang("Pengali Interval", "Interval Multiplier"),
											val: [switch_lang("0.5x", "0.5x"), switch_lang("1x", "1x")],
											tooltip_text: switch_lang("Jumlah pengali untuk cooldown pada skill ini. Nilai yang dikalikan akan sesuai dengan nilai pada bagian detail. Penyesuaian ini berbanding lurus dengan penyesuaian Pengali Penyembuhan.\nPenyesuaian: 0.5x atau 1x.", 
																	  "The number of multipliers for this skill's cooldown. The multiplied value will match the value in the details section. This adjustment is directly proportional to the Healing Multiplier.\nAdjustment: 0.5x or 1x."),
											bound: {adj_num: 1, linear: 1, letter: "A"},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj2", 2)
										}
									}; 
								break;
								case 3:			// Diminisher Knife
									result = {
										num1: {
											text: switch_lang("Jenis Kerusakan", "Damage Type"),
											val: [switch_lang("Fisik", "Physical"), switch_lang("Asam", "Acid")],
											tooltip_text: switch_lang("Jenis kerusakan dari skill ini.\nPenyesuaian: Fisik atau Asam.", 
																	  "The damage type of this skill.\nAdjustment: Physical or Acid."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 1)
										},
										num2: {
											text: switch_lang("Jenis Debuff", "Debuff Type"),
											val: [switch_lang("Slow (25%)", "Slow (25%)"), switch_lang("Weaken (20%)", "Weaken (20%)"), switch_lang("Broken (15%)", "Broken (15%)")],
											tooltip_text: switch_lang("Jenis debuff yang timbul dari dari skill ini.\n\nPenyesuaian:\nSlow = Mengurangi Agility target (25%).\nWeaken = Mengurangi Attack Amplifier target (20%).\nBroken = Mengurangi Physical dan Acid DMG Reduction target (15%).", 
																	  "The type of debuff given from this skill.\n\nAdjustment:\nSlow = Reduce the target's Agility (25%).\nWeaken = Reduce the target's Attack Amplifier (20%).\nBroken = Reduce the target's Physical and Acid DMG Reduction (15%)."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj2", 1),
											real_val: [25, 20, 15]
										}
									}; 
								break;
							}
						break;
					}
				break;
				case 4:		// ULTI
					switch (class_num) {
						case 1:			// Warrior
							switch (skill_num) {
								case 2:			// Devastating Ambush
									result = {
										num1: {
											text: switch_lang("Tinggi Lompatan", "Jump Height"),
											val: [switch_lang("Pendek", "Short"), switch_lang("Sedang", "Medium"), switch_lang("Tinggi", "High")],
											tooltip_text: switch_lang("Tinggi lompatan saat menggunakan skill ini.\nPenyesuaian: Pendek, Sedang, atau Tinggi.", 
																	  "The jump height while using this skill.\nAdjustment: Short, Medium, or High."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 2)
										}
									}; 
								break;
							}
						break;
						case 2:			// Archer
							switch (skill_num) {
								case 1:			// Cloudburst Volley
									result = {
										num1: {
											text: switch_lang("Cakupan Area", "Area Coverage"),
											val: [switch_lang("Kecil", "Small"), switch_lang("Sedang", "Medium"), switch_lang("Besar", "Large")],
											tooltip_text: switch_lang("Jangkauan efektif area yang ditargetkan dari skill ini.Penyesuaian ini berbanding lurus dengan penyesuaian Waktu Tunda.\nPenyesuaian: Kecil, Sedang, atau Besar.", 
																	  "Effective range of targeted area of this skill. This adjustment is directly proportional to the Delay Time.\nAdjustment: Small, Medium, or Large."),
											bound: {adj_num: 2, linear: 1, letter: "A"},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 1)
										},
										num2: {
											text: switch_lang("Waktu Tunda", "Delay Time"),
											val: [switch_lang("1 Detik", "1 Second"), switch_lang("2 Detik", "2 Seconds"), switch_lang("3 Detik", "3 Seconds")],
											tooltip_text: switch_lang("Waktu tunda dari skill ini sebelum serangannya terjadi.Penyesuaian ini berbanding lurus dengan penyesuaian Cakupan Area.\nPenyesuaian: 1 Detik, 2 Detik, atau 3 Detik.", 
																	  "The delay time of this skill before the attack occurs. This adjustment is directly proportional to the Area Coverage.\nAdjustment: 1 Second, 2 Seconds, or 3 Seconds."),
											bound: {adj_num: 1, linear: 1, letter: "A"},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj2", 1)
										},
										num3: {
											text: switch_lang("Konsumsi Tumpukan Maks", "Max Consumed Stacks"),
											val: [switch_lang("0", "0"), switch_lang("1", "1"), switch_lang("2", "2"), switch_lang("3", "3"), switch_lang("4", "4"), switch_lang("5", "5")],
											tooltip_text: switch_lang("Jumlah maksimum tumpukan Multiplex Arrowhead yang bisa dikonsumsi saat menggunakan skill ini.\nPenyesuaian: 0, 1, 2, 3, 4, atau 5.", 
																	  "The maximum number of Multiplex Arrowhead stacks that can be consumed when using this skill.\nAdjustment: 0, 1, 2, 3, 4, or 5."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj3", 4)
										}
									}; 
								break;
								case 2:			// Harpoon Breakout
									result = {
										num1: {
											text: switch_lang("Konsumsi Tumpukan Maks", "Max Consumed Stacks"),
											val: [switch_lang("0", "0"), switch_lang("1", "1")],
											tooltip_text: switch_lang("Jumlah maksimum tumpukan Multiplex Arrowhead yang bisa dikonsumsi saat menggunakan skill ini.\nPenyesuaian: 0 atau 1.", 
																	  "The maximum number of Multiplex Arrowhead stacks that can be consumed when using this skill.\nAdjustment: 0 or 1."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 2)
										}
									}; 
								break;
								case 3:			// Pinpoint Shooting
									result = {
										num1: {
											text: switch_lang("Konsumsi Tumpukan Maks", "Max Consumed Stacks"),
											val: [switch_lang("0", "0"), switch_lang("1", "1"), switch_lang("2", "2"), switch_lang("3", "3"), switch_lang("4", "4"), switch_lang("5", "5")],
											tooltip_text: switch_lang("Jumlah maksimum tumpukan Multiplex Arrowhead yang bisa dikonsumsi saat menggunakan skill ini.\nPenyesuaian: 0, 1, 2, 3, 4, atau 5.", 
																	  "The maximum number of Multiplex Arrowhead stacks that can be consumed when using this skill.\nAdjustment: 0, 1, 2, 3, 4, or 5."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 4)
										}
									}; 
								break;
							}
						break;
						case 3:			// Medic
							switch (skill_num) {
								case 1:			// Medical Kit
									result = {
										num1: {
											text: switch_lang("Pengali Penyembuhan", "Healing Multiplier"),
											val: [switch_lang("0.4x", "0.4x"), switch_lang("0.7x", "0.7x"), switch_lang("1x", "1x"), switch_lang("1.2x", "1.2x"), switch_lang("1.4x", "1.4x")],
											tooltip_text: switch_lang("Jumlah pengali untuk penyembuhan pada skill ini. Nilai yang dikalikan akan sesuai dengan nilai pada bagian detail. Penyesuaian ini berbanding lurus dengan penyesuaian Pengali Cooldown.\nPenyesuaian: 0.4x, 0.7x, 1x, 1.2x, atau 1.4x.", 
																	  "The number of multipliers for this skill's healing amount. The multiplied value will match the value in the details section. This adjustment is directly proportional to the Cooldown Multiplier.\nAdjustment: 0.4x, 0.7x, 1x, 1.2x, or 1.4x."),
											bound: {adj_num: 2, linear: 1, letter: "A"},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 3),
											real_val: [0.4, 0.7, 1, 1.2, 1.4]
										},
										num2: {
											text: switch_lang("Pengali Cooldown", "Cooldown Multiplier"),
											val: [switch_lang("0.6x", "0.6x"), switch_lang("0.8x", "0.8x"), switch_lang("1x", "1x"), switch_lang("1.3x", "1.3x"), switch_lang("1.6x", "1.6x")],
											tooltip_text: switch_lang("Jumlah pengali untuk cooldown pada skill ini. Nilai yang dikalikan akan sesuai dengan nilai pada bagian detail. Penyesuaian ini berbanding lurus dengan penyesuaian Pengali Penyembuhan.\nPenyesuaian: 0.6x, 0.8x, 1x, 1.3x, atau 1.6x.", 
																	  "The number of multipliers for this skill's cooldown. The multiplied value will match the value in the details section. This adjustment is directly proportional to the Healing Multiplier.\nAdjustment: 0.6x, 0.8x, 1x, 1.3x, or 1.6x."),
											bound: {adj_num: 1, linear: 1, letter: "A"},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj2", 3),
											real_val: [0.6, 0.8, 1, 1.3, 1.6]
										}
									}; 
								break;
								case 2:			// Corrosive Concentration
									result = {
										num1: {
											text: switch_lang("Kekuatan Lemparan", "Throwing Power"),
											val: [switch_lang("Lemah", "Weak"), switch_lang("Sedang", "Medium"), switch_lang("Kuat", "Strong")],
											tooltip_text: switch_lang("Kekuatan pengguna dalam melempar objek yang ada dalam skill ini.\nPenyesuaian: Lemah, Sedang, atau Kuat.", 
																	  "The user's power to throw objects in this skill.\nAdjustment: Weak, Medium, or Strong."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 2)
										}
									}; 
								break;
								case 3:			// CBC Booster
									result = {
										num1: {
											text: switch_lang("Jenis Buff", "Buff Type"),
											val: [switch_lang("Max HP (15%)", "Max HP (15%)"), switch_lang("Attack (15%)", "Attack (15%)"), switch_lang("Defense (15%)", "Defense (15%)"), switch_lang("Agility (25%)", "Agility (25%)"), switch_lang("Damage Output (15%)", "Damage Output (15%)"), switch_lang("Ignore Interruption (15%)", "Ignore Interruption (15%)"), 
												  switch_lang("Cooldown Reduction (10%)", "Cooldown Reduction (10%)"), switch_lang("Critical Buildup (15%)", "Critical Buildup (15%)"), switch_lang("Critical Damage (20%)", "Critical Damage (20%)"), switch_lang("Critical Protection (10%)", "Critical Protection (10%)"), switch_lang("Healing Output (15%)", "Healing Output (15%)"), switch_lang("Attack Speed (20%)", "Attack Speed (20%)"), 
												  switch_lang("Armor Damage (25%)", "Armor Damage (25%)"), switch_lang("Armor Strength (15%)", "Armor Strength (15%)"), switch_lang("Damage Resistance (20%)", "Damage Resistance (20%)"), switch_lang("CC Power (15%)", "CC Power (15%)"), switch_lang("Buff Power (15%)", "Buff Power (15%)"), switch_lang("Charge Speed (20%)", "Charge Speed (20%)")],
											tooltip_text: switch_lang("Jenis buff yang akan didapatkan pengguna beserta jumlah efektivitasnya.\n\nPenyesuaian: \nMax HP (15%), Attack (15%), Defense (15%), Agility (25%), Damage Output (15%), Ignore Interruption (15%), Cooldown Reduction (10%), Critical Buildup (15%), Critical Damage (20%), Critical Protection (10%), Healing Output (15%), Attack Speed (20%), Armor Damage (25%), Armor Strength (15%), Damage Resistance (20%), CC Power (15%), Buff Power (15%), atau Charge Speed (20%).",
																	  "The type of buff the user will get and the effectiveness amount.\n\nAdjustment: \nMax HP (15%), Attack (15%), Defense (15%), Agility (25%), Damage Output (15%), Ignore Interruption (15%), Cooldown Reduction (10%), Critical Buildup (15%), Critical Damage (20%), Critical Protection (10%), Healing Output (15%), Attack Speed (20%), Armor Damage (25%), Armor Strength (15%), Damage Resistance (20%), CC Power (15%), Buff Power (15%), or Charge Speed (20%)."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 1),
											real_val: [15, 15, 15, 25, 15, 15, 10, 15, 20, 10, 15, 20, 25, 15, 20, 15, 15, 20]
										}
									}; 

									if (skill_data.talent.s20) {
										result.num2 = {
											text: switch_lang("Overboost", "Overboost"),
											val: [switch_lang("Tidak Aktif", "Inactive"), switch_lang("Tingkat 1", "Level 1"), switch_lang("Tingkat 2", "Level 2"), switch_lang("Tingkat 3", "Level 3")],
											tooltip_text: switch_lang("Tingkat Overboost dari skill ini. Setiap tingkat akan menambah efektivitas buff sebesar 0.25x dan mengurangi HP pengguna sebesar 1% dari HP maksimumnya.\nPenyesuaian: Tidak Aktif, Tingkat 1, Tingkat 2, atau Tingkat 3.",
																	  "Overboost level of this skill. Each level will increase the buff's effectiveness by 0.25x and reduce the user's HP by 1% of its maximum HP.\nAdjustment: Inactive, Level 1, Level 2, or Level 3."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj2", 1)
										};
									}
								break;
							}
						break;
					}
				break;
				case 6:		// UNIQ
					switch (class_num) {
						case 1:			// Warrior
							result = {
								num1: {
									text: switch_lang("Aktivasi Otomatis", "Auto Activation"),
									val: [switch_lang("Tidak", "No"), switch_lang("Ya", "Yes")],
									tooltip_text: switch_lang("Mengaktifkan skill secara otomatis ketika poin Form sudah mencapai batas maksimum.\nPenyesuaian: Tidak atau Ya.", 
															  "Activate skills automatically when Form points have reached the cap.\nAdjustment: No or Yes."),
									bound: {adj_num: 0, linear: 0, letter: ""},
									adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 1)
								}
							}; 
						break;
						case 3:			// Medic
							result = {
								num1: {
									text: switch_lang("Aktivasi Otomatis", "Auto Activation"),
									val: [switch_lang("Tidak", "No"), switch_lang("Ya", "Yes")],
									tooltip_text: switch_lang("Mengaktifkan skill secara otomatis ketika poin Rejuvenating Potion sudah mencapai ambang batas pada penyesuaian Ambang Batas Aktivasi.\nPenyesuaian: Tidak atau Ya.", 
															  "Activate skills automatically when Rejuvenation Potion points have reached the threshold on the Activation Threshold adjustment.\nAdjustment: No or Yes."),
									bound: {adj_num: 0, linear: 0, letter: ""},
									adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 1)
								},
								num2: {
									text: switch_lang("Ambang Batas Aktivasi", "Activation Threshold"),
									val: ["100", "110", "120", "130", "140", "150", "160", "170", "180", "190", "200", "210", "220", "230", "240", "250", "260", "270", "280", "290", "300"],
									tooltip_text: switch_lang("Jumlah poin Rejuvenating Potion minimum agar skill bisa diaktifkan secara otomatis. Hanya efektif kalau penyesuaian Aktivasi Otomatis dipilih ke opsi \"Ya.\"\nPenyesuaian: 100 sampai 300.", 
															  "Minimum amount of Rejuvenating Potion points for the skill to be activated automatically. Only effective when the Auto Activation adjustment is selected to the \"Yes\" option.\nAdjustment: 100 to 300."),
									bound: {adj_num: 0, linear: 0, letter: ""},
									adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj2", 11), 
									real_val: [100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200, 210, 220, 230, 240, 250, 260, 270, 280, 290, 300]
								}
							}; 
						break;
					}
				break;
				case 7:		// EXT
					switch (class_num) {
						case 1:			// Warrior
							switch (skill_num) {
								case 1:			// Wavecleaver
								case 2:			// Doom Drop
								case 5:			// Starfall Strike
									result = {
										num1: {
											text: switch_lang("Jenis Penggunaan", "Type of Use"),
											val: [switch_lang("Manual", "Manual"), switch_lang("Otomatis", "Automatic")],
											tooltip_text: switch_lang("Jenis penggunaan skill ini saat skill ini tersedia untuk digunakan.\n\nPenyesuaian: Manual: Harus menekan tombol tertentu dalam waktu terbatas untuk menggunakan skill ini.\nOtomatis: Skill ini akan segera terpicu tanpa harus menekan tombol tertentu.", 
																	  "The type of skill usage when this skill is available for use.\n\nAdjustment:\nManual: Need to press a certain button within a limited time to use this skill.\nAutomatic: This skill will be triggered immediately without the need to press a specific button."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 1)
										}
									};
								break;
							}
						break;
						case 2:			// Archer
							switch (skill_num) {
								case 1:			// Twin Talon Shot
								case 2:			// Soulsnare Shot
								case 5:			// Predator's Trilogy
									result = {
										num1: {
											text: switch_lang("Jenis Penggunaan", "Type of Use"),
											val: [switch_lang("Manual", "Manual"), switch_lang("Otomatis", "Automatic")],
											tooltip_text: switch_lang("Jenis penggunaan skill ini saat skill ini tersedia untuk digunakan.\n\nPenyesuaian: Manual: Harus menekan tombol tertentu dalam waktu terbatas untuk menggunakan skill ini.\nOtomatis: Skill ini akan segera terpicu tanpa harus menekan tombol tertentu.", 
																	  "The type of skill usage when this skill is available for use.\n\nAdjustment:\nManual: Need to press a certain button within a limited time to use this skill.\nAutomatic: This skill will be triggered immediately without the need to press a specific button."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 1)
										}
									};
								break;
							}
						break;
						case 3:			// Medic
							switch (skill_num) {
								case 1:			// Stasis Flask/Bolt
								case 2:			// Abyss Flask
								case 5:			// Caustic Drip
									result = {
										num1: {
											text: switch_lang("Jenis Penggunaan", "Type of Use"),
											val: [switch_lang("Manual", "Manual"), switch_lang("Otomatis", "Automatic")],
											tooltip_text: switch_lang("Jenis penggunaan skill ini saat skill ini tersedia untuk digunakan.\n\nPenyesuaian: Manual: Harus menekan tombol tertentu dalam waktu terbatas untuk menggunakan skill ini.\nOtomatis: Skill ini akan segera terpicu tanpa harus menekan tombol tertentu.", 
																	  "The type of skill usage when this skill is available for use.\n\nAdjustment:\nManual: Need to press a certain button within a limited time to use this skill.\nAutomatic: This skill will be triggered immediately without the need to press a specific button."),
											bound: {adj_num: 0, linear: 0, letter: ""},
											adj_val: ini_read_real(sect_name[type], "type" + string(skill_num) + "_adj1", 1)
										}
									};
								break;
							}
						break;
					}
				break;
			}
			
			file_text_encrypt(string_lower(bots_data.name) + ".txt");
			delete bots_data;
	}

	delete skill_data;
	return result;
}

function db_get_batk_seq(db_bots, db_batk, class_num, batkseq_type, set) {
	// class_num dimulai dari 1
	// batkseq_type = 1-normal, 2-alt, 3-airborne
	// name = "set{x}_num{y}", x = set_num, y = seq_num
	var result = {};
	var seq_type_name = ["seq_norm", "seq_alt", "seq_air"];
	var unlock_level = [];
	var max_atk = 0;
	var bots_data = db_get_row(db_bots, 1, class_num);

	switch (batkseq_type) {
		case 1:
		case 2:
			switch (class_num) {
				case 1: unlock_level = [1, 1, 1, 5, 10, 15, 20, 25]; break;
				case 2: unlock_level = [1, 1, 1, 5, 10, 17, 25]; break;
				case 3: unlock_level = [1, 1, 1, 5, 10, 20]; break;
			}
			break;
		case 3:
			switch (class_num) {
				case 1: unlock_level = [1, 5, 15, 25]; break;
				case 2: unlock_level = [5, 10, 25]; break;
				case 3: unlock_level = []; break;
			}
			break;
	}
	max_atk = array_length(unlock_level);

	file_text_decrypt(string_lower(bots_data.name) + ".txt");
	for (var i = 0; i < max_atk; i++) {		// BATK Seq Type
		var current_type = 0;
		switch (batkseq_type) {
			case 1:			// Normal
				switch (i) {
					case 0: current_type = ini_read_real(seq_type_name[batkseq_type-1], "set" + string(set) + "_num" + string(i+1), 1); break;
					case 1: current_type = ini_read_real(seq_type_name[batkseq_type-1], "set" + string(set) + "_num" + string(i+1), 1); break;
					case 2: current_type = ini_read_real(seq_type_name[batkseq_type-1], "set" + string(set) + "_num" + string(i+1), 2); break;
					default: current_type = ini_read_real(seq_type_name[batkseq_type-1], "set" + string(set) + "_num" + string(i+1), 0);
				}
				break;
			case 2:			// Alternative
			case 3:			// Airborne
				current_type = ini_read_real(seq_type_name[batkseq_type-1], "set" + string(set) + "_num" + string(i+1), 0);
				break;
		}

		var current_adj = {};
		if (current_type > 0) {
			for (var j = 0; j < db_get_value(db_batk, 14, current_type); j++) {
				struct_set(current_adj, "num" + string(j+1), ini_read_real(seq_type_name[batkseq_type-1], "set" + string(set) + "_num" + string(i+1) + "_adj" + string(j+1), 1));
			}
		}
		
		struct_set(result, "num" + string(i+1), {seq_type: batkseq_type, set_num: set, type: current_type, adj: current_adj, unlock_lv: unlock_level[i]});
	}
	file_text_encrypt(string_lower(bots_data.name) + ".txt");
	
	var chain_cnt = 0;
	var chain = 0;
	for (var i = 0; i < max_atk; i++) {		// BATK Seq Chain
		var current_type = result[$ "num"+string(i+1)].type;
		var next_type = 0;
		var prev_type = 0;
		if (i+1 < max_atk) {
			next_type = result[$ "num"+string(i+2)].type;
		}
		if (i > 0) {
			prev_type = result[$ "num"+string(i)].type;
		}
		
		if (current_type > 0) {
			var skill_data = db_get_row(db_batk, 6, current_type);
			if (i == 0) {
				if (skill_data.chain.valid) {
					var next_skill_data = {};
					if (next_type > 0) {
						next_skill_data = db_get_row(db_batk, 6, next_type);
						if (next_skill_data.chain.valid) {
							chain = 1;
						} else {
							chain = 0;
						}
					} else {
						chain = 0
					}
					delete next_skill_data;
				}
			} else {
				if (skill_data.chain.valid) {
					var prev_skill_data = db_get_row(db_batk, 6, prev_type);
					var next_skill_data = {};
					if (next_type > 0) {
						next_skill_data = db_get_row(db_batk, 6, next_type);
					}
				
					if (prev_skill_data.chain.valid) {
						if (skill_data.chain.val <= -1) {
							if (current_type == prev_type) {
								chain = clamp(chain_cnt + 1, 1, max_atk);
							} else {
								chain = clamp(chain_cnt + skill_data.chain.val, 1, max_atk);
							}
						} else {
							chain = clamp(chain_cnt + skill_data.chain.val, 1, max_atk);
						}
					} else if (array_length(struct_get_names(next_skill_data)) > 0) {
						if (next_skill_data.chain.valid) {
							chain = 1;
						} else {
							chain = 0;
						}
					}
				
					delete prev_skill_data;
					delete next_skill_data;
				} else {
					chain = 0;
				}
			}
		} else {
			chain = 0;
		}
		
		chain_cnt = chain;
		result[$ "num"+string(i+1)].chain_num = chain;
		delete skill_data;
	}
	
	delete bots_data;
	return result;
}

