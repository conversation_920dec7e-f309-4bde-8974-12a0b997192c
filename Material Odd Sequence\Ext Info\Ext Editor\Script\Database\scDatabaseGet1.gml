function db_get_value(db, column_num, row_num) {
	// column_num & row_num mulai dari 1
	var result = ds_grid_get(db, column_num - 1, row_num - 1);
	return result;
}

function db_get_row(db, source, row) {
	// source = 1-bots, 2-weapon, 3-equipment, 4-items, 5-talents, 6-skill_list
	//          7-skill_dtl, 8-setting, 9-lab, 10-stage, 11-stage_list
	// row mulai dari 1
	var result = {};
	var col_name = [];
	
	switch (source) {
		case 1:			// DB Bots
			col_name = ["num", "name", "unlocked", "level", "xp", "weapon", "core", "bearing", "crust", "sprite", "sprite_w", "derv", "spmv", "spmv_mod", "ulti", "batk_set", "alt_batk_set", "air_batk_set"];
			break;
		case 2:			// DB Weapons
			col_name = ["class_num", "num", "name", "unlocked", "rarity", "level", "trait1_type", "trait1_tier", "trait2_type", "trait2_tier", "variant", "cost_num", "cost", "batk_limit"];
			break;
		case 3:			// DB Equipments
			col_name = ["type", "class_num", "num", "code", "name", "unlocked", "rarity", "level", "trait1_type", "trait1_tier", "trait2_type", "trait2_tier", "cost_num", "cost"];
			break;
		case 4:			// DB Items
			result = {
				num : ds_grid_get(db, 0, row - 1),
				type : ds_grid_get(db, 1, row - 1),
				name_file : ds_grid_get(db, 2, row - 1),
				sprite : ds_grid_get(db, 3, row - 1),
				frame : ds_grid_get(db, 4, row - 1),
				grade : ds_grid_get(db, 5, row - 1),
				count : ds_grid_get(db, 6, row - 1),
				name : switch_lang(ds_grid_get(db, 9, row - 1), ds_grid_get(db, 7, row - 1)),
				desc : switch_lang(ds_grid_get(db, 10, row - 1), ds_grid_get(db, 8, row - 1)),
				link_num : ds_grid_get(db, 11, row - 1), 
				attribute : ds_grid_get(db, 12, row - 1),
				spr_item : ds_grid_get(db, 13, row - 1)
			}
			break;
		case 5:			// DB Talents
			col_name = ["num", "name", "unlocked", "sprite", "frame", "type", "title", "desc", "active_req", "cost"];
			break;
		case 6:			// DB Skill List
			col_name = ["num", "sprite", "title", "desc", "tag", "unlocked", "level", "unlock_lv", "talent", "atk_cost", "eff_level", "type", "upgraded", "adj_count", "chain", "sk_mod", "str_name"];
			break;
		case 7:			// DB Skill Details
			col_name = ["num", "skill_num", "dtl_type", "text", "val", "attr", "opt", "val_mod"];
			break;
		case 8:			// DB Setting
			col_name = ["num", "type", "name", "title", "desc", "val", "change_type"];
			break;
		case 9:			// DB Lab
			col_name = ["num", "type", "item_num", "class_num", "list", "out_val"];
			break;
		case 10:		// DB Stage
			col_name = ["num", "type", "name", "title", "desc", "unlocked", "bg", "entry"];
			break;
		case 11:		// DB Stage List
			col_name = ["num", "stage_num", "group", "type", "name", "title", "desc", "icon", "lost", "star", "clear", "diff", "reward", "detail", "bg", "bots", "tex", "tg_room", "parallax"];
			break;
	}
	
	if (!struct_exists(result, "num")) {
		for (var i = 0; i < array_length(col_name); i++) {
			result[$ col_name[i]] = ds_grid_get(db, i, row - 1);
		}
	}
	
	return result;
	
}

function db_get_stats_val(bots_db, target_data, class_num, source) {
	// class_num mulai dari 1
	// source = 1-bots, 2-weapon, 3-core, 4-bearing, 5-crust
	// target_data = struct dari database ~db_get_row()
	var result = {};
	var trait_cnt_list = [20, 9, 9, 9];		// Ambil dari DB trait
	switch (source) {
		case 1:		// Bots
			var all_stats = new bots_stats();
			
			var db_wp = db_create_weapon(bots_db, class_num);
			var db_core = db_create_equipment(bots_db, class_num, 1);
			var db_bearing = db_create_equipment(bots_db, class_num, 2);
			var db_crust = db_create_equipment(bots_db, class_num, 3);
			var db_talents = db_create_talent(bots_db, class_num);
			
			var wp_data = db_get_row(db_wp, 2, target_data.weapon);
			var wp_stat = db_get_stats_val(bots_db, wp_data, class_num, 2);
			var core_data = db_get_row(db_core, 3, target_data.core);	
			var core_stat = db_get_stats_val(bots_db, core_data, class_num, 3);
			var bearing_data = db_get_row(db_bearing, 3, target_data.bearing);	
			var bearing_stat = db_get_stats_val(bots_db, bearing_data, class_num, 4);
			var crust_data = db_get_row(db_crust, 3, target_data.crust);	
			var crust_stat = db_get_stats_val(bots_db, crust_data, class_num, 5);
			
			var base_stat_flat = ["", "hp_flat", "atk_flat", "def_flat"];
			var base_stat_scale = ["", "hp_scale", "atk_scale", "def_scale"];
			var class_stat = [wp_stat.stat1, wp_stat.stat2, wp_stat.stat3, core_stat.stat1, core_stat.stat2, core_stat.stat3, core_stat.effect1,
							  bearing_stat.stat1, bearing_stat.stat2, bearing_stat.stat3, bearing_stat.effect1, crust_stat.stat1, crust_stat.stat2, crust_stat.stat3, crust_stat.effect1];
			var eq_set = 0;
			var set_cnt = (target_data.core == target_data.bearing) + (target_data.core == target_data.crust) + (target_data.bearing == target_data.crust);
			if (set_cnt >= 2) {
				if (target_data.core == target_data.bearing) {
					eq_set = target_data.core;
					array_push(class_stat, core_stat.effect3);
				} else if (target_data.core == target_data.crust) {
					eq_set = target_data.core;
					array_push(class_stat, core_stat.effect3);
				} else if (target_data.bearing == target_data.crust) {
					eq_set = target_data.bearing;
					array_push(class_stat, bearing_stat.effect3);
				}
			}
			var stat_names = struct_get_names(all_stats);
			for (var i = 0; i < array_length(class_stat); i++) {
				if (class_stat[i].num > 0) {
					for (var j = 0; j < array_length(stat_names); j++) {
						var key = stat_names[j];
						if (class_stat[i].num == all_stats[$ key].num) {
							if (struct_exists(class_stat[i], "type")) {
								switch (class_stat[i].type) {
									case "flat": all_stats[$ base_stat_flat[class_stat[i].num]].val += class_stat[i].val; break;
									default: all_stats[$ base_stat_scale[class_stat[i].num]].val += class_stat[i].val;
								}
							} else {
								all_stats[$ key].val += class_stat[i].val;
							}
							break;
						}
					}
				}
			}
			
			var trait_stat = [wp_stat.trait1, wp_stat.trait2, core_stat.trait1, core_stat.trait2,
							  bearing_stat.trait1, bearing_stat.trait2, crust_stat.trait1, crust_stat.trait2];
			for (var i = 0; i < array_length(trait_stat); i++) {
				if (trait_stat[i].num > 0) {
					for (var j = 0; j < array_length(stat_names); j++) {
						var key = stat_names[j];
						if (struct_exists(trait_stat[i], "stat_num")) {
							if (trait_stat[i].stat_num == all_stats[$ key].num) {
								if (trait_stat[i].stat_num >= 1 && trait_stat[i].stat_num <= 3) {
									all_stats[$ base_stat_scale[trait_stat[i].stat_num]].val += trait_stat[i].val;
								} else {
									all_stats[$ key].val += trait_stat[i].val;
								}
								break;
							}
						} else {
							break;
						}
					}
				}
			}
			
			result = all_stats;
			var base_stats = ["hp", "atk", "def"];
			var sp_stats = [];
			switch (class_num) {
				case 1:
					sp_stats = ["melee_do", "dmg_reduction"];
					all_stats.melee_do.val += round(floor(target_data.level/10) * 1.5);
					all_stats.dmg_reduction.val += floor(target_data.level/10) * 3;
					all_stats.crit_buildup.val += 5;
					all_stats.hp_scale.val += db_get_value(db_talents, 3, 1) * 10;
					all_stats.crit_damage.val += db_get_value(db_talents, 3, 2) * 20;
					all_stats.dmg_reduction.val += db_get_value(db_talents, 3, 3) * 10;
					all_stats.cc_power.val += db_get_value(db_talents, 3, 4) * 15;
					
					all_stats.hp = {num: 1, type: "flat", val: round((((all_stats.hp_scale.val)/100) * (1000 + (target_data.level*100) + (floor(target_data.level/5)*6)*(target_data.level*10))) + all_stats.hp_flat.val)};
					all_stats.atk = {num: 2, type: "flat", val: round(((all_stats.atk_scale.val)/100) * (90 + (target_data.level*5) + (floor(target_data.level/5)*5)*target_data.level) + all_stats.atk_flat.val)};
					all_stats.def = {num: 3, type: "flat", val: round(((all_stats.def_scale.val)/100) * (130 + (target_data.level*9) + (floor(target_data.level/5)*6)*target_data.level) + all_stats.def_flat.val)};
				break;
				case 2:
					sp_stats = ["ranged_do", "batk_do"];
					all_stats.ranged_do.val += round(floor(target_data.level/10) * 1.5);
					all_stats.batk_do.val += floor(target_data.level/10) * 3;
					all_stats.crit_buildup.val += 3;
					all_stats.accuracy.val += 50;
					all_stats.recoil_reduction.val += 50;
					all_stats.def_scale.val += db_get_value(db_talents, 3, 1) * 15;
					all_stats.crit_buildup.val += db_get_value(db_talents, 3, 2) * 10;
					all_stats.ap.val += db_get_value(db_talents, 3, 3) * 15;
					
					all_stats.hp = {num: 1, type: "flat", val: round((((all_stats.hp_scale.val)/100) * (800 + (target_data.level*100) + (floor(target_data.level/5)*4.3)*(target_data.level*10))) + all_stats.hp_flat.val)};
					all_stats.atk = {num: 2, type: "flat", val: round(((all_stats.atk_scale.val)/100) * (110 + (target_data.level*4) + (floor(target_data.level/5)*3)*target_data.level) + all_stats.atk_flat.val)};
					all_stats.def = {num: 3, type: "flat", val: round(((all_stats.def_scale.val)/100) * (90 + (target_data.level*6) + (floor(target_data.level/5)*5)*target_data.level) + all_stats.def_flat.val)};
				break;
				case 3:
					sp_stats = ["healing_output", "ulti_power"];
					all_stats.healing_output.val += round(floor(target_data.level/10) * 1.5);
					all_stats.ulti_power.val += floor(target_data.level/10) * 4;
					all_stats.crit_buildup.val += 5;
					all_stats.def_scale.val += db_get_value(db_talents, 3, 1) * 10;
					all_stats.crit_protection.val += db_get_value(db_talents, 3, 2) * 15;
					all_stats.acid_do.val += db_get_value(db_talents, 3, 3) * 10;
					all_stats.buff_power.val += db_get_value(db_talents, 3, 4) * 10;
					
					all_stats.hp = {num: 1, type: "flat", val: round((((all_stats.hp_scale.val)/100) * (900 + (target_data.level*90) + (floor(target_data.level/5)*6.8)*(target_data.level*10))) + all_stats.hp_flat.val)};
					all_stats.atk = {num: 2, type: "flat", val: round(((all_stats.atk_scale.val)/100) * (70 + (target_data.level*5) + (floor(target_data.level/5)*2.82)*target_data.level) + all_stats.atk_flat.val)};
					all_stats.def = {num: 3, type: "flat", val: round(((all_stats.def_scale.val)/100) * (120 + (target_data.level*8) + (floor(target_data.level/5)*5.8)*target_data.level) + all_stats.def_flat.val)};
				break;
				
			}
			struct_set(result, "main_stats", array_concat(base_stats, sp_stats));
			
			#region		// Max Default Stats
			all_stats.agility.val = clamp(all_stats.agility.val, 0, 175);
			all_stats.dmg_output.val = clamp(all_stats.dmg_output.val, 0, 200);
			all_stats.ignore_interruption.val = clamp(all_stats.ignore_interruption.val, 0, 55);
			all_stats.cd_reduction.val = clamp(all_stats.cd_reduction.val, 0, 30);
			all_stats.crit_buildup.val = clamp(all_stats.crit_buildup.val, 0, 60);
			all_stats.crit_damage.val = clamp(all_stats.crit_damage.val, 0, 250);
			all_stats.crit_protection.val = clamp(all_stats.crit_protection.val, 0, 70);
			all_stats.healing_output.val = clamp(all_stats.healing_output.val, 0, 150 + (class_num == 3)*50);
			all_stats.atk_spd.val = clamp(all_stats.atk_spd.val, 0, 135);
			all_stats.melee_do.val = clamp(all_stats.melee_do.val, 0, 50 + (class_num == 1)*15);
			all_stats.ranged_do.val = clamp(all_stats.ranged_do.val, 0, 50 + (class_num == 2)*15);
			all_stats.aoe_dmg_scale.val = clamp(all_stats.aoe_dmg_scale.val, 0, 80);
			all_stats.physical_dmg_bonus.val = clamp(all_stats.physical_dmg_bonus.val, 0, 150);
			all_stats.physical_do.val = clamp(all_stats.physical_do.val, 0, 50);
			all_stats.dmg_reduction.val = clamp(all_stats.dmg_reduction.val, 0, 40);
			all_stats.acidity_bonus.val = clamp(all_stats.acidity_bonus.val, 0, 150);
			all_stats.acid_do.val = clamp(all_stats.acid_do.val, 0, 50);
			all_stats.tc.val = clamp(all_stats.tc.val, 0, 40);
			all_stats.def_penetration.val = clamp(all_stats.def_penetration.val, 0, 30);
			all_stats.ap.val = clamp(all_stats.ap.val, 0, 80);
			all_stats.ab.val = clamp(all_stats.ab.val, 0, 200);
			all_stats.armor_burst.val = clamp(all_stats.armor_burst.val, 0, 350);
			all_stats.armor_str.val = clamp(all_stats.armor_str.val, 0, 150);
			all_stats.dmg_res.val = clamp(all_stats.dmg_res.val, 0, 80);
			all_stats.cc_power.val = clamp(all_stats.cc_power.val, 0, 200);
			all_stats.cc_res.val = clamp(all_stats.cc_res.val, 0, 70);
			all_stats.buff_power.val = clamp(all_stats.buff_power.val, 0, 200);
			all_stats.debuff_res.val = clamp(all_stats.debuff_res.val, 0, 70);
			all_stats.charge_spd.val = clamp(all_stats.charge_spd.val, 0, 200);
			all_stats.batk_db.val = clamp(all_stats.batk_db.val, 0, 200);
			all_stats.batk_do.val = clamp(all_stats.batk_do.val, 0, 50);
			all_stats.batk_power.val = clamp(all_stats.batk_power.val, 0, 100);
			all_stats.batk_eff.val = clamp(all_stats.batk_eff.val, 0, 25);
			all_stats.derv_db.val = clamp(all_stats.derv_db.val, 0, 200);
			all_stats.derv_do.val = clamp(all_stats.derv_do.val, 0, 50);
			all_stats.derv_power.val = clamp(all_stats.derv_power.val, 0, 100);
			all_stats.derv_eff.val = clamp(all_stats.derv_eff.val, 0, 25);
			all_stats.spmv_db.val = clamp(all_stats.spmv_db.val, 0, 200);
			all_stats.spmv_do.val = clamp(all_stats.spmv_do.val, 0, 50);
			all_stats.spmv_power.val = clamp(all_stats.spmv_power.val, 0, 100);
			all_stats.spmv_eff.val = clamp(all_stats.spmv_eff.val, 0, 25);
			all_stats.ulti_db.val = clamp(all_stats.ulti_db.val, 0, 200);
			all_stats.ulti_do.val = clamp(all_stats.ulti_do.val, 0, 50);
			all_stats.ulti_power.val = clamp(all_stats.ulti_power.val, 0, 100);
			all_stats.ulti_eff.val = clamp(all_stats.ulti_eff.val, 0, 25);
			all_stats.intg_db.val = clamp(all_stats.intg_db.val, 0, 200);
			all_stats.intg_do.val = clamp(all_stats.intg_do.val, 0, 50);
			all_stats.intg_power.val = clamp(all_stats.intg_power.val, 0, 100);
			all_stats.intg_eff.val = clamp(all_stats.intg_eff.val, 0, 25);
			all_stats.accuracy.val = clamp(all_stats.accuracy.val, 0, 100);
			all_stats.recoil_reduction.val = clamp(all_stats.recoil_reduction.val, 0, 100);
			all_stats.ammo_cap.val = clamp(all_stats.ammo_cap.val, 0, 200);
			all_stats.mags_cap.val = clamp(all_stats.mags_cap.val, 0, 150);
			all_stats.reload_spd.val = clamp(all_stats.reload_spd.val, 0, 200);
			#endregion
			
			#region		// CP
			var cp = 0;
			var cp_survival = 0;
			cp_survival += (all_stats.hp.val/30000)*10000;
			cp_survival += (all_stats.def.val/2500)*10000;
			cp_survival += (all_stats.agility.val/70)*10000;
			cp_survival += (all_stats.ignore_interruption.val/30)*10000;
			cp_survival += (all_stats.crit_protection.val/40)*10000;
			cp_survival += (all_stats.dmg_reduction.val/30)*10000;
			cp_survival += (all_stats.tc.val/30)*10000;
			cp_survival += (all_stats.armor_str.val/80)*10000;
			cp_survival += (all_stats.dmg_res.val/35)*10000;
			cp_survival += (all_stats.cc_res.val/35)*10000;
			cp_survival += (all_stats.debuff_res.val/35)*10000;
			cp_survival = round(cp_survival);
			
			var cp_damage = 0;
			cp_damage += (all_stats.atk.val/3000)*10000;
			cp_damage += (all_stats.dmg_output.val/60)*10000;
			cp_damage += (all_stats.crit_buildup.val/30)*10000;
			cp_damage += (all_stats.crit_damage.val/75)*10000;
			cp_damage += (all_stats.atk_spd.val/60)*10000;
			cp_damage += (all_stats.melee_do.val/40)*10000;
			cp_damage += (all_stats.ranged_do.val/40)*10000;
			cp_damage += (all_stats.aoe_dmg_scale.val/50)*10000;
			cp_damage += (all_stats.physical_dmg_bonus.val/100)*10000;
			cp_damage += (all_stats.physical_do.val/40)*10000;
			cp_damage += (all_stats.acidity_bonus.val/100)*10000;
			cp_damage += (all_stats.acid_do.val/40)*10000;
			cp_damage += (all_stats.def_penetration.val/20)*10000;
			cp_damage += (all_stats.ap.val/40)*10000;
			cp_damage += (all_stats.ab.val/70)*10000;
			cp_damage += (all_stats.armor_burst.val/100)*10000;
			cp_damage += (all_stats.charge_spd.val/135)*10000;
			cp_damage += (all_stats.batk_db.val/100)*10000;
			cp_damage += (all_stats.batk_do.val/40)*10000;
			cp_damage += (all_stats.derv_db.val/100)*10000;
			cp_damage += (all_stats.derv_do.val/40)*10000;
			cp_damage += (all_stats.spmv_db.val/100)*10000;
			cp_damage += (all_stats.spmv_do.val/40)*10000;
			cp_damage += (all_stats.ulti_db.val/100)*10000;
			cp_damage += (all_stats.ulti_do.val/40)*10000;
			cp_damage += (all_stats.intg_db.val/100)*10000;
			cp_damage += (all_stats.intg_do.val/40)*10000;
			cp_damage = round(cp_damage);
			
			var cp_support = 0;
			cp_support += (all_stats.cd_reduction.val/25)*10000;
			cp_support += (all_stats.healing_output.val/70)*10000;
			cp_support += (all_stats.cc_power.val/70)*10000;
			cp_support += (all_stats.buff_power.val/70)*10000;
			cp_support += (all_stats.batk_power.val/60)*10000;
			cp_support += (all_stats.batk_eff.val/25)*10000;
			cp_support += (all_stats.derv_power.val/60)*10000;
			cp_support += (all_stats.derv_eff.val/25)*10000;
			cp_support += (all_stats.spmv_power.val/60)*10000;
			cp_support += (all_stats.spmv_eff.val/25)*10000;
			cp_support += (all_stats.ulti_power.val/60)*10000;
			cp_support += (all_stats.ulti_eff.val/25)*10000;
			cp_support += (all_stats.intg_power.val/60)*10000;
			cp_support += (all_stats.intg_eff.val/25)*10000;
			cp_support += (all_stats.accuracy.val/50)*10000;
			cp_support += (all_stats.recoil_reduction.val/40)*10000;
			cp_support += (all_stats.ammo_cap.val/100)*10000;
			cp_support += (all_stats.mags_cap.val/100)*10000;
			cp_support += (all_stats.reload_spd.val/100)*10000;
			cp_damage = round(cp_damage);
			
			cp = round(cp_survival + cp_damage + cp_support);
			#endregion
			struct_set(result, "cp", {total: cp, survival: cp_survival, damage: cp_damage, support: cp_support});
			
			delete all_stats;
			ds_grid_destroy(db_wp);
			ds_grid_destroy(db_core);
			ds_grid_destroy(db_bearing);
			ds_grid_destroy(db_crust);
			ds_grid_destroy(db_talents);
		break;
		case 2:		// Weapon
			switch (class_num) {
				case 1:		// Warrior
					switch (target_data.num) {
						case 1:
							result = {
								stat1 : {num: 2, type: "flat", val: round(50 + 200 * (target_data.level/50))},
								stat2 : {num: 0, val: 0},
								stat3 : {num: 0, val: 0},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Tidak ada peningkatan dari senjata ini.",
															 "No improvements from this weapon.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Tidak ada kemajuan dari senjata ini.",
															 "No advancements from this weapon.")},
								effect3 : {title: switch_lang("Efek Spesial", "Special Effects"),
										   desc: switch_lang("Tidak ada efek spesial dari senjata ini.",
															 "No special effects from this weapon.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						case 2:
							result = {
								stat1 : {num: 2, type: "flat", val: round(100 + 260 * (target_data.level/50))},
								stat2 : {num: 0, val: 0},
								stat3 : {num: 0, val: 0},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Meningkatkan Physical Damage Output untuk \"Slash\" sebanyak 10%.",
															 "Increases Physical Damage Output for \"Slash\" by 10%.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Tidak ada kemajuan dari senjata ini.",
															 "No advancements from this weapon.")},
								effect3 : {title: switch_lang("Efek Spesial", "Special Effects"),
										   desc: switch_lang("Tidak ada efek spesial dari senjata ini.",
															 "No special effects from this weapon.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						case 3:
							result = {
								stat1 : {num: 2, type: "flat", val: round(220 + 380 * (target_data.level/50))},
								stat2 : {num: 8, val: round(5 + 10 * floor(target_data.level/10) / 5)},
								stat3 : {num: 0, val: 0},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Meningkatkan Physical Damage Output untuk \"Blow\" dan \"Uppercut\" sebanyak 15%.",
															 "Increases Physical Damage Output for \"Blow\" and \"Uppercut\" by 15%.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Tidak ada kemajuan dari senjata ini.",
															 "No advancements from this weapon.")},
								effect3 : {title: switch_lang("Efek Spesial", "Special Effects"),
										   desc: switch_lang("Tidak ada efek spesial dari senjata ini.",
															 "No special effects from this weapon.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						case 4:
							result = {
								stat1 : {num: 2, type: "flat", val: round(400 + 500 * (target_data.level/50))},
								stat2 : {num: 9, val: round(10 + 25 * floor(target_data.level/5) / 10)},
								stat3 : {num: 13, val: round(5 + 10 * floor(target_data.level/10) / 5)},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Meningkatkan Physical Damage Bonus untuk \"Blow\", \"Double Slash\", dan \"Twin-Thrust\" sebanyak 50%.",
															 "Increases Physical Damage Bonus for \"Blow\", \"Double Slash\", and \"Twin-Thrust\" by 50%.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Menaikkan level aktif maksimal \"Blow\" ke 10.",
															 "Raise \"Blow\" maximum active level to 10.")},
								effect3 : {title: switch_lang("Efek Spesial", "Special Effects"),
										   desc: switch_lang("Tidak ada efek spesial dari senjata ini.",
															 "No special effects from this weapon.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						case 5:
							result = {
								stat1 : {num: 2, type: "flat", val: round(400 + 500 * (target_data.level/50))},
								stat2 : {num: 16, val: round(10 + 25 * floor(target_data.level/5) / 10)},
								stat3 : {num: 8, val: round(5 + 15 * floor(target_data.level/10) / 5)},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Meningkatkan Physical Damage Output untuk \"Blow\", \"Uppercut\", dan \"Slam\" sebanyak 20%.",
															 "Increases Physical Damage Output for \"Blow\", \"Uppercut\", and \"Slam\" by 20%.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Menaikkan level aktif maksimal \"Slam\" ke 10.",
															 "Raise \"Slam\" maximum active level to 10.")},
								effect3 : {title: switch_lang("Efek Spesial", "Special Effects"),
										   desc: switch_lang("Tidak ada efek spesial dari senjata ini.",
															 "No special effects from this weapon.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						case 6:
							result = {
								stat1 : {num: 2, type: "flat", val: round(600 + 1200 * (target_data.level/50))},
								stat2 : {num: 9, val: round(15 + 30 * (target_data.level/50))},
								stat3 : {num: 8, val: round(10 + 15 * floor(target_data.level/5) / 10)},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Meningkatkan Physical Damage Bonus untuk \"Blow\", \"Thrust\", \"Twin-Thrust\", dan \"Smash\" sebanyak 75%.",
															 "Increases Physical Damage Bonus for \"Blow\", \"Thrust\", \"Twin-Thrust\", and \"Smash\" by 75%.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Menaikkan level aktif maksimal \"Double Slash\" dan \"Smash\" ke 10. Menaikkan level aktif maksimal tipe Serangan Dasar lainnya ke 8.",
															 "Raise \"Double Slash\" and \"Smash\" maximum active level to 10. Raise other Basic Attack type maximum active level to 8.")},
								effect3 : {title: switch_lang("Reckoning", "Reckoning"),
										   desc: switch_lang("Memicu \"Perfect Parry\" akan meningkatkan poin Critical Buildup pengguna sebesar 50%.",
															 "Trigger a \"Perfect Parry\" will increases user's Critical Buildup points by 50%.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						
					}
				break;
				case 2:		// Archer
					switch (target_data.num) {
						case 1:
							result = {
								stat1 : {num: 2, type: "flat", val: round(100 + 400 * (target_data.level/50))},
								stat2 : {num: 0, val: 0},
								stat3 : {num: 0, val: 0},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Tidak ada peningkatan dari senjata ini.",
															 "No improvements from this weapon.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Tidak ada kemajuan dari senjata ini.",
															 "No advancements from this weapon.")},
								effect3 : {title: switch_lang("Efek Spesial", "Special Effects"),
										   desc: switch_lang("Tidak ada efek spesial dari senjata ini.",
															 "No special effects from this weapon.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						case 2:
							result = {
								stat1 : {num: 2, type: "flat", val: round(200 + 600 * (target_data.level/50))},
								stat2 : {num: 0, val: 0},
								stat3 : {num: 0, val: 0},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Meningkatkan Physical Damage Output untuk \"Ordinary Shot\" sebanyak 10%.",
															 "Increases Physical Damage Output for \"Ordinary Shot\" by 10%.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Tidak ada kemajuan dari senjata ini.",
															 "No advancements from this weapon.")},
								effect3 : {title: switch_lang("Efek Spesial", "Special Effects"),
										   desc: switch_lang("Tidak ada efek spesial dari senjata ini.",
															 "No special effects from this weapon.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						case 3:
							result = {
								stat1 : {num: 2, type: "flat", val: round(400 + 1100 * (target_data.level/50))},
								stat2 : {num: 5, val: round(5 + 5 * floor(target_data.level/10) / 5)},
								stat3 : {num: 0, val: 0},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Meningkatkan Physical Damage Bonus untuk \"Heavy Shot\" dan \"Parallel Shot\" sebanyak 35%.",
															 "Increases Physical Damage Bonus for \"Heavy Shot\" and \"Parallel Shot\" by 35%.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Tidak ada kemajuan dari senjata ini.",
															 "No advancements from this weapon.")},
								effect3 : {title: switch_lang("Efek Spesial", "Special Effects"),
										   desc: switch_lang("Tidak ada efek spesial dari senjata ini.",
															 "No special effects from this weapon.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						case 4:
							result = {
								stat1 : {num: 2, type: "flat", val: round(800 + 1700 * (target_data.level/50))},
								stat2 : {num: 9, val: round(10 + 25 * floor(target_data.level/5) / 10)},
								stat3 : {num: 14, val: round(5 + 10 * floor(target_data.level/10) / 5)},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Meningkatkan Physical Damage Bonus untuk \"Heavy Shot\", \"Multi Shot\", dan \"Double Shot\" sebanyak 50%.",
															 "Increases Physical Damage Bonus for \"Heavy Shot\", \"Multi Shot\", and \"Double Shot\" by 50%.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Menaikkan level aktif maksimal \"Heavy Shot\" ke 10.",
															 "Raise \"Heavy Shot\" maximum active level to 10.")},
								effect3 : {title: switch_lang("Efek Spesial", "Special Effects"),
										   desc: switch_lang("Tidak ada efek spesial dari senjata ini.",
															 "No special effects from this weapon.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						case 5:
							result = {
								stat1 : {num: 2, type: "flat", val: round(800 + 1700 * (target_data.level/50))},
								stat2 : {num: 19, val: round(10 + 25 * floor(target_data.level/5) / 10)},
								stat3 : {num: 8, val: round(5 + 15 * floor(target_data.level/10) / 5)},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Meningkatkan Physical Damage Output untuk \"Parallel Shot\", \"Acidic Shot\", dan \"Explosive Shot\" sebanyak 20%.",
															 "Increases Physical Damage Output for \"Parallel Shot\", \"Acidic Shot\", and \"Explosive Shot\" by 20%.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Menaikkan level aktif maksimal \"Acidic Shot\" ke 10.",
															 "Raise \"Acidic Shot\" maximum active level to 10.")},
								effect3 : {title: switch_lang("Efek Spesial", "Special Effects"),
										   desc: switch_lang("Tidak ada efek spesial dari senjata ini.",
															 "No special effects from this weapon.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						case 6:
							result = {
								stat1 : {num: 2, type: "flat", val: round(1500 + 2500 * (target_data.level/50))},
								stat2 : {num: 9, val: round(15 + 30 * (target_data.level/50))},
								stat3 : {num: 8, val: round(10 + 15 * floor(target_data.level/5) / 10)},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Meningkatkan Physical Damage Bonus untuk \"Ordinary Shot\", \"Heavy Shot\", \"Multi Shot\", dan \"Explosive Shot\" sebanyak 75%.",
															 "Increases Physical Damage Bonus for \"Ordinary Shot\", \"Heavy Shot\", \"Multi Shot\", and \"Explosive Shot\" by 75%.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Menaikkan level aktif maksimal \"Heavy Shot\" dan \"Double Shot\" ke 10. Menaikkan level aktif maksimal tipe Serangan Dasar lainnya ke 8.",
															 "Raise \"Heavy Shot\" and \"Double Shot\" maximum active level to 10. Raise other Basic Attack type maximum active level to 8.")},
								effect3 : {title: switch_lang("Proper Memory", "Proper Memory"),
										   desc: switch_lang("Meningkatkan Damage Scale untuk \"Follow-Up Shot\" sebesar 15%.",
															 "Increases Damage Scale for \"Follow-Up Shot\" by 15%.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						
					}
				break;
				case 3:		// Medic
					switch (target_data.num) {
						case 1:
							result = {
								stat1 : {num: 2, type: "flat", val: round(70 + 330 * (target_data.level/50))},
								stat2 : {num: 0, val: 0},
								stat3 : {num: 0, val: 0},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Tidak ada peningkatan dari senjata ini.",
															 "No improvements from this weapon.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Tidak ada kemajuan dari senjata ini.",
															 "No advancements from this weapon.")},
								effect3 : {title: switch_lang("Efek Spesial", "Special Effects"),
										   desc: switch_lang("Tidak ada efek spesial dari senjata ini.",
															 "No special effects from this weapon.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						case 2:
							result = {
								stat1 : {num: 2, type: "flat", val: round(120 + 480 * (target_data.level/50))},
								stat2 : {num: 0, val: 0},
								stat3 : {num: 0, val: 0},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Meningkatkan Physical Damage Output untuk \"Slash\" sebanyak 10%.",
															 "Increases Physical Damage Output for \"Slash\" by 10%.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Tidak ada kemajuan dari senjata ini.",
															 "No advancements from this weapon.")},
								effect3 : {title: switch_lang("Efek Spesial", "Special Effects"),
										   desc: switch_lang("Tidak ada efek spesial dari senjata ini.",
															 "No special effects from this weapon.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						case 3:
							result = {
								stat1 : {num: 2, type: "flat", val: round(250 + 650 * (target_data.level/50))},
								stat2 : {num: 21, val: round(5 + 5 * floor(target_data.level/10) / 5)},
								stat3 : {num: 0, val: 0},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Meningkatkan Physical Damage Output untuk \"Blow\" dan \"Uppercut\" sebanyak 15%.",
															 "Increases Physical Damage Output for \"Blow\" and \Uppercut\" by 15%.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Tidak ada kemajuan dari senjata ini.",
															 "No advancements from this weapon.")},
								effect3 : {title: switch_lang("Efek Spesial", "Special Effects"),
										   desc: switch_lang("Tidak ada efek spesial dari senjata ini.",
															 "No special effects from this weapon.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						case 4:
							result = {
								stat1 : {num: 2, type: "flat", val: round(500 + 1100 * (target_data.level/50))},
								stat2 : {num: 9, val: round(10 + 25 * floor(target_data.level/5) / 10)},
								stat3 : {num: 13, val: round(5 + 10 * floor(target_data.level/10) / 5)},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Meningkatkan Physical Damage Bonus untuk \"Slash\", \"Blow\", dan \"Stab\" sebanyak 50%.",
															 "Increases Physical Damage Bonus for \"Slash\", \"Blow\", and \"Stab\" by 50%.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Menaikkan level aktif maksimal \"Blow\" ke 10.",
															 "Raise \"Blow\" maximum active level to 10.")},
								effect3 : {title: switch_lang("Efek Spesial", "Special Effects"),
										   desc: switch_lang("Tidak ada efek spesial dari senjata ini.",
															 "No special effects from this weapon.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						case 5:
							result = {
								stat1 : {num: 2, type: "flat", val: round(500 + 1100 * (target_data.level/50))},
								stat2 : {num: 19, val: round(10 + 25 * floor(target_data.level/5) / 10)},
								stat3 : {num: 8, val: round(5 + 15 * floor(target_data.level/10) / 5)},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Meningkatkan Acid Damage Output untuk \"Blow\", \"Acidic Slash\", dan \"Spinning Slash\" sebanyak 20%.",
															 "Increases Acid Damage Output for \"Blow\", \"Acidic Slash\", and \"Spinning Slash\" by 20%.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Menaikkan level aktif maksimal \"Acidic Slash\" ke 10.",
															 "Raise \"Acidic Slash\" maximum active level to 10.")},
								effect3 : {title: switch_lang("Efek Spesial", "Special Effects"),
										   desc: switch_lang("Tidak ada efek spesial dari senjata ini.",
															 "No special effects from this weapon.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						case 6:
							result = {
								stat1 : {num: 2, type: "flat", val: round(1000 + 1500 * (target_data.level/50))},
								stat2 : {num: 9, val: round(15 + 30 * (target_data.level/50))},
								stat3 : {num: 8, val: round(10 + 15 * floor(target_data.level/5) / 10)},
								effect1 : {title: switch_lang("Peningkatan", "Improvements"),
										   desc: switch_lang("Meningkatkan Physical Damage Bonus untuk \"Slash\", \"Thrust\", \"Stab\", dan \"Drag Out\" sebanyak 75%.",
															 "Increases Physical Damage Bonus for \"Slash\", \"Thrust\", \"Stab\", and \"Drag Out\" by 75%.")},
								effect2 : {title: switch_lang("Kemajuan", "Advancements"),
										   desc: switch_lang("Menaikkan level aktif maksimal \"Blow\" dan \"Spinning Slash\" ke 10. Menaikkan level aktif maksimal tipe Serangan Dasar lainnya ke 8.",
															 "Raise \"Blow\" and \"Spinning Slash\" maximum active level to 10. Raise other Basic Attack type maximum active level to 8.")},
								effect3 : {title: switch_lang("Just A Small Gift", "Just A Small Gift"),
										   desc: switch_lang("Meninggalkan ramuan penyembuh kecil yang menyembuhkan 2% dari max HP pengambil selama 5 detik ketika beralih kelas.",
															 "Drop a small healing potion that heals 2% of the taker's max HP for 5 seconds when switched out.")},
								effect4 : {title: switch_lang("Efek Luar Biasa", "Overpowering Effects"),
										   desc: switch_lang("Tidak ada efek luar biasa dari senjata ini.",
															 "No overpowering effects from this weapon.")}
							}
							break;
						
					}
				break;
			}
			
			var db_trait = db_create_trait();
			if (target_data.rarity == 1 || target_data.rarity == 2) {
				result.trait1 = {num: -1, tier: 0, val: 0};
				result.trait2 = {num: -1, tier: 0, val: 0};
			} else if (target_data.rarity == 3 || target_data.rarity == 4) {
				result.trait1 = {num: target_data.trait1_type, tier: clamp(target_data.trait1_tier, 1, 5), val: 0};
				result.trait2 = {num: -1, tier: 0, val: 0};
				if (target_data.trait1_type > 0) {
					result.trait1.val = floor(db_get_value(db_trait, 3, target_data.trait1_type) + (db_get_value(db_trait, 4, target_data.trait1_type)/4*(clamp(target_data.trait1_tier, 1, 5)-1)));
					result.trait1.stat_num = db_get_value(db_trait, 2, target_data.trait1_type);
				}
			} else if (target_data.rarity == 5 || target_data.rarity == 6) {
				result.trait1 = {num: target_data.trait1_type, tier: clamp(target_data.trait1_tier, 1, 5), val: 0};
				result.trait2 = {num: target_data.trait2_type, tier: clamp(target_data.trait2_tier, 1, 5), val: 0};
				if (target_data.trait1_type > 0) {
					result.trait1.val = floor(db_get_value(db_trait, 3, target_data.trait1_type) + (db_get_value(db_trait, 4, target_data.trait1_type)/4*(clamp(target_data.trait1_tier, 1, 5)-1)));
					result.trait1.stat_num = db_get_value(db_trait, 2, target_data.trait1_type);
				}
				if (target_data.trait2_type > 0) {
					result.trait2.val = floor(db_get_value(db_trait, 3, target_data.trait2_type) + (db_get_value(db_trait, 4, target_data.trait2_type)/4*(clamp(target_data.trait2_tier, 1, 5)-1)));
					result.trait2.stat_num = db_get_value(db_trait, 2, target_data.trait2_type);
				}
			} 
			ds_grid_destroy(db_trait);
		break;
		case 3:		// Core
			switch (target_data.num) {
				case 1:
					result = {
						stat1 : {num: 2, type: "flat", val: round(35 + (target_data.level/50) * 115)},
						stat2 : {num: 0, val: 0},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Efek Pasif", "Passive Effect"),
								   desc: switch_lang("Tidak ada efek pasif dari core ini.",
													 "No passive effect from this core."),
								   num: 0, val: 0},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari core ini.",
													 "No special effect from this core.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 2:
					result = {
						stat1 : {num: 2, type: "flat", val: round(50 + (target_data.level/50) * 190)},
						stat2 : {num: 0, val: 0},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Flowing Strength I", "Flowing Strength I"),
								   desc: switch_lang("Meningkatkan ATK pengguna sebesar 5%.",
													 "Increases user's ATK by 5%."), 
								   num: 2, type: "percent", val: 5},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari core ini.",
													 "No special effect from this core.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 3:
					result = {
						stat1 : {num: 2, type: "flat", val: round(120 + (target_data.level/50) * 280)},
						stat2 : {num: 2, type: "percent", val: round(5 + 20 * floor(target_data.level/10) * 10 / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Flowing Strength II", "Flowing Strength II"),
								   desc: switch_lang("Meningkatkan ATK pengguna sebesar 10%.",
													 "Increases user's ATK by 10%."),
								   num: 2, type: "percent", val: 10},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari core ini.",
													 "No special effect from this core.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 4:
					result = {
						stat1 : {num: 2, type: "flat", val: round(120 + (target_data.level/50) * 280)},
						stat2 : {num: 6, val: round(2 + 20 * floor(target_data.level/10) * 8 / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Unshakeable I", "Unshakeable I"),
								   desc: switch_lang("Meningkatkan Ignore Interruption pengguna sebesar 5%.",
													 "Increases user's Ignore Interruption by 5%."),
								   num: 6, val: 5},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari core ini.",
													 "No special effect from this core.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 5:
					result = {
						stat1 : {num: 2, type: "flat", val: round(120 + (target_data.level/50) * 280)},
						stat2 : {num: 9, val: round(10 + 20 * floor(target_data.level/10) * 15 / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Frailty Destroyer I", "Frailty Destroyer I"),
								   desc: switch_lang("Meningkatkan Critical Damage pengguna sebesar 15%.",
													 "Increases user's Critical Damage by 15%."),
								   num: 9, val: 15},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari core ini.",
													 "No special effect from this core.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 6:
					result = {
						stat1 : {num: 2, type: "flat", val: round(120 + (target_data.level/50) * 280)},
						stat2 : {num: 23, val: round(7 + 20 * floor(target_data.level/10) * 13 / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Penetrator I", "Penetrator I"),
								   desc: switch_lang("Meningkatkan Armor Penetration pengguna sebesar 10%.",
													 "Increases user's Armor Penetration by 10%."),
								   num: 23, val: 10},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari core ini.",
													 "No special effect from this core.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 7:
					result = {
						stat1 : {num: 2, type: "flat", val: round(120 + (target_data.level/50) * 280)},
						stat2 : {num: 22, val: round(2 + 20 * floor(target_data.level/10) * 8 / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Breacher I", "Breacher I"),
								   desc: switch_lang("Meningkatkan Defense Penetration pengguna sebesar 5%.",
													 "Increases user's Defense Penetration by 5%."),
								   num: 22, val: 5},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari core ini.",
													 "No special effect from this core.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 8:
					result = {
						stat1 : {num: 2, type: "flat", val: round(120 + (target_data.level/50) * 280)},
						stat2 : {num: 11, val: round(5 + 20 * floor(target_data.level/10) * 10 / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Refreshing Healing I", "Refreshing Healing I"),
								   desc: switch_lang("Meningkatkan Healing Output pengguna sebesar 5%.",
													 "Increases user's Healing Output by 5%."),
								   num: 11, val: 5},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari core ini.",
													 "No special effect from this core.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 9:
					result = {
						stat1 : {num: 2, type: "flat", val: round(300 + 300 * (target_data.level/50))},
						stat2 : {num: 8, val: round(5 + 15 * floor(target_data.level/5) / 10)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Frailty Destroyer II", "Frailty Destroyer II"),
								   desc: switch_lang("Meningkatkan Critical Damage pengguna sebesar 25%.",
													 "Increases user's Critical Damage by 25%."),
								   num: 9, val: 25},
						effect2 : {title: switch_lang("Precise Strike", "Precise Strike"),
								   desc: switch_lang("Memberikan serangan kritis pada musuh akan meningkatkan ATK pengguna sebanyak 10% selama 5 detik.",
													 "Dealing a critical hit to enemies will increase user's ATK by 10% for 5 seconds.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Cooldown Reduction pengguna sebanyak 10%.",
													 "Increases user's Cooldown Reduction by 10%."),
								   num: 7, val: 10},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 10:
					result = {
						stat1 : {num: 2, type: "flat", val: round(300 + 300 * (target_data.level/50))},
						stat2 : {num: 9, val: round(15 + 20 * floor(target_data.level/5) / 10)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Speedy Tire II", "Speedy Tire II"),
								   desc: switch_lang("Meningkatkan Agility pengguna sebesar 15%.",
													 "Increases user's Agility by 15%."),
								   num: 4, val: 15},
						effect2 : {title: switch_lang("Potential Boost", "Potential Boost"),
								   desc: switch_lang("Memberikan serangan kritis pada musuh akan meningkatkan Critical Buildup pengguna sebanyak 5% selama 3 detik.",
													 "Dealing a critical hit to enemies will increases user's Critical Buildup by 5% for 3 seconds.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Defense Penetration pengguna sebanyak 7%.",
													 "Increases user's Defense Penetration by 7%."),
								   num: 22, val: 7},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 11:
					result = {
						stat1 : {num: 2, type: "flat", val: round(300 + 300 * (target_data.level/50))},
						stat2 : {num: 11, val: round(5 + 15 * floor(target_data.level/5) / 10)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Re-energized Vitality III", "Re-energized Vitality III"),
								   desc: switch_lang("Meningkatkan Health Points pengguna sebesar 15%.",
													 "Increases user's Health Points by 15%."),
								   num: 1, type: "percent", val: 15},
						effect2 : {title: switch_lang("Emergency Healing", "Emergency Healing"),
								   desc: switch_lang("Jika HP pengguna di bawah 40% dari HP maksimum, meningkatkan Healing Output pengguna sebesar 20%.",
													 "When user's HP is under 40% of Max HP, increases user's Healing Output by 20%.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Debuff Resistance pengguna sebanyak 20%.",
													 "Increases user's Debuff Resistance by 20%."),
								   num: 31, val: 20},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 12:
					result = {
						stat1 : {num: 2, type: "flat", val: round(300 + 300 * (target_data.level/50))},
						stat2 : {num: 5, val: round(5 + 10 * floor(target_data.level/5) / 10)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Anti-acid Layer II", "Anti-acid Layer II"),
								   desc: switch_lang("Meningkatkan Acid Damage Reduction pengguna sebesar 10%.",
													 "Increases user's Acid Damage Reduction by 10%."),
								   num: 21, val: 10},
						effect2 : {title: switch_lang("Intensive Healing", "Intensive Healing"),
								   desc: switch_lang("Menyembuhkan HP pengguna sebanyak 5% dari Max HP setiap 10 detik ketika pengguna sedang aktif.",
													 "Heals user's HP by 5% of Max HP every 10 seconds when user is active.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Physical Damage Bonus pengguna sebanyak 30%.",
													 "Increases user's Physical Damage Bonus by 30%."),
								   num: 16, val: 30},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 13:
					result = {
						stat1 : {num: 2, type: "flat", val: round(400 + 500 * (target_data.level/50))},
						stat2 : {num: 5, val: round(5 + 15 * (target_data.level/50))},
						stat3 : {num: 6, val: round(5 + 15 * floor(target_data.level/5) / 10)},
						effect1 : {title: switch_lang("Armor Wrecker III", "Armor Wrecker III"),
								   desc: switch_lang("Meningkatkan Armor Damage pengguna sebesar 35%.",
													 "Increases user's Armor Damage by 35%."),
								   num: 24, val: 35},
						effect2 : {title: switch_lang("Unstable Power", "Unstable Power"),
								   desc: switch_lang("Memberikan serangan kritis pada musuh akan meningkatkan ATK pengguna sebesar 5% selama 5 detik. Bisa ditumpuk (Maks: 4 tumpukan).",
													 "Dealing a critical hit to enemies will increase user's ATK by 5% for 5 seconds. Stackable (Max: 4 stacks).")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Attack Speed pengguna sebanyak 15%.",
													 "Increases user's Attack Speed by 15%."),
								   num: 12, val: 15},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Meningkatkan ATK pengguna sebesar 30% selama 6 detik setelah menerima serangan kritis dari musuh.",
													 "Increases user's ATK by 30% for 6 seconds after user takes a critical hit from enemies.")}
					}
					break;
				case 14:
					result = {
						stat1 : {num: 2, type: "flat", val: round(400 + 500 * (target_data.level/50))},
						stat2 : {num: 8, val: round(10 + 15 * (target_data.level/50))},
						stat3 : {num: 14, val: round(5 + 15 * floor(target_data.level/5) / 10)},
						effect1 : {title: switch_lang("Unshakeable III", "Unshakeable III"),
								   desc: switch_lang("Meningkatkan Ignore Interruption pengguna sebesar 15%.",
													 "Increases user's Ignore Interruption by 15%."),
								   num: 6, val: 15},
						effect2 : {title: switch_lang("Sharp Dodge", "Sharp Dodge"),
								   desc: switch_lang("Meningkatkan Agility pengguna sebesar 35% selama 2 detik setelah menggunakan dash.",
													 "Increases user's Agility by 35% for 2 seconds after using dash.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Armor Burst pengguna sebanyak 100%.",
													 "Increases user's Armor Burst by 100%."),
								   num: 25, val: 100},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Menambah poin Critical Buildup pengguna sebesar 5% setiap 2 detik ketika pengguna sedang aktif.",
													 "Increases user's Critical Buildup points by 5% every 2 seconds if user is active.")}
					}
					break;
				case 15:
					result = {
						stat1 : {num: 2, type: "flat", val: round(400 + 500 * (target_data.level/50))},
						stat2 : {num: 9, val: round(15 + 30 * (target_data.level/50))},
						stat3 : {num: 11, val: round(10 + 15 * floor(target_data.level/5) / 10)},
						effect1 : {title: switch_lang("Anti-acid Layer III", "Anti-acid Layer III"),
								   desc: switch_lang("Meningkatkan Acid Damage Reduction pengguna sebesar 15%.",
													 "Increases user's Acid Damage Reduction by 15%."),
								   num: 21, val: 15},
						effect2 : {title: switch_lang("Enriched Med-kit", "Enriched Med-kit"),
								   desc: switch_lang("Perangkat Penyembuh akan meningkatkan ATK, DEF, dan Acid Damage Reduction pengambil sebesar 15% selama 7 detik.",
													 "Healing Devices will increase taker's ATK, DEF, and Acid Damage Reduction by 15% for 7 seconds.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Debuff Resistance pengguna sebanyak 25%.",
													 "Increases user's Debuff Resistance by 25%."),
								   num: 31, val: 25},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Perangkat penyembuh juga akan menambah poin Critical Buildup pengambil sebanyak 75%.",
													 "Healing Devices will also increase taker's Critical Buildup points by 75%.")}
					}
					break;
				case 16:
					result = {
						stat1 : {num: 2, type: "flat", val: round(400 + 500 * (target_data.level/50))},
						stat2 : {num: 6, val: round(5 + 15 * (target_data.level/50))},
						stat3 : {num: 21, val: round(5 + 15 * floor(target_data.level/5) / 10)},
						effect1 : {title: switch_lang("Sturdy Pivot II", "Sturdy Pivot II"),
								   desc: switch_lang("Meningkatkan Physical Damage Bonus pengguna sebesar 35%.",
													 "Increases user's Physical Damage Bonus by 35%."),
								   num: 16, val: 35},
						effect2 : {title: switch_lang("Shortened Flow", "Shortened Flow"),
								   desc: switch_lang("Setiap 3 serangan dasar akan mengurangi cooldown dari gerakan spesial pengguna sebanyak 1 detik.",
													 "Every 3 basic attacks will decrease user's special moves cooldown by 1 second.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan CC Power pengguna sebanyak 40%.",
													 "Increases user's CC Power by 40%."),
								   num: 28, val: 40},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Meningkatkan ATK dan DEF semua anggota tim sebesar 15% selama 10 detik setelah mengerahkan perangkat pendukung.",
													 "Increases ATK and DEF of all team members by 15% for 10 seconds after deploying support devices")}
					}
					break;
			}
			
			var add_num = 0;
			for (var i = 0; i < source - 2; i++) {
				add_num += trait_cnt_list[i];
			}
			var db_trait = db_create_trait();
			if (target_data.rarity == 1 || target_data.rarity == 2) {
				result.trait1 = {num: -1, tier: 0, val: 0};
				result.trait2 = {num: -1, tier: 0, val: 0};
			} else if (target_data.rarity == 3 || target_data.rarity == 4) {
				result.trait1 = {num: target_data.trait1_type, tier: clamp(target_data.trait1_tier, 1, 5), val: 0};
				result.trait2 = {num: -1, tier: 0, val: 0};
				if (target_data.trait1_type > 0) {
					result.trait1.num += add_num;
					result.trait1.val = floor(db_get_value(db_trait, 3, result.trait1.num) + (db_get_value(db_trait, 4, result.trait1.num)/4*(clamp(target_data.trait1_tier, 1, 5)-1)));
					result.trait1.stat_num = db_get_value(db_trait, 2, result.trait1.num);
				}
			} else if (target_data.rarity == 5 || target_data.rarity == 6) {
				result.trait1 = {num: target_data.trait1_type, tier: clamp(target_data.trait1_tier, 1, 5), val: 0};
				result.trait2 = {num: target_data.trait2_type, tier: clamp(target_data.trait2_tier, 1, 5), val: 0};
				if (target_data.trait1_type > 0) {
					result.trait1.num += add_num;
					result.trait1.val = floor(db_get_value(db_trait, 3, result.trait1.num) + (db_get_value(db_trait, 4, result.trait1.num)/4*(clamp(target_data.trait1_tier, 1, 5)-1)));
					result.trait1.stat_num = db_get_value(db_trait, 2, result.trait1.num);
				}
				if (target_data.trait2_type > 0) {
					result.trait2.num += add_num;
					result.trait2.val = floor(db_get_value(db_trait, 3, result.trait2.num) + (db_get_value(db_trait, 4, result.trait2.num)/4*(clamp(target_data.trait2_tier, 1, 5)-1)));
					result.trait2.stat_num = db_get_value(db_trait, 2, result.trait2.num);
				}
			} 
			ds_grid_destroy(db_trait);
		break;
		case 4:		// Bearing
			switch (target_data.num) {
				case 1:
					result = {
						stat1 : {num: 1, type: "flat", val: round(700 + (target_data.level/50) * 1800)},
						stat2 : {num: 0, val: 0},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Efek Pasif", "Passive Effect"),
								   desc: switch_lang("Tidak ada efek pasif dari bearing ini.",
													 "No passive effect from this bearing."),
								   num: 0, val: 0},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari bearing ini.",
													 "No special effect from this bearing.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 2:
					result = {
						stat1 : {num: 1, type: "flat", val: round(1250 + (target_data.level/50) * 2750)},
						stat2 : {num: 0, val: 0},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Re-energized Vitality I", "Re-energized Vitality I"),
								   desc: switch_lang("Meningkatkan HP pengguna sebesar 5%.",
													 "Increases user's HP by 5%."),
								   num: 1, type: "percent", val: 5},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari bearing ini.",
													 "No special effect from this bearing.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 3:
					result = {
						stat1 : {num: 1, type: "flat", val: round(2500 + (target_data.level/50) * 4000)},
						stat2 : {num: 1, type: "percent", val: round(5 + 20 * floor(target_data.level/10) * 10 / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Re-energized Vitality II", "Re-energized Vitality II"),
								   desc: switch_lang("Meningkatkan HP pengguna sebesar 10%.",
													 "Increases user's HP by 10%."),
								   num: 1, type: "percent", val: 10},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari bearing ini.",
													 "No special effect from this bearing.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 4:
					result = {
						stat1 : {num: 1, type: "flat", val: round(2500 + (target_data.level/50) * 4000)},
						stat2 : {num: 5, val: round(2 + 20 * floor(target_data.level/10) * 8 / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Misery Strike I", "Misery Strike I"),
								   desc: switch_lang("Meningkatkan Damage Output pengguna sebesar 5%.",
													 "Increases user's Damage Output by 5%."),
								   num: 5, val: 5},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari bearing ini.",
													 "No special effect from this bearing.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 5:
					result = {
						stat1 : {num: 1, type: "flat", val: round(2500 + (target_data.level/50) * 4000)},
						stat2 : {num: 8, val: round(3 + 20 * floor(target_data.level/10) * 12 / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Frailty Revealer I", "Frailty Revealer I"),
								   desc: switch_lang("Meningkatkan Critical Buildup pengguna sebesar 5%.",
													 "Increases user's Critical Buildup by 5%."),
								   num: 8, val: 5},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari bearing ini.",
													 "No special effect from this bearing.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 6:
					result = {
						stat1 : {num: 1, type: "flat", val: round(2500 + (target_data.level/50) * 4000)},
						stat2 : {num: 18, val: round(2 + 20 * floor(target_data.level/10) * 8 / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Heavy Coating I", "Heavy Coating I"),
								   desc: switch_lang("Meningkatkan Physical Damage Reduction pengguna sebesar 10%.",
													 "Increases user's Physical Damage Reduction by 10%."),
								   num: 18, val: 10},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari bearing ini.",
													 "No special effect from this bearing.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 7:
					result = {
						stat1 : {num: 1, type: "flat", val: round(2500 + (target_data.level/50) * 4000)},
						stat2 : {num: 25, val: round(15 + 20 * 20 * floor(target_data.level/10) / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Violent Sharpnel I", "Violent Sharpnel I"),
								   desc: switch_lang("Meningkatkan Armor Burst pengguna sebesar 20%.",
													 "Increases user's Armor Burst by 20%."),
								   num: 25, val: 20},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari bearing ini.",
													 "No special effect from this bearing.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 8:
					result = {
						stat1 : {num: 1, type: "flat", val: round(2500 + (target_data.level/50) * 4000)},
						stat2 : {num: 19, val: round(5 + 15 * 20 * floor(target_data.level/10) / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("High Concentration I", "High Concentration I"),
								   desc: switch_lang("Meningkatkan Acidity Bonus pengguna sebesar 15%.",
													 "Increases user's Acidity Bonus by 15%."),
								   num: 19, val: 15},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari bearing ini.",
													 "No special effect from this bearing.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 9:
					result = {
						stat1 : {num: 1, type: "flat", val: round(3500 + 5500 * (target_data.level/50))},
						stat2 : {num: 13, val: round(5 + 10 * floor(target_data.level/5) / 10)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Heavy Coating II", "Heavy Coating II"),
								   desc: switch_lang("Meningkatkan Physical Damage Reduction pengguna sebesar 10%.",
													 "Increases user's Physical Damage Reduction by 10%."),
								   num: 18, val: 10},
						effect2 : {title: switch_lang("Adaptive Shell", "Adaptive Shell"),
								   desc: switch_lang("Terkena serangan dari musuh akan meningkatkan Physical Damage Reduction pengguna sebesar 10% selama 3 detik.",
													 "Hit by enemies will increase user's Physical Damage Reduction by 10% for 3 seconds.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Cooldown Reduction pengguna sebanyak 10%.",
													 "Increases user's Cooldown Reduction by 10%."),
								   num: 7, val: 10},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 10:
					result = {
						stat1 : {num: 1, type: "flat", val: round(3500 + 5500 * (target_data.level/50))},
						stat2 : {num: 2, type: "percent", val: round(5 + 15 * floor(target_data.level/5) / 10)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Frailty Revealer II", "Frailty Revealer II"),
								   desc: switch_lang("Meningkatkan Critical Buildup pengguna sebesar 10%.",
													 "Increases user's Critical Buildup by 10%."),
								   num: 8, val: 10},
						effect2 : {title: switch_lang("Delayed Destruction", "Delayed Destruction"),
								   desc: switch_lang("Meningkatkan Damage Output pengguna sebesar 20% untuk serangan berikutnya setelah menggunakan dash.",
													 "Increases user's Damage Output by 20% for the next attack after using dash.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Defense Penetration pengguna sebanyak 7%.",
													 "Increases user's Defense Penetration by 7%."),
								   num: 22, val: 7},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 11:
					result = {
						stat1 : {num: 1, type: "flat", val: round(3500 + 5500 * (target_data.level/50))},
						stat2 : {num: 19, val: round(10 + 20 * floor(target_data.level/5) / 10)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Heavy Coating II", "Heavy Coating II"),
								   desc: switch_lang("Meningkatkan Physical Damage Reduction pengguna sebesar 10%.",
													 "Increases user's Physical Damage Reduction by 10%."),
								   num: 18, val: 10},
						effect2 : {title: switch_lang("Enhanced Med-kit", "Enhanced Med-kit"),
								   desc: switch_lang("Perangkat penyembuh akan meningkatkan ATK dan Physical Damage Reduction pengambil sebesar 10% selama 5 detik.",
													 "Healing devices will increase taker's ATK and Physical Damage Reduction by 10% for 5 seconds.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Debuff Resistance pengguna sebanyak 20%.",
													 "Increases user's Debuff Resistance by 20%."),
								   num: 31, val: 20},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 12:
					result = {
						stat1 : {num: 1, type: "flat", val: round(3500 + 5500 * (target_data.level/50))},
						stat2 : {num: 8, val: round(5 + 15 * floor(target_data.level/5) / 10)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Frailty Concealer II", "Frailty Concealer II"),
								   desc: switch_lang("Meningkatkan Critical Protection pengguna sebesar 15%.",
													 "Increases user's Critical Protection by 15%."),
								   num: 10, val: 15},
						effect2 : {title: switch_lang("Upgraded Device", "Upgraded Device"),
								   desc: switch_lang("Meningkatkan HP, ATK, dan DEF dari semua perangkat pendukung sebanyak 10%.",
													 "Increases HP, ATK, and DEF of all support devices by 10%.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Physical Damage Bonus pengguna sebanyak 30%.",
													 "Increases user's Physical Damage Bonus by 30%."),
								   num: 16, val: 30},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 13:
					result = {
						stat1 : {num: 1, type: "flat", val: round(5000 + 10000 * (target_data.level/50))},
						stat2 : {num: 8, val: round(10 + 15 * (target_data.level/50))},
						stat3 : {num: 7, val: round(5 + 15 * floor(target_data.level/5) / 10)},
						effect1 : {title: switch_lang("Powered Punch III", "Powered Punch III"),
								   desc: switch_lang("Meningkatkan Melee Damage Output pengguna sebesar 15%.",
													 "Increases user's Melee Damage Output by 15%."),
								   num: 13, val: 15},
						effect2 : {title: switch_lang("High-adaptive Shell", "High-adaptive Shell"),
								   desc: switch_lang("Terkena serangan dari musuh akan meningkatkan Physical Damage Reduction pengguna sebesar 5% selama 3 detik. Bisa ditumpuk (Maks: 6 tumpukan).",
													 "Hit by enemies will increase user's Damage Reduction by 5% for 3 seconds. Stackable (Max: 6 stacks).")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Attack Speed pengguna sebanyak 15%.",
													 "Increases user's Attack Speed by 15%."),
								   num: 12, val: 15},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Meningkatkan ATK pengguna sebesar 30% selama 6 detik setelah menerima serangan kritis dari musuh.",
													 "Increases user's ATK by 30% for 6 seconds after user takes a critical hit from enemies.")}
					}
					break;
				case 14:
					result = {
						stat1 : {num: 1, type: "flat", val: round(5000 + 10000 * (target_data.level/50))},
						stat2 : {num: 9, val: round(15 + 30 * (target_data.level/50))},
						stat3 : {num: 22, val: round(5 + 15 * floor(target_data.level/5) / 10)},
						effect1 : {title: switch_lang("Heavy Coating III", "Heavy Coating III"),
								   desc: switch_lang("Meningkatkan Physical Damage Reduction pengguna sebesar 15%.",
													 "Increases user's Physical Damage Reduction by 15%."),
								   num: 18, val: 15},
						effect2 : {title: switch_lang("Serious Breakage", "Serious Breakage"),
								   desc: switch_lang("Memberikan serangan kritis pada musuh akan meningkatkan Critical Damage pengguna sebesar 10% selama 3 detik. Bisa ditumpuk (Maks: 4 tumpukan).",
													 "Dealing a critical hit to enemies will increase user's Critical Damage by 10% for 3 seconds. Stackable (Max: 4 stacks).")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Armor Burst pengguna sebanyak 100%.",
													 "Increases user's Armor Burst by 100%."),
								   num: 25, val: 100},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Menambah poin Critical Buildup pengguna sebesar 5% setiap 2 detik ketika pengguna sedang aktif.",
													 "Increases user's Critical Buildup points by 5% every 2 seconds if user is active.")}
					}
					break;
				case 15:
					result = {
						stat1 : {num: 1, type: "flat", val: round(5000 + 10000 * (target_data.level/50))},
						stat2 : {num: 4, val: round(10 + 15 * (target_data.level/50))},
						stat3 : {num: 7, val: round(5 + 15 * floor(target_data.level/5) / 10)},
						effect1 : {title: switch_lang("Misery Strike III", "Misery Strike III"),
								   desc: switch_lang("Meningkatkan Damage Output pengguna sebesar 15%.",
													 "Increases user's Damage Output by 15%."),
								   num: 4, val: 15},
						effect2 : {title: switch_lang("Enhanced Treatment", "Enhanced Treatment"),
								   desc: switch_lang("Meningkatkan Healing Output pengguna sebesar 15% selama 5 detik setelah menggunakan gerakan spesial.",
													 "Increases user's Healing Output by 15% for 5 seconds after using special moves.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Debuff Resistance pengguna sebanyak 25%.",
													 "Increases user's Debuff Resistance by 25%."),
								   num: 31, val: 25},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Perangkat penyembuh juga akan menambah poin Critical Buildup pengambil sebanyak 75%.",
													 "Healing Devices will also increase taker's Critical Buildup points by 75%.")}
					}
					break;
				case 16:
					result = {
						stat1 : {num: 1, type: "flat", val: round(5000 + 10000 * (target_data.level/50))},
						stat2 : {num: 9, val: round(15 + 30 * (target_data.level/50))},
						stat3 : {num: 8, val: round(10 + 15 * floor(target_data.level/5) / 10)},
						effect1 : {title: switch_lang("Armor Wrecker III", "Armor Wrecker III"),
								   desc: switch_lang("Meningkatkan Armor Damage pengguna sebesar 35%.",
													 "Increases user's Armor Damage by 35%."),
								   num: 24, val: 35},
						effect2 : {title: switch_lang("Reinforced Device", "Reinforced Device"),
								   desc: switch_lang("Meningkatkan Max HP, ATK, dan DEF dari semua Perangkat Pendukung sebesar 20%.",
													 "Increases Max HP, ATK, and DEF of all Support Device by 20%.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan CC Power pengguna sebanyak 40%.",
													 "Increases user's CC Power by 40%."),
								   num: 28, val: 40},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Meningkatkan ATK dan DEF semua anggota tim sebesar 15% selama 10 detik setelah mengerahkan perangkat pendukung.",
													 "Increases ATK and DEF of all team members by 15% for 10 seconds after deploying support devices")}
					}
					break;
			}
			
			var add_num = 0;
			for (var i = 0; i < source - 2; i++) {
				add_num += trait_cnt_list[i];
			}
			var db_trait = db_create_trait();
			if (target_data.rarity == 1 || target_data.rarity == 2) {
				result.trait1 = {num: -1, tier: 0, val: 0};
				result.trait2 = {num: -1, tier: 0, val: 0};
			} else if (target_data.rarity == 3 || target_data.rarity == 4) {
				result.trait1 = {num: target_data.trait1_type, tier: clamp(target_data.trait1_tier, 1, 5), val: 0};
				result.trait2 = {num: -1, tier: 0, val: 0};
				if (target_data.trait1_type > 0) {
					result.trait1.num += add_num;
					result.trait1.val = floor(db_get_value(db_trait, 3, result.trait1.num) + (db_get_value(db_trait, 4, result.trait1.num)/4*(clamp(target_data.trait1_tier, 1, 5)-1)));
					result.trait1.stat_num = db_get_value(db_trait, 2, result.trait1.num);
				}
			} else if (target_data.rarity == 5 || target_data.rarity == 6) {
				result.trait1 = {num: target_data.trait1_type, tier: clamp(target_data.trait1_tier, 1, 5), val: 0};
				result.trait2 = {num: target_data.trait2_type, tier: clamp(target_data.trait2_tier, 1, 5), val: 0};
				if (target_data.trait1_type > 0) {
					result.trait1.num += add_num;
					result.trait1.val = floor(db_get_value(db_trait, 3, result.trait1.num) + (db_get_value(db_trait, 4, result.trait1.num)/4*(clamp(target_data.trait1_tier, 1, 5)-1)));
					result.trait1.stat_num = db_get_value(db_trait, 2, result.trait1.num);
				}
				if (target_data.trait2_type > 0) {
					result.trait2.num += add_num;
					result.trait2.val = floor(db_get_value(db_trait, 3, result.trait2.num) + (db_get_value(db_trait, 4, result.trait2.num)/4*(clamp(target_data.trait2_tier, 1, 5)-1)));
					result.trait2.stat_num = db_get_value(db_trait, 2, result.trait2.num);
				}
			} 
			ds_grid_destroy(db_trait);
		break;
		case 5:		// Crust
			switch (target_data.num) {
				case 1:
					result = {
						stat1 : {num: 3, type: "flat", val: round(60 + (target_data.level/50) * 160)},
						stat2 : {num: 0, val: 0},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Efek Pasif", "Passive Effect"),
								   desc: switch_lang("Tidak ada efek pasif dari bearing ini.",
													 "No passive effect from this bearing."),
								   num: 0, val: 0},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari bearing ini.",
													 "No special effect from this bearing.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 2:
					result = {
						stat1 : {num: 3, type: "flat", val: round(100 + (target_data.level/50) * 270)},
						stat2 : {num: 0, val: 0},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Fortified Jacket I", "Fortified Jacket I"),
								   desc: switch_lang("Meningkatkan DEF pengguna sebesar 5%.",
													 "Increases user's DEF by 5%."),
								   num: 3, type: "percent", val: 5},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari bearing ini.",
													 "No special effect from this bearing.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 3:
					result = {
						stat1 : {num: 3, type: "flat", val: round(230 + (target_data.level/50) * 320)},
						stat2 : {num: 3, type: "percent", val: round(5 + 10 * 20 * floor(target_data.level/10) / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Fortified Jacket II", "Fortified Jacket II"),
								   desc: switch_lang("Meningkatkan DEF pengguna sebesar 10%.",
													 "Increases user's DEF by 10%."),
								   num: 3, type: "percent", val: 10},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari bearing ini.",
													 "No special effect from this bearing.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 4:
					result = {
						stat1 : {num: 3, type: "flat", val: round(230 + (target_data.level/50) * 320)},
						stat2 : {num: 4, val: round(5 + 10 * 20 * floor(target_data.level/10) / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Speedy Tire I", "Speedy Tire I"),
								   desc: switch_lang("Meningkatkan Agility pengguna sebesar 10%.",
													 "Increases user's Agility by 10%."),
								   num: 4, val: 10},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari bearing ini.",
													 "No special effect from this bearing.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 5:
					result = {
						stat1 : {num: 3, type: "flat", val: round(230 + (target_data.level/50) * 320)},
						stat2 : {num: 7, val: round(2 + 8 * 20 * floor(target_data.level/10) / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Quick Cycle I", "Quick Cycle I"),
								   desc: switch_lang("Meningkatkan Cooldown Reduction pengguna sebesar 5%.",
													 "Increases user's Cooldown Reduction by 5%."),
								   num: 7, val: 5},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari bearing ini.",
													 "No special effect from this bearing.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 6:
					result = {
						stat1 : {num: 3, type: "flat", val: round(230 + (target_data.level/50) * 320)},
						stat2 : {num: 10, val: round(3 + 12 * 20 * floor(target_data.level/10) / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Frailty Concealer I", "Frailty Concealer I"),
								   desc: switch_lang("Meningkatkan Critical Protection pengguna sebesar 10%.",
													 "Increases user's Critical Protection by 10%."),
								   num: 10, val: 10},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari bearing ini.",
													 "No special effect from this bearing.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 7:
					result = {
						stat1 : {num: 3, type: "flat", val: round(230 + (target_data.level/50) * 320)},
						stat2 : {num: 24, val: round(5 + 15 * 20 * floor(target_data.level/10) / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Armor Wrecker I", "Armor Wrecker I"),
								   desc: switch_lang("Meningkatkan Armor Damage pengguna sebesar 15%.",
													 "Increases user's Armor Damage by 15%."),
								   num: 24, val: 15},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari bearing ini.",
													 "No special effect from this bearing.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 8:
					result = {
						stat1 : {num: 3, type: "flat", val: round(230 + (target_data.level/50) * 320)},
						stat2 : {num: 21, val: round(2 + 8 * 20 * floor(target_data.level/10) / 100)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Anti-acid Layer I", "Anti-acid Layer I"),
								   desc: switch_lang("Meningkatkan Acid Damage Reduction pengguna sebesar 5%.",
													 "Increases user's Acid Damage Reduction by 5%."),
								   num: 21, val: 5},
						effect2 : {title: switch_lang("Efek Spesial", "Special Effect"),
								   desc: switch_lang("Tidak ada efek spesial dari bearing ini.",
													 "No special effect from this bearing.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Tidak ada efek 2-set dari set peralatan ini.",
													 "No 2-set effect from this equipment set."),
								   num: 0, val: 0},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 9:
					result = {
						stat1 : {num: 3, type: "flat", val: round(300 + 400 * (target_data.level/50))},
						stat2 : {num: 4, val: round(5 + 15 * floor(target_data.level/5) / 10)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Unshakeable II", "Unshakeable II"),
								   desc: switch_lang("Meningkatkan Ignore Interruption pengguna sebesar 10%.",
													 "Increases user's Ignore Interruption by 10%."),
								   num: 6, val: 10},
						effect2 : {title: switch_lang("Slick Dodge", "Slick Dodge"),
								   desc: switch_lang("Meningkatkan Agility pengguna sebesar 10% selama 3 detik setelah melakukan dash.",
													 "Increases user's Agility by 10% for 3 seconds after using dash")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Cooldown Reduction pengguna sebanyak 10%.",
													 "Increases user's Cooldown Reduction by 10%."),
								   num: 7, val: 10},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 10:
					result = {
						stat1 : {num: 3, type: "flat", val: round(300 + 400 * (target_data.level/50))},
						stat2 : {num: 14, val: round(5 + 10 * floor(target_data.level/5) / 10)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Penetrator II", "Penetrator II"),
								   desc: switch_lang("Meningkatkan Armor Penetration pengguna sebesar 15%.",
													 "Increases user's Armor Penetration by 15%."),
								   num: 23, val: 15},
						effect2 : {title: switch_lang("Stable Elimination", "Stable Elimination"),
								   desc: switch_lang("Meningkatkan Critical Damage pengguna sebesar 15% jika HP pengguna di atas 80% dari HP maksimum.",
													 "Increases user's Critical Damage by 15% if user's HP higher than 80% of Max HP.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Defense Penetration pengguna sebanyak 7%.",
													 "Increases user's Defense Penetration by 7%."),
								   num: 22, val: 7},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 11:
					result = {
						stat1 : {num: 3, type: "flat", val: round(300 + 400 * (target_data.level/50))},
						stat2 : {num: 5, val: round(5 + 10 * floor(target_data.level/5) / 10)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Anti-acid Layer II", "Anti-acid Layer II"),
								   desc: switch_lang("Meningkatkan Acid Damage Reduction pengguna sebesar 10%.",
													 "Increases user's Acid Damage Reduction by 10%."),
								   num: 21, val: 10},
						effect2 : {title: switch_lang("Acid Improvement", "Acid Improvement"),
								   desc: switch_lang("Meningkatkan ATK dan Acidity Bonus pengguna sebesar 20% selama 10 detik setelah menggunakan skill ultimate.",
													 "Increases user's ATK and Acidity Bonus by 20% for 10 seconds after using ultimate skills.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Debuff Resistance pengguna sebanyak 20%.",
													 "Increases user's Debuff Resistance by 20%."),
								   num: 31, val: 20},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 12:
					result = {
						stat1 : {num: 3, type: "flat", val: round(300 + 400 * (target_data.level/50))},
						stat2 : {num: 18, val: round(5 + 10 * floor(target_data.level/5) / 10)},
						stat3 : {num: 0, val: 0},
						effect1 : {title: switch_lang("Frailty Destroyer II", "Frailty Destroyer II"),
								   desc: switch_lang("Meningkatkan Critical Damage pengguna sebesar 25%.",
													 "Increases user's Critical Damage by 25%."),
								   num: 9, val: 25},
						effect2 : {title: switch_lang("Durability Extension", "Durability Extension"),
								   desc: switch_lang("Memperpanjang durasi dari semua perangkat pendukung sebanyak 15%.",
													 "Increases duration of all support devices by 15%.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Physical Damage Bonus pengguna sebanyak 30%.",
													 "Increases user's Physical Damage Bonus by 30%."),
								   num: 16, val: 30},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Tidak ada efek 3-set dari set peralatan ini.",
													 "No 3-set effect from this equipment set.")}
					}
					break;
				case 13:
					result = {
						stat1 : {num: 3, type: "flat", val: round(500 + 550 * (target_data.level/50))},
						stat2 : {num: 4, val: round(10 + 15 * (target_data.level/50))},
						stat3 : {num: 18, val: round(5 + 15 * floor(target_data.level/5) / 10)},
						effect1 : {title: switch_lang("Frailty Destroyer III", "Frailty Destroyer III"),
								   desc: switch_lang("Meningkatkan Critical Damage pengguna sebesar 35%.",
													 "Increases user's Critical Damage by 35%."),
								   num: 9, val: 35},
						effect2 : {title: switch_lang("Energy Discharge", "Energy Discharge"),
								   desc: switch_lang("Meningkatkan Physical Damage Bonus pengguna sebesar 80% untuk serangan berikutnya setelah menggunakan gerakan spesial.",
													 "Increases user's Physical Damage Bonus by 80% for the next attack after using special move.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Attack Speed pengguna sebanyak 15%.",
													 "Increases user's Attack Speed by 15%."),
								   num: 12, val: 15},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Meningkatkan ATK pengguna sebesar 30% selama 6 detik setelah menerima serangan kritis dari musuh.",
													 "Increases user's ATK by 30% for 6 seconds after user takes a critical hit from enemies.")}
					}
					break;
				case 14:
					result = {
						stat1 : {num: 3, type: "flat", val: round(500 + 550 * (target_data.level/50))},
						stat2 : {num: 2, type: "percent", val: round(10 + 15 * (target_data.level/50))},
						stat3 : {num: 23, val: round(10 + 20 * floor(target_data.level/5) / 10)},
						effect1 : {title: switch_lang("Augmented Force II", "Augmented Force II"),
								   desc: switch_lang("Meningkatkan Buff Power pengguna sebesar 35%.",
													 "Increases user's Buff Power by 35%."),
								   num: 30, val: 35},
						effect2 : {title: switch_lang("Great Calmness", "Great Calmness"),
								   desc: switch_lang("Meningkatkan Critical Buildup pengguna sebesar 10% jika HP pengguna di atas 85% dari Max HP.",
													 "Increases user's Critical Buildup by 10% if user's HP higher than 85% of Max HP.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Armor Burst pengguna sebanyak 100%.",
													 "Increases user's Armor Burst by 100%."),
								   num: 25, val: 100},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Menambah poin Critical Buildup pengguna sebesar 5% setiap 2 detik ketika pengguna sedang aktif.",
													 "Increases user's Critical Buildup points by 5% every 2 seconds if user is active.")}
					}
					break;
				case 15:
					result = {
						stat1 : {num: 3, type: "flat", val: round(500 + 550 * (target_data.level/50))},
						stat2 : {num: 19, val: round(10 + 30 * (target_data.level/50))},
						stat3 : {num: 6, val: round(5 + 15 * floor(target_data.level/5) / 10)},
						effect1 : {title: switch_lang("Frailty Revealer III", "Frailty Revealer III"),
								   desc: switch_lang("Meningkatkan Critical Buildup pengguna sebesar 15%.",
													 "Increases user's Critical Buildup by 15%."),
								   num: 8, val: 15},
						effect2 : {title: switch_lang("Concentrated Acid", "Concentrated Acid"),
								   desc: switch_lang("Meningkatkan Acid Damage Reduction dan Acidity Bonus pengguna sebesar 5% selama 3 detik setelah pengguna menerima kerusakan asam.. Bisa ditumpuk (Maks: 5 tumpukan).",
													 "Increases user's Acid Damage Reduction and Acidity Bonus by 5% for 3 seconds after user takes acid damage. Stackable (Max: 5 stacks).")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan Debuff Resistance pengguna sebanyak 25%.",
													 "Increases user's Debuff Resistance by 25%."),
								   num: 31, val: 25},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Perangkat penyembuh juga akan menambah poin Critical Buildup pengambil sebanyak 75%.",
													 "Healing Devices will also increase taker's Critical Buildup points by 75%.")}
					}
					break;
				case 16:
					result = {
						stat1 : {num: 3, type: "flat", val: round(500 + 550 * (target_data.level/50))},
						stat2 : {num: 5, val: round(5 + 15 * (target_data.level/50))},
						stat3 : {num: 10, val: round(10 + 15 * floor(target_data.level/5) / 10)},
						effect1 : {title: switch_lang("Heavy Coating III", "Heavy Coating III"),
								   desc: switch_lang("Meningkatkan Physical Damage Reduction pengguna sebesar 15%.",
													 "Increases user's Physical Damage Reduction by 15%."),
								   num: 18, val: 15},
						effect2 : {title: switch_lang("Continued Durability", "Continued Durability"),
								   desc: switch_lang("Memperpanjang durasi dari semua perangkat pendukung sebanyak 25%.",
													 "Increases duration of all support devices by 25%.")},
						effect3 : {title: switch_lang("Efek 2-Set", "2-Set Effect"),
								   desc: switch_lang("Meningkatkan CC Power pengguna sebanyak 40%.",
													 "Increases user's CC Power by 40%."),
								   num: 28, val: 40},
						effect4 : {title: switch_lang("Efek 3-Set", "3-Set Effect"),
								   desc: switch_lang("Meningkatkan ATK dan DEF semua anggota tim sebesar 15% selama 10 detik setelah mengerahkan perangkat pendukung.",
													 "Increases ATK and DEF of all team members by 15% for 10 seconds after deploying support devices")}
					}
					break;
			}
			
			var add_num = 0;
			for (var i = 0; i < source - 2; i++) {
				add_num += trait_cnt_list[i];
			}
			var db_trait = db_create_trait();
			if (target_data.rarity == 1 || target_data.rarity == 2) {
				result.trait1 = {num: -1, tier: 0, val: 0};
				result.trait2 = {num: -1, tier: 0, val: 0};
			} else if (target_data.rarity == 3 || target_data.rarity == 4) {
				result.trait1 = {num: target_data.trait1_type, tier: clamp(target_data.trait1_tier, 1, 5), val: 0};
				result.trait2 = {num: -1, tier: 0, val: 0};
				if (target_data.trait1_type > 0) {
					result.trait1.num += add_num;
					result.trait1.val = floor(db_get_value(db_trait, 3, result.trait1.num) + (db_get_value(db_trait, 4, result.trait1.num)/4*(clamp(target_data.trait1_tier, 1, 5)-1)));
					result.trait1.stat_num = db_get_value(db_trait, 2, result.trait1.num);
				}
			} else if (target_data.rarity == 5 || target_data.rarity == 6) {
				result.trait1 = {num: target_data.trait1_type, tier: clamp(target_data.trait1_tier, 1, 5), val: 0};
				result.trait2 = {num: target_data.trait2_type, tier: clamp(target_data.trait2_tier, 1, 5), val: 0};
				if (target_data.trait1_type > 0) {
					result.trait1.num += add_num;
					result.trait1.val = floor(db_get_value(db_trait, 3, result.trait1.num) + (db_get_value(db_trait, 4, result.trait1.num)/4*(clamp(target_data.trait1_tier, 1, 5)-1)));
					result.trait1.stat_num = db_get_value(db_trait, 2, result.trait1.num);
				}
				if (target_data.trait2_type > 0) {
					result.trait2.num += add_num;
					result.trait2.val = floor(db_get_value(db_trait, 3, result.trait2.num) + (db_get_value(db_trait, 4, result.trait2.num)/4*(clamp(target_data.trait2_tier, 1, 5)-1)));
					result.trait2.stat_num = db_get_value(db_trait, 2, result.trait2.num);
				}
			} 
			ds_grid_destroy(db_trait);
		break;
	}
	return result;
}


function db_get_col(db, col, func = noone) {
	// col dimulai dari 1
	// func(col, row) buat filter, mulai dari 1, harus urut dari kecil ke besar
	var result = [];
	
	for (var i = 0; i < ds_grid_height(db); i++) {
		if (func == noone) {
			array_push(result, db_get_value(db, col, i+1));
		} else {
			if (func(db, col, i+1)) {
				array_push(result, db_get_value(db, col, i+1));
			}
		}
	}

	return result;
}



function db_get_value_group(db, find_col, find_val, return_col = 1, func = noone) {
	// find_col & return_col mulai dari 1
	// func(row) buat filter
	var result = [];
	
	for (var i = 0; i < ds_grid_height(db); i++) {
		if (db_get_value(db, find_col, i+1) == find_val) {
			if (func == noone) {
				array_push(result, db_get_value(db, return_col, i+1));
			} else {
				if (func(i+1)) {
					array_push(result, db_get_value(db, return_col, i+1));
				}
			}
		}
	}

	return result;
}

function db_get_value_func(db, find_col, func, return_col = 1) {
	// find_col & return_col mulai dari 1
	// func(db, col, row) buat filter
	var result = [];
	
	for (var i = 0; i < ds_grid_height(db); i++) {
		if (func(db, find_col, i+1)) {
			array_push(result, db_get_value(db, return_col, i+1));
		}
	}

	return result;
}
