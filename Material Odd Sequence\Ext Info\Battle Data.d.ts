


interface Sprite {}
interface Object {}
interface int {}

type BotsData = {
	// db_get_row(db_bots, db_type_bots, class_num)
	num: int;
	name: string;
	unlocked: boolean;
	level: int;
	xp: int;
	weapon: int;
	core: int;
	bearing: int;
	crust: int;
	sprite: Sprite;
	sprite_w: int;
	derv: int;
	spmv: int;
	spmv_mod: int;
	ulti: int;
	batk_set: int;
	alt_batk_set: int;
	air_batk_seq: int;
	stg_obj: Object;
	stg_spr: {
		wp_spr: Sprite;
		wp_frm: int;
		wp_acc_spr: Sprite[];
		wp_acc_frm: int[];
	};

	// extra
	frame: int;
	desc: string;
	mc_desc: string;
	ts_desc: string;
	ts_active: boolean;
	stats_info: {
		stat_title: string[];
		stat_val: int[];
		info_title: string[];
		stat_standard: int[];
		stat_present: int[];
		info_data: string[];
	};
}

type BotsStats = {
	// data_bots_stats_tohash(stats_struct)
	[stats_name: string]: {
		num: int;
		val: int;
	};
}

type BotsAtk = {
	// bots_get_batk(db_bots, db_batk, db_stats, bots_stat, class_num, batk_seqtype = bots_batk_norm)
	// bots_get_skill(db_bots, db_skill, db_stats, bots_stat, class_num, skill_type, skill_num, ext = 0)
	type: int;
	chain_num: int;
	adj: {
		[numX: string]: int;
	};
	sk_type: int;
	level: int;
	eff_level: int;
	upgraded: int;
	sprite: Sprite;
	talent: {
		[code: string]: boolean;
	};

	index: {
		[name: string]: int | int[];
	};
	[index_name: string]: int | int[];

	multiplier: string;
	int_power: int;
	ignint_bonus: int;
	ignint_subs: int;
	target: skill_target_type;
	target_cnt: int;
	dmg_type: skill_dmg_type;
	dmg_excess?: int;
	cd_type: skill_cd_type;

	buff: stats_list | stats_list[];
	buff_target: skill_target_type | skill_target_type[];
	debuff: stats_ailment | stats_ailment[];

}

declare const enum skill_target_type {
	none,
	own, 
	ally,
	enemy,
	team,
	all_bots
}

declare const enum skill_dmg_type {
	none,
	physical,
	acid,
	absolute
}

declare const enum skill_cd_type {
	none,
	timeout,
	dyn_timeout,
	auto_timeout,
	usage_batk
}

declare const enum stats_list {				// < -3 = debuff, 100+ = multiplier
	hp_scale = -1, 
	atk_scale = -2, 
	def_scale = -3,
	hp = 1, 
	atk = 2, 
	def = 3, 
	agility = 4,
	dmg_output = 5,
	ignore_interruption = 6,
	cd_reduction = 7,
	crit_buildup = 8,
	crit_damage = 9,
	crit_protection = 10,
	healing_output = 11,
	atk_spd = 12,
	melee_do = 13,
	ranged_do = 14,
	aoe_dmg_scale = 15,
	physical_dmg_bonus = 16,
	physical_do = 17,
	dmg_reduction = 18,
	acidity_bonus = 19,
	acid_do = 20,
	tc = 21,
	def_penetration = 22,
	ap = 23,
	ab = 24,
	armor_burst = 25,
	armor_str = 26,
	dmg_res = 27,
	cc_power = 28,
	cc_res = 29,
	buff_power = 30,
	debuff_res = 31,
	charge_spd = 32,
	batk_db = 33,
	batk_do = 34,
	batk_power = 35,
	batk_eff = 36,
	derv_db = 37,
	derv_do = 38,
	derv_power = 39,
	derv_eff = 40,
	spmv_db = 41,
	spmv_do = 42,
	spmv_power = 43,
	spmv_eff = 44,
	ulti_db = 45,
	ulti_do = 46,
	ulti_power = 47,
	ulti_eff = 48,
	intg_db = 49,
	intg_do = 50,
	intg_power = 51,
	intg_eff = 52,
	accuracy = 53,
	recoil_reduction = 54,
	ammo_cap = 55,
	mags_cap = 56,
	reload_spd = 57
}

declare const enum stats_ailment {
	debuff_slow = 1,
	debuff_weaken = 2,
	debuff_impair = 3,
	debuff_break = 4,
	debuff_soften = 5,
	debuff_enervate = 6,
	debuff_crack = 7,
	debuff_exhaust = 8,
	debuff_devitalize = 9,
	debuff_fracture = 10,
	debuff_languish = 11,
	debuff_erode = 12,

	cc_flinch = 101, 
	cc_bind = 102, 
	cc_knockback = 103, 
	cc_airborne = 104, 
	cc_knockdown = 105, 
	cc_silence = 106, 
	cc_stun = 107, 
	cc_paralyze = 108, 
	cc_neutralize = 109
}

declare const enum stats_mod {
	immune_slow = 201,
	immune_weaken = 202,
	immune_impair = 203,
	immune_break = 204,
	immune_soften = 205,
	immune_enervate = 206,
	immune_crack = 207,
	immune_exhaust = 208,
	immune_devitalize = 209,
	immune_fracture = 210,
	immune_languish = 211,
	immune_erode = 212,
	immune_flinch = 213, 

	immune_bind = 214, 
	immune_knockback = 215, 
	immune_airborne = 216, 
	immune_knockdown = 217, 
	immune_silence = 218, 
	immune_stun = 219, 
	immune_paralyze = 220, 
	immune_neutralize = 221,

	cleanse_debuff = 251,
	cleanse_debuff_all = 252,

	infuse_physical = 301,
	infuse_acid = 302
}

declare const enum BOTS_PROPS {
	WEAPON,
	WEAPON_ACC1
}
