/* 
 * name: NexaFlux
 * desc: A flexible keyframe-based animation system for GameMaker 2.3+
 * author: @undervolta
 * version: 0.1.0
 * date: 2025-06-11
 * 
 * repo: https://github.com/undervolta/NexaFlux
 * docs: https://github.com/undervolta/NexaFlux/wiki 
 * license: MIT 
 * 
 * dependencies: None 
 * compatibility: GameMaker 2.3+ - All platforms 
 */ 


global.nexaflux = {
	enabled: false,
	auto_destroy: true,
	thread_count: 0,
	ref: [],
	actions: false
};

enum NEXAFLUX_PROP {
	TYPES,
	KEY_TIMES,
	VALUES,
	CURVES,
	ACTIONS,
	SPEED,
	MAX_TIME,
	LOOP_COUNT,
	IS_RELATIVE,
	IS_PLAYING,
	IS_REVERSED,
	IS_LOOPING,
	KEY_TIMES_EQUAL,
	ON_START,
	ON_END
}

enum NEXAFLUX_VALUE {
	TRANSLATE_X,
	TRANSLATE_Y,
	ROTATE,
	SCALE_X,
	SCALE_Y,
	DEP<PERSON>,
	INIT_DEPTH,
	ALPHA
}

enum NEXAFLUX_CURVES {
	LINEAR,
	EASE_IN_QUAD,
	EASE_OUT_QUAD,
	EASE_INOUT_QUAD
}

enum NEXAFLUX_MORPH {
	NONE,
	SHIFT,				// add current animation value to the front of the new animation
	SHIFT_RESET,		// same as SHIFT, but the new animation into the original animation on end
	REPLACE,			// replace the new animation first keyframe with the current animation value
	REPLACE_RESET,		// same as REPLACE, but the new animation into the original animation on end
	APPEND,				// append the current animation with the new animation
	APPEND_RESET		// same as APPEND, but the new animation into the original animation on end
}

/*
 * @desc Initialize NexaFlux system
 * @param {Real} [thread_count]=0 Thread count for the animation system. If 0, will use hardware concurrency. 
 * @param {Boolean} [auto_destroy]=true Automatically destroy the NexaFlux animation when it's not used anymore.
 */
function nexaflux_init(thread_count = 0, auto_destroy = true) {
	// guard clause
	if (global.nexaflux.enabled) {
		show_debug_message("NexaFlux ERROR: NexaFlux is already enabled. Please call `nexaflux_init()` only once.");
		return;
	}

	if (!is_real(thread_count)) {
		show_debug_message($"NexaFlux ERROR: Invalid thread count ({thread_count}). Please provide a valid integer.");
		return;
	} else if (thread_count < 0) {
		show_debug_message($"NexaFlux WARNING: Invalid thread count ({thread_count}). Thread count must be greater than or equal to 0. Defaulting to 0.");
		thread_count = 0;
	}

	if (!is_bool(auto_destroy)) {
		show_debug_message($"NexaFlux WARNING: Invalid `auto_destroy` value ({auto_destroy}). Defaulting to `true`.");
		auto_destroy = true;
	}

	global.nexaflux.enabled = true;
	global.nexaflux.thread_count = round(thread_count);
	global.nexaflux.auto_destroy = auto_destroy;
	global.nexaflux.actions = new NexaFluxActionCall();
}


function NexaFluxAnim(_ref = noone, _is_relative = false, _data = undefined, _actions = undefined) constructor {
	ref = _ref;						// instance, struct, or noone
	types = [];                     // 2D array of "translate_x", "translate_y", "rotate", "scale_x", "scale_y", "depth", "alpha"
	values = [];                    // 3D array of real values
	key_times = [];                 // 2D array of key times in frames (int) 
	curves = [];                    // 1D/2D array of NEXAFLUX_CURVES
	
	action = {
		funcs : [],					// 2D array of actions to be executed based on times 
		times : [],					// 1D array of time in frames (int)
		args : [],					// 2D array of arguments to be passed to the action
		done : []					// 1D array of boolean to mark if action is done
	};

	speed = 1;
	time = 0;
	max_time = 0;
	value = {						// current value
		/*translate_x: 0,
		translate_y: 0,
		rotate: 0,
		scale_x: 0,
		scale_y: 0,
		depth: 0,
		init_depth: 0,
		alpha: 0*/
	};
	index = {						// current index in update()
		/*translate_x: 0,
		translate_y: 0,
		rotate: 0,
		scale_x: 0,
		scale_y: 0,
		depth: 0,
		init_depth: 0,
		alpha: 0,
		action: 0*/
	};
	loop_count = 0;
	current_loop = 0;
	current_anim = 0;

	debug = false;					// debug mode
	is_relative = _is_relative;     // modify the value directly to the instance
	is_playing = false;
	is_reversed = false;
	is_looping = false;
	key_times_equal = true;			// mark if all key_times are equal
	skip_update = false;
	
	on_start = undefined;
	on_end = undefined;

	// guard clause
	if (debug) {
		if (!(instance_exists(_ref) || is_struct(_ref) || _ref == noone)) {
			show_debug_message($"NexaFlux WARNING: Invalid reference ({ref}). Defaulting to `noone`.");
			ref = noone;
		}
		if (!is_bool(_is_relative)) {
			show_debug_message($"NexaFlux WARNING: Invalid `is_relative` value ({_is_relative}). Defaulting to `false`.");
			is_relative = false;
		}
		if (!(is_struct(_data) || is_numeric(_data))) {
			if (is_struct(_data)) {
				show_debug_message($"NexaFlux WARNING: Invalid `data` struct ({_data}). No data will be added.");
			} else {
				show_debug_message($"NexaFlux WARNING: Invalid `data` value ({_data}). No data will be added.");
			}
		}
		if (!is_struct(_actions)) {
			show_debug_message($"NexaFlux WARNING: Invalid `actions` struct ({_actions}). No actions will be added.");
		}
	}

	// get data, actions, and options from preset
	var preset_data = (is_numeric(_data)) ? (new NexaFluxPreset(round(_data), _actions)) : _data;
	set_anim_data(preset_data, _actions, true);

	// getter methods
	#region
	static get_val = function(type) {
		switch (type) {
			case NEXAFLUX_VALUE.TRANSLATE_X: return value.translate_x;
			case NEXAFLUX_VALUE.TRANSLATE_Y: return value.translate_y;
			case NEXAFLUX_VALUE.ROTATE: return value.rotate;
			case NEXAFLUX_VALUE.SCALE_X: return value.scale_x;
			case NEXAFLUX_VALUE.SCALE_Y: return value.scale_y;
			case NEXAFLUX_VALUE.DEPTH: return value.depth;
			case NEXAFLUX_VALUE.INIT_DEPTH: return value.init_depth;
			case NEXAFLUX_VALUE.ALPHA: return value.alpha;
		}
	}
	
	static get_index = function(type) {
		switch (type) {
			case NEXAFLUX_VALUE.TRANSLATE_X: return index.translate_x;
			case NEXAFLUX_VALUE.TRANSLATE_Y: return index.translate_y;
			case NEXAFLUX_VALUE.ROTATE: return index.rotate;
			case NEXAFLUX_VALUE.SCALE_X: return index.scale_x;
			case NEXAFLUX_VALUE.SCALE_Y: return index.scale_y;
			case NEXAFLUX_VALUE.DEPTH: return index.depth;
			case NEXAFLUX_VALUE.INIT_DEPTH: return index.init_depth;
			case NEXAFLUX_VALUE.ALPHA: return index.alpha;
		}
	}

	static get_x = function() {
		return value.translate_x;
	}

	static get_y = function() {
		return value.translate_y;
	}

	static get_rot = function() {
		return value.rotate;
	}

	static get_scale_x = function() {
		return value.scale_x;
	}

	static get_scale_y = function() {
		return value.scale_y;
	}

	static get_depth = function() {
		return value.depth;
	}

	static get_alpha = function() {
		return value.alpha;
	}
	#endregion

	// setter methods
	#region
	static set_values = function(data) {
		if (is_array(data)) {
			for (var i = 0; i < array_length(data); i++) {
				switch (data[i]) {
					case NEXAFLUX_VALUE.TRANSLATE_X: ref.x = (is_relative) ? ref.x + value.translate_x : value.translate_x; break;
					case NEXAFLUX_VALUE.TRANSLATE_Y: ref.y = (is_relative) ? ref.y + value.translate_y : value.translate_y; break;
					case NEXAFLUX_VALUE.ROTATE: ref.image_angle = (is_relative) ? ref.image_angle + value.rotate : value.rotate; break;
					case NEXAFLUX_VALUE.SCALE_X: ref.image_xscale = (is_relative) ? ref.image_xscale + value.scale_x : value.scale_x; break;
					case NEXAFLUX_VALUE.SCALE_Y: ref.image_yscale = (is_relative) ? ref.image_yscale + value.scale_y : value.scale_y; break;
					case NEXAFLUX_VALUE.DEPTH: ref.depth = (is_relative) ? ref.depth + value.depth : value.depth; break;
					case NEXAFLUX_VALUE.INIT_DEPTH: ref.init_depth = (is_relative) ? ref.init_depth + value.init_depth : value.init_depth; break;
					case NEXAFLUX_VALUE.ALPHA: ref.image_alpha = (is_relative) ? ref.image_alpha + value.alpha : value.alpha; break;
				}
			}
		} else if (is_struct(data)) {
			set_values(data.types);
		} else {
			show_debug_message($"NexaFlux WARNING: Invalid `data` value ({data}). No data will be set.");
		}
	}

	static set_val = function(type, val) {
		switch (type) {
			case NEXAFLUX_VALUE.TRANSLATE_X: value.translate_x = val; break;
			case NEXAFLUX_VALUE.TRANSLATE_Y: value.translate_y = val; break;
			case NEXAFLUX_VALUE.ROTATE: value.rotate = val; break;
			case NEXAFLUX_VALUE.SCALE_X: value.scale_x = val; break;
			case NEXAFLUX_VALUE.SCALE_Y: value.scale_y = val; break;
			case NEXAFLUX_VALUE.DEPTH: value.depth = val; break;
			case NEXAFLUX_VALUE.INIT_DEPTH: value.init_depth = val; break;
			case NEXAFLUX_VALUE.ALPHA: value.alpha = val; break;
		}
	}

	static set_index = function(type, val) {
		switch (type) {
			case NEXAFLUX_VALUE.TRANSLATE_X: index.translate_x = val; break;
			case NEXAFLUX_VALUE.TRANSLATE_Y: index.translate_y = val; break;
			case NEXAFLUX_VALUE.ROTATE: index.rotate = val; break;
			case NEXAFLUX_VALUE.SCALE_X: index.scale_x = val; break;
			case NEXAFLUX_VALUE.SCALE_Y: index.scale_y = val; break;
			case NEXAFLUX_VALUE.DEPTH: index.depth = val; break;
			case NEXAFLUX_VALUE.INIT_DEPTH: index.init_depth = val; break;
			case NEXAFLUX_VALUE.ALPHA: index.alpha = val; break;
		}
	}

	static set_x = function(val) {
		value.translate_x = val;
	}

	static set_y = function(val) {
		value.translate_y = val;
	}

	static set_rot = function(val) {
		value.rotate = val;
	}

	static set_scale_x = function(val) {
		value.scale_x = val;
	}

	static set_scale_y = function(val) {
		value.scale_y = val;
	}

	static set_depth = function(val) {
		value.depth = val;
	}

	static set_alpha = function(val) {
		value.alpha = val;
	}

	static set_anim_data = function(data_struct, action_struct = undefined, apply_val = false) {
		// data_struct can be a struct or a NexaFluxPreset

		// guard clause
		if (!(is_struct(data_struct) || is_numeric(data_struct))) {
			if (is_struct(data_struct)) {
				show_debug_message($"NexaFlux WARNING: Invalid `data_struct` struct ({data_struct}). No data will be set.");
			} else {
				show_debug_message($"NexaFlux WARNING: Invalid `data_struct` value ({data_struct}). No data will be set.");
			}
			return;
		}

		// if data_struct is a preset enum, create a new NexaFluxPreset
		if (is_numeric(data_struct)) {
			current_anim = data_struct;
			data_struct = new NexaFluxPreset(round(data_struct), action_struct);
		}

		var names = struct_get_names(data_struct);
		var props = [];

		// convert names string to NEXAFLUX_PROP
		for (var i = 0; i < array_length(names); i++) {
			if (is_undefined(data_struct[$ names[i]])) {
				if (debug) {
					show_debug_message($"NexaFlux WARNING: Data field `{names[i]}` is undefined. Skipping this field.");
				}
				continue;
			}

			switch (names[i]) {
				case "types": array_push(props, NEXAFLUX_PROP.TYPES); break;
				case "key_times": array_push(props, NEXAFLUX_PROP.KEY_TIMES); break;
				case "values": array_push(props, NEXAFLUX_PROP.VALUES); break;
				case "curves": array_push(props, NEXAFLUX_PROP.CURVES); break;
				case "actions": array_push(props, NEXAFLUX_PROP.ACTIONS); break;
				case "speed": array_push(props, NEXAFLUX_PROP.SPEED); break;
				case "max_time": array_push(props, NEXAFLUX_PROP.MAX_TIME); break;
				case "loop_count": array_push(props, NEXAFLUX_PROP.LOOP_COUNT); break;
				case "is_relative": array_push(props, NEXAFLUX_PROP.IS_RELATIVE); break;
				case "is_playing": array_push(props, NEXAFLUX_PROP.IS_PLAYING); break;
				case "is_reversed": array_push(props, NEXAFLUX_PROP.IS_REVERSED); break;
				case "is_looping": array_push(props, NEXAFLUX_PROP.IS_LOOPING); break;
				case "key_times_equal": array_push(props, NEXAFLUX_PROP.KEY_TIMES_EQUAL); break;
				case "on_start": array_push(props, NEXAFLUX_PROP.ON_START); break;
				case "on_end": array_push(props, NEXAFLUX_PROP.ON_END); break;
				default: 
					if (debug) {
						show_debug_message($"NexaFlux WARNING: Unknown data field `{names[i]}`. Skipping this field.");
					}
			}
		}
		
		types = [];
		values = [];
		key_times = [];
		curves = [];

		action.funcs = [];
		action.times = [];
		action.args = [];
		action.done = [];
		
		on_end = undefined;
		
		for (var i = 0; i < array_length(props); i++) {
			switch (props[i]) {
				case NEXAFLUX_PROP.TYPES: types = data_struct.types; break;
				case NEXAFLUX_PROP.KEY_TIMES: key_times = data_struct.key_times; break;
				case NEXAFLUX_PROP.CURVES: curves = data_struct.curves; break;
				case NEXAFLUX_PROP.VALUES: 
					values = data_struct.values;

					// set default values and indexes
					var val_name = "";
					for (var j = 0; j < array_length(data_struct.types); j++) {
						switch (data_struct.types[j]) {
							case NEXAFLUX_VALUE.TRANSLATE_X: val_name = "translate_x"; break;
							case NEXAFLUX_VALUE.TRANSLATE_Y: val_name = "translate_y"; break;
							case NEXAFLUX_VALUE.ROTATE: val_name = "rotate"; break;
							case NEXAFLUX_VALUE.SCALE_X: val_name = "scale_x"; break;
							case NEXAFLUX_VALUE.SCALE_Y: val_name = "scale_y"; break;
							case NEXAFLUX_VALUE.DEPTH: val_name = "depth"; break;
							case NEXAFLUX_VALUE.INIT_DEPTH: val_name = "init_depth"; break;
							case NEXAFLUX_VALUE.ALPHA: val_name = "alpha"; break;
						}
						
						//struct_set_from_hash(value, variable_get_hash(val_name), data_struct.values[j][0][0]);
						//struct_set_from_hash(index, variable_get_hash(val_name), 0);
						value[$ val_name] = data_struct.values[j][0][0];
						index[$ val_name] = 0;

						if (is_reversed) {
							//struct_set_from_hash(value, variable_get_hash(val_name), data_struct.values[j][array_length(data_struct.values[j]) - 1][0]);
							//struct_set_from_hash(index, variable_get_hash(val_name), array_length(data_struct.values[j]) - 1);
							value[$ val_name] = data_struct.values[j][array_length(data_struct.values[j]) - 1][0];
							index[$ val_name] = array_length(data_struct.values[j]) - 1;
						}
					}
					break;
				case NEXAFLUX_PROP.ACTIONS:
					index.action = 0;

					action.times = data_struct.actions.times;
					//action.args = action_struct.args;
					for (var j = 0; j < array_length(data_struct.actions.funcs); j++) {
						array_push(action.funcs, [data_struct.actions.funcs[j]]);
						array_push(action.args, [data_struct.actions.args[j]]);
						array_push(action.done, false);
					}
					
					delete data_struct.actions;

					/*action.funcs = data_struct.actions.funcs;
					action.times = data_struct.actions.times;
					action.args = data_struct.actions.args;
					
					for (var j = 0; j < array_length(data_struct.actions.funcs); j++) {
						array_push(action.done, false);

						if (_actions[i].time > max_time) {
							max_time = _actions[i].time;
						}
					}*/
					break;
				case NEXAFLUX_PROP.MAX_TIME: max_time = data_struct.max_time; break;
				case NEXAFLUX_PROP.LOOP_COUNT: loop_count = data_struct.loop_count; break;
				case NEXAFLUX_PROP.SPEED: speed = data_struct.speed; break;
				case NEXAFLUX_PROP.IS_RELATIVE: is_relative = data_struct.is_relative; break;
				case NEXAFLUX_PROP.IS_PLAYING: is_playing = data_struct.is_playing; break;
				case NEXAFLUX_PROP.IS_REVERSED: is_reversed = data_struct.is_reversed; break;
				case NEXAFLUX_PROP.IS_LOOPING: is_looping = data_struct.is_looping; break;
				case NEXAFLUX_PROP.KEY_TIMES_EQUAL: key_times_equal = data_struct.key_times_equal; break;
				case NEXAFLUX_PROP.ON_START: on_start = data_struct.on_start; break;
				case NEXAFLUX_PROP.ON_END: on_end = data_struct.on_end; break;
				default: show_debug_message($"NexaFlux WARNING: Unknown data field `{names[i]}`. Skipping this field.");
			}
			
		}

		// set max_time
		if (key_times_equal) {
			max_time = key_times[0][array_length(key_times[0]) - 1];
		} else {
			for (var i = 0; i < array_length(key_times); i++) {
				for (var j = 0; j < array_length(key_times[i]); j++) {
					if (key_times[i][j] > max_time) {
						max_time = key_times[i][j];
					}
				}
			}
		}

		// update values
		if (apply_val) {
			for (var i = 0; i < array_length(data_struct.types); i++) {
				set_val(data_struct.types[i], data_struct.values[i][!is_reversed ? 0 : (array_length(values[i]) - 1)][0])
			}
		}
		set_values(data_struct);

		if (instance_exists(ref)) {
			if (ref.attr.dir_x == -1) {
				ref.image_yscale *= -1;
				ref.image_angle = 180 - ref.image_angle;
			}
		}
		
		delete data_struct;

		// set max_time and reset time
		if (key_times_equal) {
			max_time = key_times[0][array_length(key_times[0]) - 1];
		} else {
			for (var i = 0; i < array_length(key_times); i++) {
				for (var j = 0; j < array_length(key_times[i]); j++) {
					if (key_times[i][j] > max_time) {
						max_time = key_times[i][j];
					}
				}
			}
		}
		time = (!is_reversed) ? 0 : max_time;
		current_loop = 0;

		// set default curve type if not set
		if (array_length(curves) == 0) {
			repeat (array_length(key_times)) {
				array_push(curves, NEXAFLUX_CURVES.LINEAR);
			}
		} else if (array_length(curves) != array_length(key_times)) {
			repeat (array_length(key_times) - array_length(curves)) {
				array_push(curves, NEXAFLUX_CURVES.LINEAR);
			}
		}

		for (var i = 0; i < array_length(curves); i++) {
			if (is_array(curves[i])) {
				if (array_length(curves[i]) != array_length(key_times[i])) {
					repeat (array_length(key_times[i]) - array_length(curves[i])) {
						array_push(curves[i], NEXAFLUX_CURVES.LINEAR);
					}
				}
			}
		}
		
		// set actions
		/*if (is_struct(action_struct)) {
			//action.funcs = action_struct.funcs;
			action.times = action_struct.times;
			//action.args = action_struct.args;
			
			for (var i = 0; i < array_length(action_struct.funcs); i++) {
				array_push(action.funcs, [action_struct.funcs[i]]);
				array_push(action.args, [action_struct.args[i]]);
				array_push(action.done, false);
			}
			show_debug_message($"set action = {action}")
			delete action_struct;
		}*/
		
		// reset index
		reset_index();
	}
	#endregion

	// calculation methods
	#region
	static calc_t = function(current_t, next_t, curve_type = NEXAFLUX_CURVES.LINEAR) {
		// current_t = key_times[i][idx]
		// next_t = key_times[i][idx+1]
		switch (curve_type) {
			case NEXAFLUX_CURVES.LINEAR: return (time - current_t) / (next_t - current_t);
			case NEXAFLUX_CURVES.EASE_IN_QUAD: return ease_in_quad((time - current_t) / (next_t - current_t));
			case NEXAFLUX_CURVES.EASE_OUT_QUAD: return ease_out_quad((time - current_t) / (next_t - current_t));
			case NEXAFLUX_CURVES.EASE_INOUT_QUAD: return ease_inout_quad((time - current_t) / (next_t - current_t));
		}
	}
	
	static calc_cubic_bezier = function(p0, c1, c2, p3, t) {
		var u = 1 - t;
		
		return (
			u * u * u * p0 +
			3 * u * u * t * c1 +
			3 * u * t * t * c2 +
			t * t * t * p3
		);
	}
	#endregion

	// management methods
	#region
	static play = function() {
		if (!is_playing) {
			is_playing = true;
		} else {
			show_debug_message("NexaFlux WARNING: Already playing. Please call `pause()` or `stop()` before calling `play()` again.");
		}
	}

	static pause = function() {
		if (is_playing) {
			is_playing = false;
		} else {
			show_debug_message("NexaFlux WARNING: Not playing. Please call `play()` before calling `pause()`.");
		}
	}

	static stop = function() {
		if (is_playing) {
			is_playing = true;
			time = (!is_reversed) ? 0 : max_time;
		} else {
			show_debug_message("NexaFlux WARNING: Not playing. Please call `play()` before calling `stop()`.");
		}
	}
	
	static reset_action = function() {
		for (var i = 0; i < array_length(action.done); i++) {
			action.done[i] = false;
		}
	}

	static reset_index = function() {
		for (var i = 0; i < array_length(types); i++) {
			set_index(types[i], 0);
		}
	}

	static reset_value = function() {
		for (var i = 0; i < array_length(types); i++) {
			set_val(types[i], !is_reversed ? values[i][0][0] : values[i][array_length(values[i]) - 1][0]);
		}
	}
	
	static reset_time = function() {
		time = (!is_reversed) ? 0 : max_time;
		
		if (ref.speed == 0) {
			ref.speed = 1;
		}
	}

	static push_front = function(_type, _value, _key_times, _curves = NEXAFLUX_CURVES.LINEAR) {
		// guard clause
		if (!is_string(type)) {
			show_debug_message("NexaFlux ERROR: Invalid `type`. Please provide a valid string.");
			return;
		}

		if (!(is_array(_value) || is_array(_key_times))) {
			show_debug_message("NexaFlux ERROR: Invalid `value` or `key_times`. Please provide a valid 2D `value` and 1D `key_times` array.");
			return;
		}

		if (!(is_numeric(_curves) || is_array(_curves))) {
			show_debug_message("NexaFlux ERROR: Invalid `curves`. Please provide a valid `NEXAFLUX_CURVES` constant or 1D array of `NEXAFLUX_CURVES` constants.");
			return;
		}

		var index = array_get_index(types, _type);

		if (index < 0) {
			show_debug_message("NexaFlux ERROR: `type` not found. Please provide a valid string.");
			return;
		}

		// insert to front
		array_insert(values[index], 0, _value);
		array_insert(key_times[index], 0, _key_times);
		array_insert(curves[index], 0, _curves);

		// update key_times
		for (var i = 1; i < array_length(key_times[index]); i++) {
			key_times[index][i] += _key_times[index][0];
		}

		// update max_time
		if (key_times_equal) {
			max_time = key_times[index][array_length(key_times[index]) - 1];
		} else {
			if (key_times[index][array_length(key_times[index]) - 1] > max_time) {
				max_time = key_times[index][array_length(key_times[index]) - 1];
			}
		}
	}

	static push_back = function(type, _value, _key_times, _curves = NEXAFLUX_CURVES.LINEAR) {
		// guard clause
		if (!is_string(type)) {
			show_debug_message("NexaFlux ERROR: Invalid `type`. Please provide a valid string.");
			return;
		}

		if (!(is_array(_value) || is_array(_key_times))) {
			show_debug_message("NexaFlux ERROR: Invalid `value` or `key_times`. Please provide a valid 2D `value` and 1D `key_times` array.");
			return;
		}

		if (!(is_numeric(_curves) || is_array(_curves))) {
			show_debug_message("NexaFlux ERROR: Invalid `curves`. Please provide a valid `NEXAFLUX_CURVES` constant or 1D array of `NEXAFLUX_CURVES` constants.");
			return;
		}

		var index = array_get_index(types, type);

		if (index < 0) {
			show_debug_message("NexaFlux ERROR: `type` not found. Please provide a valid string.");
			return;
		}

		// insert to back
		array_push(values[index], _value);
		array_push(key_times[index], _key_times);
		array_push(curves[index], _curves);

		// update max_time
		if (key_times_equal) {
			max_time = key_times[index][array_length(key_times[index]) - 1];
		} else {
			if (key_times[index][array_length(key_times[index]) - 1] > max_time) {
				max_time = key_times[index][array_length(key_times[index]) - 1];
			}
		}
	}

	static call_action = function(index, delete_action = true) {
		// index = index in action.funcs
		if (is_numeric(index)) {
			for (var i = 0; i < array_length(action.funcs[index]); i++) {
				global.nexaflux.actions.call(action.funcs[index][i], action.args[index][i]);
				action.done[index] = true;
			}
		} else if (is_struct(index)) {
			global.nexaflux.actions.call_struct(index);
			if (delete_action && struct_exists(index, "args")) {
				delete index;
			}
		}
	}

	static morph = function(anim_preset, type = NEXAFLUX_MORPH.NONE, morph_time = 0, morph_curve = NEXAFLUX_CURVES.LINEAR, action_struct = undefined) {
		// anim_preset = NEXAFLUX_PRESET or struct
		// type = NEXAFLUX_MORPH
		// morph_time = time in seconds to morph the animation (SHIFT)(0.1), offset index of the replaced keyframe (REPLACE)([0, 0.1]), or the time to append the new animation (APPEND)(0.1)
		// morph_curve = NEXAFLUX_CURVES

		// guard clause
		if (!(is_numeric(anim_preset) || is_struct(anim_preset))) {
			show_debug_message($"NexaFlux ERROR: Invalid `anim_preset` ({anim_preset}). Please provide a valid `NEXAFLUX_PRESET` or struct.");
			return;
		}

		if (!is_numeric(type)) {
			show_debug_message($"NexaFlux WARNING: Invalid `type` value ({type}). Defaulting to `NEXAFLUX_MORPH.NONE`.");
			type = NEXAFLUX_MORPH.NONE;
		}

		if (!(is_numeric(morph_time) || is_array(morph_time))) {
			show_debug_message($"NexaFlux WARNING: Invalid `morph_time` value ({morph_time}). Defaulting to 0.");
			morph_time = 0;
		}
		if (is_numeric(morph_time)) {
			if (morph_time < 0) {
				show_debug_message($"NexaFlux WARNING: Invalid `morph_time` value ({morph_time}). Morph time must be greater than or equal to 0. Defaulting to 0.");
				morph_time = 0;
			}
		} else {
			repeat (3 - array_length(morph_time)) {
				array_push(morph_time, 0);
			}
			
			if (morph_time[0] < 0 || morph_time[1] < 0 || morph_time[2] < 0) {
				show_debug_message($"NexaFlux WARNING: Invalid `morph_time` value ({morph_time}). Morph time must be greater than or equal to 0. Defaulting to [0, 0, 0].");
				morph_time = [0, 0, 0];
			} 
		}
		
		// get new animation data
		if (!is_undefined(action_struct)) {
			if (is_numeric(morph_time)) {
				if (!is_array(action_struct.times)) {
					action_struct.times += ceil(morph_time * 60);
				} else {
					for (var i = 0; i < array_length(action_struct.times); i++) {
						action_struct.times[i] += ceil(morph_time * 60);
					}
				}
			} else if (is_array(morph_time)) {
				if (!is_array(action_struct.times)) {
					action_struct.times += ceil(morph_time[2] * 60);
				} else {
					for (var i = 0; i < array_length(action_struct.times); i++) {
						action_struct.times[i] += ceil(morph_time[2] * 60);
					}
				}
			}
		}
		
		current_anim = anim_preset;
		var morph_anim = anim_preset;
  		if (is_numeric(morph_anim)) {
			anim_preset = new NexaFluxPreset(round(morph_anim), action_struct);
		}
		

		// get current animation data
		var idx = -1;

		switch (type) {
			case NEXAFLUX_MORPH.SHIFT:
			case NEXAFLUX_MORPH.SHIFT_RESET:
				//show_debug_message($"before morph:\ntypes = {anim_preset.types}\nvalues = {anim_preset.values}\nkey_times = {anim_preset.key_times}\ncurves = {anim_preset.curves}")
				for (var i = 0; i < array_length(anim_preset.types); i++) {
					idx = array_get_index(types, anim_preset.types[i]);

					if (idx >= 0) {
						// push current values to the front of the new animation
						array_insert(anim_preset.values[i], 0, [get_val(anim_preset.types[idx])]);
						array_insert(anim_preset.key_times[i], 0, 0);
						
						// add key times for morphing
						if (morph_time > 0) {
							for (var j = 1; j < array_length(anim_preset.key_times[idx]); j++) {
								anim_preset.key_times[idx][j] += ceil(morph_time * 60); 				// convert to frames (60 fps)
							}
						}
					} else if (debug) {
						show_debug_message($"NexaFlux WARNING: Type `{anim_preset.types[i]}` not found in current animation. Skipping this type.");
					}
					
				}

				array_insert(anim_preset.curves, 0, morph_curve);
				if (type == NEXAFLUX_MORPH.SHIFT_RESET) {
					anim_preset.on_end = new NexaFluxAction(NEXAFLUX_ACTION.RESET_ORIGINAL, [current_anim]);
				}
				//show_debug_message($"after morph:\ntypes = {anim_preset.types}\nvalues = {anim_preset.values}\nkey_times = {anim_preset.key_times}\ncurves = {anim_preset.curves}")
			break;

			case NEXAFLUX_MORPH.REPLACE:
			case NEXAFLUX_MORPH.REPLACE_RESET:
				morph_time = is_array(morph_time) ? morph_time : [0, 0.1, 0];	// skipped keyframe, time for single val, extra time for all key_times
				
				var skip_index = morph_time[0];
				var skipped_keytime = 0;
				
				for (var i = 0; i < array_length(types); i++) {
					idx = array_get_index(anim_preset.types, types[i]);
					
					if (idx >= 0) {
						for (var j = 0; j < array_length(anim_preset.key_times[i]); j++) {
							if (j <= skip_index) {
								skipped_keytime += anim_preset.key_times[i][j];
							} else {
								anim_preset.key_times[i][j] = anim_preset.key_times[i][j] - skipped_keytime + ceil(morph_time[2] * 60);
							}
						}

						if (array_length(anim_preset.values[i]) > 1) {
							anim_preset.values[i][skip_index] = [get_val(types[i])];
						} else {
							array_insert(anim_preset.values[i], 0, [get_val(types[i])]);
							array_insert(anim_preset.key_times[i], 0, 0);
							anim_preset.key_times[i][1] += ceil(morph_time[1] * 60);
						}
					}
				}
				
				anim_preset.curves[skip_index] = morph_curve;

				for (i = skip_index - 1; i >= 0; i--) {
					array_delete(anim_preset.types, i, 1);
					array_delete(anim_preset.values, i, 1);
					array_delete(anim_preset.key_times, i, 1);
					array_delete(anim_preset.curves, i, 1);
				}
				
				if (type == NEXAFLUX_MORPH.REPLACE_RESET) {
					anim_preset.on_end = new NexaFluxAction(NEXAFLUX_ACTION.RESET_ORIGINAL, [current_anim]);
				}
			break;

			case NEXAFLUX_MORPH.APPEND:
			case NEXAFLUX_MORPH.APPEND_RESET:
				for (var i = 0; i < array_length(anim_preset.types); i++) {
					idx = array_get_index(types, anim_preset.types[i]);
					
					if (idx >= 0) {
						// push current values to the back of the new animation
						values[idx] = array_concat(values[idx], anim_preset.values[i]);

						// add key times for morphing
						for (var j = 0; j < array_length(anim_preset.key_times[i]); j++) {
							anim_preset.key_times[idx][j] += ceil(morph_time * 60) + max_time;
						}

						key_times = array_concat(key_times, anim_preset.key_times[i]);
					} else {
						array_push(types, anim_preset.types[i]);
						array_push(values, anim_preset.values[i]);
						array_push(key_times, anim_preset.key_times[i]);

						for (var j = 0; j < array_length(anim_preset.key_times[i]); j++) {
							anim_preset.key_times[array_length(key_times) - 1][j] += ceil(morph_time * 60) + max_time;
						}
					}
				}

				curves = array_concat(curves, anim_preset.curves);

				max_time += ceil(morph_time * 60) + anim_preset.max_time;
				
				if (type == NEXAFLUX_MORPH.APPEND_RESET) {
					anim_preset.on_end = new NexaFluxAction(NEXAFLUX_ACTION.RESET_ORIGINAL, [current_anim]);
				}
			break;
		}
		
		// set new animation data
		if (!(
			type == NEXAFLUX_MORPH.APPEND || 
			type == NEXAFLUX_MORPH.APPEND_RESET
		)) {
  			set_anim_data(anim_preset);
		}
	}
	#endregion

	// update method
	static update = function() {
		if (!is_playing) return;
		if (skip_update && loop_count == -1) return;

		// update time
		if (instance_exists(ref) || struct_exists(ref, "speed")) {
			speed = ref.speed;
		}
		
		var spd_mult = 1;
		switch (ref.bots_parent) {
			case anim_speed_team: spd_mult = gamespd_get_team(); break;
			case anim_speed_enemy: spd_mult = gamespd_get_enemy(); break;
		}

		if (!is_reversed) {
			if (loop_count >= 1 && current_loop >= loop_count && time >= max_time) return;
			
			time += delta_t(speed) * spd_mult;
			time = min(time, max_time);
			
			if (time >= max_time) {
				if (on_end != undefined) {
					call_action(on_end);
					
					if ((loop_count >= 1 && current_loop + 1 >= loop_count) || loop_count == -1) {
						on_end = undefined;
					}
				}
				
				if ((loop_count >= 1 && current_loop < loop_count) || loop_count == -1) {
					current_loop++;
					
					if (current_loop < loop_count || loop_count == -1) {
						time = 0;
						reset_action();
						reset_index();
						reset_value();
					}
				}
			}
		} else {
			if (loop_count >= 1 && current_loop >= loop_count && time <= 0) return;
			
			time -= delta_t(speed) * spd_mult;
			time = max(time, 0);
			
			if (time <= 0) {
				if (on_end != undefined) {
					call_action(on_end);
					
					if ((loop_count >= 1 && current_loop + 1 >= loop_count) || loop_count == -1) {
						on_end = undefined;
					}
				}
				
				if ((loop_count >= 1 && current_loop < loop_count) || loop_count == -1) {
					current_loop++;
					
					if (current_loop < loop_count || loop_count == -1) {
						time = max_time;
						reset_action();
						reset_index();
						reset_value();
					}
				}
			}
		}
		
		// calculate current value
		if (!skip_update) {
			var idx = 0;
			var t = 0;
		
			for (var i = 0; i < array_length(types); i++) {
				switch (types[i]) {
					case NEXAFLUX_VALUE.TRANSLATE_X: idx = index.translate_x; break;
					case NEXAFLUX_VALUE.TRANSLATE_Y: idx = index.translate_y; break;
					case NEXAFLUX_VALUE.ROTATE: idx = index.rotate; break;
					case NEXAFLUX_VALUE.SCALE_X: idx = index.scale_x; break;
					case NEXAFLUX_VALUE.SCALE_Y: idx = index.scale_y; break;
					case NEXAFLUX_VALUE.DEPTH: idx = index.depth; break;
					case NEXAFLUX_VALUE.INIT_DEPTH: idx = index.init_depth; break;
					case NEXAFLUX_VALUE.ALPHA: idx = index.alpha; break;
				}
			
				if (!is_reversed) {			// normal
					if (array_length(key_times[i]) > 1) {		// interpolated
						// update current index
						while (idx + 1 < array_length(key_times[i]) && time >= key_times[i][idx + 1]) {
							idx++;
							set_index(types[i], idx);
						}
					
						// calculate current value
						if (idx < array_length(key_times[i]) - 1) {
							t = calc_t(key_times[i][idx], key_times[i][idx+1], (is_array(curves[i]) ? curves[i][idx] : curves[i]));
						
							if (array_length(values[i][idx]) == 1 && 
								array_length(values[i][idx+1]) == 1) {			// linear interpolation
									switch (types[i]) {
										case NEXAFLUX_VALUE.DEPTH:				// snap
											if (value.depth != values[i][idx][0]) {
												set_val(types[i], values[i][idx][0]);
											}
											break;
										case NEXAFLUX_VALUE.INIT_DEPTH:			// snap
											if (value.init_depth != values[i][idx][0]) {
												set_val(types[i], values[i][idx][0]);
											}
											break;
										default: 
											set_val(types[i], lerp(values[i][idx][0], values[i][idx+1][0], t));
									}
							} else {											// bezier interpolation
								set_val(types[i], calc_cubic_bezier(
									values[i][idx][0],
									((array_length(values[i][idx]) > 1) ? values[i][idx][1] : values[i][idx][0]),
									((array_length(values[i][idx+1]) > 2) ? values[i][idx+1][2] : values[i][idx+1][0]),
									values[i][idx+1][0],
									t
								));
							}
						} else {								// fixed
							set_val(types[i], values[i][array_length(key_times[i]) - 1][0]);
						}
					} else {
						set_val(types[i], values[i][0][0]);
					}
				} else {					// reversed
					if (array_length(key_times[i]) > 1) {		// interpolated
						// update current index
						while (idx > 0 && time <= key_times[i][idx - 1]) {
							idx--;
							set_index(types[i], idx);
						}

						// calculate current value
						if (idx > 0) {
							t = calc_t(key_times[i][idx-1], key_times[i][idx], (is_array(curves[i]) ? curves[i][idx-1] : curves[i]));
						
							if (array_length(values[i][idx-1]) == 1 && 
								array_length(values[i][idx]) == 1) {			// linear interpolation
									switch (types[i]) {
										case NEXAFLUX_VALUE.DEPTH:
											if (value.depth != values[i][idx][0]) {
												set_val(types[i], values[i][idx][0]);
											}
											break;
										case NEXAFLUX_VALUE.INIT_DEPTH:
											if (value.init_depth != values[i][idx][0]) {
												set_val(types[i], values[i][idx][0]);
											}
											break;
										default: 
											set_val(types[i], lerp(values[i][idx-1][0], values[i][idx][0], t));
									}
							} else {											// bezier interpolation
								set_val(types[i], calc_cubic_bezier(
									values[i][idx-1][0],
									((array_length(values[i][idx-1]) > 1) ? values[i][idx-1][1] : values[i][idx-1][0]),
									((array_length(values[i][idx]) > 2) ? values[i][idx][2] : values[i][idx][0]),
									values[i][idx][0],
									t
								));
							}
						} else {
							set_val(types[i], values[i][idx][0]);
						}
					} else {									// fixed
						set_val(types[i], values[i][0][0]);
					}
				}
			}

			// apply value to instance
			set_values(types);
			
			// execute actions
			if (struct_exists(index, "action") && array_length(action.times) > 0) {
				idx = index.action;

				if (!is_reversed) {
					if (array_length(action.times) > 1) {
						while (idx + 1 < array_length(action.times) && time >= action.times[idx + 1]) {
							idx++;
							index.action = idx;
						}
					}
					
					if (time >= action.times[idx] && !action.done[idx]) {
						call_action(idx);
					}
				} else {
					if (array_length(action.times) > 1) {
						while (idx > 0 && time <= action.times[idx - 1]) {
							idx--;
							index.action = idx;
						}
					}

					if (time <= action.times[idx]) {
						if (!action.done[idx]) {
							call_action(idx);
						}
					}
				}
			}
		}
	}
}


function nexaflux_create(nexaflux_const) {
	// guard clause
	if (!is_struct(nexaflux_const)) {
		show_debug_message("NexaFlux ERROR: Invalid NexaFlux constructor. Please provide a valid constructor function.");
		return;
	}

	if (global.nexaflux.enabled) {
		array_push(global.nexaflux.ref, nexaflux_const);
	} else {
		show_debug_message("NexaFlux ERROR: NexaFlux is not enabled. Please call `nexaflux_init()` first.");
		return;
	}

	return nexaflux_const;
}


function nexaflux_destroy(nexaflux_const) {
	// guard clause
	if (!is_struct(nexaflux_const)) {
		show_debug_message("NexaFlux ERROR: Invalid NexaFlux constructor. Please provide a valid constructor function.");
		return;
	}

	if (global.nexaflux.enabled) {
		var index = array_get_index(global.nexaflux.ref, nexaflux_const);

		if (index >= 0) {
			array_delete(global.nexaflux.ref, index, 1);
			delete nexaflux_const;
		} else {
			show_debug_message("NexaFlux ERROR: NexaFlux constructor is not found. Please provide a valid constructor function.");
		}
	} else {
		show_debug_message("NexaFlux ERROR: NexaFlux is not enabled. Please call `nexaflux_init()` first.");
		return;
	}
}


function nexaflux_cleanup() {
	if (global.nexaflux.enabled) {
		for (var i = array_length(global.nexaflux.ref) - 1; i >= 0; i--) {
			nexaflux_destroy(global.nexaflux.ref[i]);
		}
	} else {
		show_debug_message("NexaFlux ERROR: NexaFlux is not enabled. Please call `nexaflux_init()` first.");
		return;
	}
}


function nexaflux_update() {
	// guard clause
	if (!global.nexaflux.enabled) {
		show_debug_message("NexaFlux ERROR: NexaFlux is not enabled. Please call `nexaflux_init()` first.");
		return;
	}

	if (array_length(global.nexaflux.ref) == 0) {
		show_debug_message("NexaFlux ERROR: No NexaFlux animations to update. Please create at least one NexaFlux animation first.");
		return;
	}

	var to_destroy = [];

	for (var i = 0; i < array_length(global.nexaflux.ref); i++) {
		if (!(struct_exists(global.nexaflux.ref[i], "ref") || instance_exists(global.nexaflux.ref[i].ref))) {
			show_debug_message($"NexaFlux WARNING: Invalid NexaFlux animation ({global.nexaflux.ref[i]}). This animation will be destroyed.");
			array_push(to_destroy, global.nexaflux.ref[i]);
			continue;
		}

		global.nexaflux.ref[i].update();
	}

	if (global.nexaflux.auto_destroy) {
		for (var i = array_length(to_destroy) - 1; i >= 0; i--) {
			nexaflux_destroy(to_destroy[i]);
		}
	}
}
