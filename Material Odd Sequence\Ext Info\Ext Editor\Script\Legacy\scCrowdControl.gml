function crowd_control(class, hit_list, target, atk_type, cc_type, cc_atk_power, cc_duration, show_text = global.show_cc, dir = atk_dir) {
	/*
	atk_type = 1-basic_atk, 2-special_mv, 3-ultimate
	cc_type = 1-flinch, 2-bind, 3-knockback, 4-knock_airborne, 5-knockdown
			  6-silence, 7-stun, 8-paralyze, 9-neutralize
	*/
	if (class == 0) {
		class = owner;
	};
	else {
		var class_list = [0, o<PERSON><PERSON><PERSON>r, oArcher, oMedic];
		class = class_list[class];
	};
	
	if (atk_type == 1) {
		var cc_power = (class.cc_power + class.basic_atk_power)/100;
		var cc_eff = (100 + class.basic_atk_eff)/100;
	};
	else if (atk_type == 2) {
		var cc_power = (class.cc_power + class.sp_move_power)/100;
		var cc_eff = (100 + class.sp_move_eff)/100;
	};
	else if (atk_type == 3) {
		var cc_power = (class.cc_power + class.ultimate_power)/100;
		var cc_eff = (100 + class.ultimate_eff)/100;
	};
	
	for (var i = 0; i < target; i++) {
		if (hit_list[| i].defeated == 0) {
			if (cc_type == 1) {
				if (hit_list[| i].dashing == 0) {
					if (hit_list[| i].flinch_immunity == 0) {
						if (hit_list[| i].fixed_ignore_interruption < 95) {
							hit_list[| i].flinched = 1;
						};
						else {
							if (hit_list[| i].ignore_interruption < 95) {
								hit_list[| i].flinched = 1;
							};
						};
						var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
						cc_text.cc_type = cc_type;
						cc_text.vspeed -= random_range(0, 3);
						cc_text.hspeed += random_range(-2, 2);
					};
					else if (show_text >= 1) {
						var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
						cc_text.cc_type = 0;
						cc_text.vspeed -= random_range(0, 3);
						cc_text.hspeed += random_range(-2, 2);
					};
				};
			};
			else if (cc_type == 2) {
				if (hit_list[| i].dashing == 0) {
					if (hit_list[| i].bind_immunity == 0) {
						if (hit_list[| i].binded == 0) {
							hit_list[| i].binded = 1;
							hit_list[| i].last_spd = hit_list[| i].hspeed;
							hit_list[| i].stop = 1;
							var cc = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oCC);
							cc.cc_type = cc_type;
							cc.stick_to = hit_list[| i];
							cc.real_spd = hit_list[| i].real_spd;
							cc.sprite_index = hit_list[| i].sprite_index;
							with (cc) {
								alarm_set(0, (room_speed * (cc_duration * cc_power))*((100-hit_list[| i].cc_res)/100));
							};
						};
						else {
							var overwrite_list = ds_list_create();
							var overwrite = instance_place_list(x, y, oCC, overwrite_list, true);
							for (var j = 0; j < overwrite; j++) {
								if (overwrite_list[| j].cc_type = cc_type) {
									with (overwrite_list[| j]) {
										alarm_set(0, (room_speed * (cc_duration * cc_power))*((100-hit_list[| i].cc_res)/100));
									};
								};	
							};
							ds_list_destroy(overwrite_list);
						};
						var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
						cc_text.cc_type = cc_type;
						cc_text.vspeed -= random_range(0, 3);
						cc_text.hspeed += random_range(-2, 2);
					};
					else if (show_text >= 1) {
						var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
						cc_text.cc_type = 0;
						cc_text.vspeed -= random_range(0, 3);
						cc_text.hspeed += random_range(-2, 2);
					};
				};
			};
			else if (cc_type == 3) {
				if (hit_list[| i].dashing == 0 /*&& hit_list[| i].interrupted == 1*/) {
					if (hit_list[| i].knockback_immunity == 0) {
						hit_list[| i].knocked_back = 1;
						if (dir == 1) {
							hit_list[| i].hspeed = ((20 * (cc_atk_power/100)) * cc_power)*((100-hit_list[| i].cc_res)/100);
						};
						else if (dir == 2) {
							hit_list[| i].hspeed = -((20 * (cc_atk_power/100)) * cc_power)*((100-hit_list[| i].cc_res)/100);
						};
						else if (dir == 3) {
							if (hit_list[| i].x >= x) {
								hit_list[| i].hspeed = ((20 * (cc_atk_power/100)) * cc_power)*((100-hit_list[| i].cc_res)/100);
							};
							else {
								hit_list[| i].hspeed = -((20 * (cc_atk_power/100)) * cc_power)*((100-hit_list[| i].cc_res)/100);
							};
						};
						else if (dir == 4) {
							hit_list[| i].vspeed += ((20 * (cc_atk_power/100)) * cc_power)*((100-hit_list[| i].cc_res)/100);
						};
						hit_list[| i].last_spd = hit_list[| i].hspeed;
						hit_list[| i].stop = 1;
						var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
						cc_text.cc_type = cc_type;
						cc_text.vspeed -= random_range(0, 3);
						cc_text.hspeed += random_range(-2, 2);
					};
					else if (show_text >= 1) {
						var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
						cc_text.cc_type = 0;
						cc_text.vspeed -= random_range(0, 3);
						cc_text.hspeed += random_range(-2, 2);
					};
				};
			};
			else if (cc_type == 4) {
				if (hit_list[| i].dashing == 0 /*&& hit_list[| i].interrupted == 1*/) {
					if (hit_list[| i].knock_airborne_immunity == 0) {
						hit_list[| i].knocked_airborne = 1;
						hit_list[| i].y -= 4;
						hit_list[| i].vspeed = -((20 * (cc_atk_power/100)) * cc_power)*((100-hit_list[| i].cc_res)/100);
						var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
						cc_text.cc_type = cc_type;
						cc_text.vspeed -= random_range(0, 3);
						cc_text.hspeed += random_range(-2, 2);
					};
					else if (show_text >= 1) {
						var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
						cc_text.cc_type = 0;
						cc_text.vspeed -= random_range(0, 3);
						cc_text.hspeed += random_range(-2, 2);
					};
				};
			};
			else if (cc_type == 5) {
				if (hit_list[| i].dashing == 0 /*&& hit_list[| i].interrupted == 1*/) {
					if (hit_list[| i].knockdown_immunity == 0) {
						if (hit_list[| i].knocked_down == 0) {
							hit_list[| i].knocked_down = 1;
							if (dir == 1) {
								hit_list[| i].hspeed = ((10 * (cc_atk_power/100)) * cc_power)*((100-hit_list[| i].cc_res)/100);
							};
							else if (dir == 2) {
								hit_list[| i].hspeed = -((10 * (cc_atk_power/100)) * cc_power)*((100-hit_list[| i].cc_res)/100);
							};
							else if (dir == 3) {
								if (hit_list[| i].x >= x) {
									hit_list[| i].hspeed = ((10 * (cc_atk_power/100)) * cc_power)*((100-hit_list[| i].cc_res)/100);
								};
								else {
									hit_list[| i].hspeed = -((10 * (cc_atk_power/100)) * cc_power)*((100-hit_list[| i].cc_res)/100);
								};
							};
							hit_list[| i].y -= 4;
							hit_list[| i].vspeed = -((10 * (cc_atk_power/100)) * cc_power)*((100-hit_list[| i].cc_res)/100);
							var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
							cc_text.cc_type = cc_type;
							cc_text.vspeed -= random_range(0, 3);
							cc_text.hspeed += random_range(-2, 2);
							var cc = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oCC);
							cc.cc_type = cc_type;
							cc.stick_to = hit_list[| i];
							cc.sprite_index = hit_list[| i].sprite_index;
							with (cc) {
								alarm_set(0, (room_speed * (cc_duration * cc_power))*((100-hit_list[| i].cc_res)/100));
							};
						};
						else {
							hit_list[| i].y -= 1;
							hit_list[| i].vspeed = -((10 * (cc_atk_power/100)) * cc_power)*((100-hit_list[| i].cc_res)/100);
							var overwrite_list = ds_list_create();
							var overwrite = instance_place_list(x, y, oCC, overwrite_list, true);
							for (var j = 0; j < overwrite; j++) {
								if (overwrite_list[| j].cc_type = cc_type) {
									with (overwrite_list[| j]) {
										alarm_set(0, (room_speed * (cc_duration * cc_power))*((100-hit_list[| i].cc_res)/100));
									};
								};	
							};
							ds_list_destroy(overwrite_list);
						};
					};
					else if (show_text >= 1) {
						var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
						cc_text.cc_type = 0;
						cc_text.vspeed -= random_range(0, 3);
						cc_text.hspeed += random_range(-2, 2);
					};
				};
			};
			else if (cc_type == 6) {
				if (hit_list[| i].dashing == 0) {
					if (hit_list[| i].silence_immunity == 0) {
						if (hit_list[| i].silenced == 0) {
							hit_list[| i].silenced = 1;
							var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
							cc_text.cc_type = cc_type;
							cc_text.vspeed -= random_range(0, 3);
							cc_text.hspeed += random_range(-2, 2);
							var cc = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oCC);
							cc.cc_type = cc_type;
							cc.stick_to = hit_list[| i];
							cc.sprite_index = hit_list[| i].sprite_index;
							with (cc) {
								alarm_set(0, (room_speed * (cc_duration * cc_power))*((100-hit_list[| i].cc_res)/100));
							};
						};
						else {
							var overwrite_list = ds_list_create();
							var overwrite = instance_place_list(x, y, oCC, overwrite_list, true);
							for (var j = 0; j < overwrite; j++) {
								if (overwrite_list[| j].cc_type = cc_type) {
									with (overwrite_list[| j]) {
										alarm_set(0, (room_speed * (cc_duration * cc_power))*((100-hit_list[| i].cc_res)/100));
									};
								};	
							};
							ds_list_destroy(overwrite_list);
						};
					};
					else if (show_text >= 1) {
						var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
						cc_text.cc_type = 0;
						cc_text.vspeed -= random_range(0, 3);
						cc_text.hspeed += random_range(-2, 2);
					};
				};
			};
			else if (cc_type == 7) {
				if (hit_list[| i].stun_immunity == 0) {
					if (hit_list[| i].stunned == 0) {
						hit_list[| i].stunned = 1;
						hit_list[| i].interrupted = 1;
						hit_list[| i].last_spd = hit_list[| i].hspeed;
						hit_list[| i].stop = 1;
						var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
						cc_text.cc_type = cc_type;
						cc_text.vspeed -= random_range(0, 3);
						cc_text.hspeed += random_range(-2, 2);
						var cc = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oCC);
						cc.cc_type = cc_type;
						cc.stick_to = hit_list[| i];
						cc.real_spd = hit_list[| i].real_spd;
						cc.sprite_index = hit_list[| i].sprite_index;
						with (cc) {
							alarm_set(0, (room_speed * (cc_duration * cc_power))*((100-hit_list[| i].cc_res)/100));
						};
					};
					else {
						var overwrite_list = ds_list_create();
						var overwrite = instance_place_list(x, y, oCC, overwrite_list, true);
						for (var j = 0; j < overwrite; j++) {
							if (overwrite_list[| j].cc_type = cc_type) {
								with (overwrite_list[| j]) {
									alarm_set(0, (room_speed * (cc_duration * cc_power))*((100-hit_list[| i].cc_res)/100));
								};
							};	
						};
						ds_list_destroy(overwrite_list);
					};
				};
				else if (show_text >= 1) {
					var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
					cc_text.cc_type = 0;
					cc_text.vspeed -= random_range(0, 3);
					cc_text.hspeed += random_range(-2, 2);
				};
			};
			else if (cc_type == 8) {
				if (hit_list[| i].paralyze_immunity == 0) {
					if (hit_list[| i].paralyzed == 0) {
						hit_list[| i].paralyzed = 1;
						hit_list[| i].interrupted = 1;
						hit_list[| i].last_spd = hit_list[| i].hspeed;
						hit_list[| i].stop = 1;
						var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
						cc_text.cc_type = cc_type;
						cc_text.vspeed -= random_range(0, 3);
						cc_text.hspeed += random_range(-2, 2);
						var cc = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oCC);
						cc.cc_type = cc_type;
						cc.stick_to = hit_list[| i];
						cc.real_spd = hit_list[| i].real_spd;
						cc.sprite_index = hit_list[| i].sprite_index;
						with (cc) {
							alarm_set(0, (room_speed * (cc_duration * cc_power))*((100-hit_list[| i].cc_res)/100));
						};
					};
					else {
						var overwrite_list = ds_list_create();
						var overwrite = instance_place_list(x, y, oCC, overwrite_list, true);
						for (var j = 0; j < overwrite; j++) {
							if (overwrite_list[| j].cc_type = cc_type) {
								with (overwrite_list[| j]) {
									alarm_set(0, (room_speed * (cc_duration * cc_power))*((100-hit_list[| i].cc_res)/100));
								};
							};	
						};
						ds_list_destroy(overwrite_list);
					};
				};
				else if (show_text >= 1) {
					var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
					cc_text.cc_type = 0;
					cc_text.vspeed -= random_range(0, 3);
					cc_text.hspeed += random_range(-2, 2);
				};
			};
			else if (cc_type == 9) {
				if (hit_list[| i].dashing == 0) {
					if (hit_list[| i].neutralize_immunity == 0) {
						if (hit_list[| i].neutralized == 0) {
							hit_list[| i].neutralized = 1;
							var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
							cc_text.cc_type = cc_type;
							cc_text.vspeed -= random_range(0, 3);
							cc_text.hspeed += random_range(-2, 2);
							var cc = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -990, oCC);
							cc.cc_type = cc_type;
							cc.stick_to = hit_list[| i];
							cc.sprite_index = hit_list[| i].sprite_index;
							with (cc) {
								alarm_set(0, (room_speed * (cc_duration * cc_power))*((100-hit_list[| i].cc_res)/100));
							};
						};
						else {
							var overwrite_list = ds_list_create();
							var overwrite = instance_place_list(x, y, oCC, overwrite_list, true);
							for (var j = 0; j < overwrite; j++) {
								if (overwrite_list[| j].cc_type = cc_type) {
									with (overwrite_list[| j]) {
										alarm_set(0, (room_speed * (cc_duration * cc_power))*((100-hit_list[| i].cc_res)/100));
									};
								};	
							};
							ds_list_destroy(overwrite_list);
						};
					};
					else if (show_text >= 1) {
						var cc_text = instance_create_depth(hit_list[| i].x, hit_list[| i].y, -991, oCCText);
						cc_text.cc_type = 0;
						cc_text.vspeed -= random_range(0, 3);
						cc_text.hspeed += random_range(-2, 2);
					};
				};
			};
		};
	};
};