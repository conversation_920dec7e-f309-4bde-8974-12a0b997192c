/// @description  Regex Pattern Parser for GML
/// This module implements a basic regex parser that can be extended
/// to support various regex functions

// Define regex pattern tokens
enum REGEX_TOKEN {
    CHAR,           // Regular character
    ANY,            // . (any character)
    START,          // ^ (start of string)
    END,            // $ (end of string)
    DIGIT,          // \d (digit)
    NON_DIGIT,      // \D (non-digit)
    WORD,           // \w (word character)
    NON_WORD,       // \W (non-word character)
    WHITESPACE,     // \s (whitespace)
    NON_WHITESPACE, // \S (non-whitespace)
    GROUP_START,    // (
    GROUP_END,      // )
    OR,             // |
    ESCAPE,         // \ (escape character)
    RANGE_START,    // [
    RANGE_END,      // ]
    RANGE_NOT,      // [^ (negated range)
    QUANTIFIER      // *, +, ?, {n}, {n,}, {n,m}
}

/// @function regex_parse_pattern
/// @param {string} pattern - The regex pattern to parse
/// @returns {array} An array of token objects representing the parsed pattern
function regex_parse_pattern(pattern) {
    var tokens = [];
    var i = 0;
    var len = string_length(pattern);
    
    while (i < len) {
        var char = string_char_at(pattern, i + 1); // GML strings are 1-indexed
        var token = {type: REGEX_TOKEN.CHAR, value: char};
        
        // Process special characters
        switch (char) {
            case ".":
                token.type = REGEX_TOKEN.ANY;
                break;
                
            case "^":
                token.type = REGEX_TOKEN.START;
                break;
                
            case "$":
                token.type = REGEX_TOKEN.END;
                break;
                
            case "(":
                token.type = REGEX_TOKEN.GROUP_START;
                break;
                
            case ")":
                token.type = REGEX_TOKEN.GROUP_END;
                break;
                
            case "|":
                token.type = REGEX_TOKEN.OR;
                break;
                
            case "[":
                // Check if it's a negated range
                if (i + 1 < len && string_char_at(pattern, i + 2) == "^") {
                    token.type = REGEX_TOKEN.RANGE_NOT;
                    i++; // Skip the next character (^)
                } else {
                    token.type = REGEX_TOKEN.RANGE_START;
                }
                break;
                
            case "]":
                token.type = REGEX_TOKEN.RANGE_END;
                break;
                
            case "\\":
                // Handle escape sequences
                if (i + 1 < len) {
                    i++;
                    var nextChar = string_char_at(pattern, i + 1);
                    switch (nextChar) {
                        case "d":
                            token.type = REGEX_TOKEN.DIGIT;
                            break;
                            
                        case "D":
                            token.type = REGEX_TOKEN.NON_DIGIT;
                            break;
                            
                        case "w":
                            token.type = REGEX_TOKEN.WORD;
                            break;
                            
                        case "W":
                            token.type = REGEX_TOKEN.NON_WORD;
                            break;
                            
                        case "s":
                            token.type = REGEX_TOKEN.WHITESPACE;
                            break;
                            
                        case "S":
                            token.type = REGEX_TOKEN.NON_WHITESPACE;
                            break;
                            
                        default:
                            // Escaped character (like \., \*, etc.)
                            token.type = REGEX_TOKEN.CHAR;
                            token.value = nextChar;
                            break;
                    }
                } else {
                    // \ at the end of the pattern is treated as a literal backslash
                    token.type = REGEX_TOKEN.CHAR;
                    token.value = "\\";
                }
                break;
                
            case "*":
            case "+":
            case "?":
                token.type = REGEX_TOKEN.QUANTIFIER;
                token.quantifier = char;
                token.min = (char == "*" || char == "?") ? 0 : 1;
                token.max = (char == "+" || char == "*") ? -1 : 1; // -1 means unlimited
                break;
                
            case "{":
                // Handle {n}, {n,}, {n,m} quantifiers
                token.type = REGEX_TOKEN.QUANTIFIER;
                var quantStr = "";
                var j = i + 1;
                var closeBraceFound = false;
                
                while (j < len) {
                    var quantChar = string_char_at(pattern, j + 1);
                    if (quantChar == "}") {
                        closeBraceFound = true;
                        break;
                    }
                    quantStr += quantChar;
                    j++;
                }
                
                if (closeBraceFound) {
                    i = j; // Update position to after the closing brace
                    
                    // Parse the quantifier
                    if (string_pos(",", quantStr) == 0) {
                        // {n} - Exactly n occurrences
                        token.min = real(quantStr);
                        token.max = token.min;
                    } else {
                        var parts = string_split(quantStr, ",");
                        token.min = real(parts[0]);
                        
                        if (array_length(parts) > 1 && parts[1] != "") {
                            // {n,m} - Between n and m occurrences
                            token.max = real(parts[1]);
                        } else {
                            // {n,} - At least n occurrences
                            token.max = -1; // Unlimited
                        }
                    }
                    token.quantifier = "{" + quantStr + "}";
                } else {
                    // Invalid quantifier, treat as a literal character
                    token.type = REGEX_TOKEN.CHAR;
                }
                break;
        }
        
        array_push(tokens, token);
        i++;
    }
    
    return tokens;
}

/// @function string_split
/// @param {string} str - The string to split
/// @param {string} delimiter - The delimiter to split by
/// @returns {array} An array of substrings
function string_split(str, delimiter) {
    var result = [];
    var pos = string_pos(delimiter, str);
    
    if (pos == 0) {
        array_push(result, str);
        return result;
    }
    
    while (pos > 0) {
        var chunk = string_copy(str, 1, pos - 1);
        array_push(result, chunk);
        str = string_delete(str, 1, pos);
        pos = string_pos(delimiter, str);
    }
    
    if (str != "") {
        array_push(result, str);
    }
    
    return result;
}

/// @function regex_match
/// @param {string} str - The string to match against
/// @param {string} pattern - The regex pattern
/// @returns {boolean} Whether the string matches the pattern
function regex_match(str, pattern) {
    var tokens = regex_parse_pattern(pattern);
    return regex_match_internal(str, tokens, 0, 0);
}

/// @function regex_match_internal
/// @param {string} str - The string to match against
/// @param {array} tokens - The parsed regex pattern tokens
/// @param {real} strPos - The current position in the string
/// @param {real} tokenPos - The current position in the tokens array
/// @returns {boolean} Whether the string matches the pattern
function regex_match_internal(str, tokens, strPos, tokenPos) {
    var strLen = string_length(str);
    var tokenLen = array_length(tokens);
    
    // Base case: reached the end of both the string and pattern
    if (tokenPos >= tokenLen) {
        return strPos >= strLen;
    }
    
    var token = tokens[tokenPos];
    var isMatch = false;
    
    // Handle different token types
    switch (token.type) {
        case REGEX_TOKEN.CHAR:
            if (strPos < strLen && string_char_at(str, strPos + 1) == token.value) {
                isMatch = regex_match_internal(str, tokens, strPos + 1, tokenPos + 1);
            }
            break;
            
        case REGEX_TOKEN.ANY:
            if (strPos < strLen) {
                isMatch = regex_match_internal(str, tokens, strPos + 1, tokenPos + 1);
            }
            break;
            
        case REGEX_TOKEN.START:
            if (strPos == 0) {
                isMatch = regex_match_internal(str, tokens, strPos, tokenPos + 1);
            }
            break;
            
        case REGEX_TOKEN.END:
            if (strPos >= strLen) {
                isMatch = regex_match_internal(str, tokens, strPos, tokenPos + 1);
            }
            break;
            
        case REGEX_TOKEN.DIGIT:
            if (strPos < strLen) {
                var ch = string_char_at(str, strPos + 1);
                if (ch >= "0" && ch <= "9") {
                    isMatch = regex_match_internal(str, tokens, strPos + 1, tokenPos + 1);
                }
            }
            break;
            
        case REGEX_TOKEN.NON_DIGIT:
            if (strPos < strLen) {
                var ch = string_char_at(str, strPos + 1);
                if (!(ch >= "0" && ch <= "9")) {
                    isMatch = regex_match_internal(str, tokens, strPos + 1, tokenPos + 1);
                }
            }
            break;
            
        case REGEX_TOKEN.WORD:
            if (strPos < strLen) {
                var ch = string_char_at(str, strPos + 1);
                if ((ch >= "a" && ch <= "z") || (ch >= "A" && ch <= "Z") || 
                    (ch >= "0" && ch <= "9") || ch == "_") {
                    isMatch = regex_match_internal(str, tokens, strPos + 1, tokenPos + 1);
                }
            }
            break;
            
        case REGEX_TOKEN.NON_WORD:
            if (strPos < strLen) {
                var ch = string_char_at(str, strPos + 1);
                if (!((ch >= "a" && ch <= "z") || (ch >= "A" && ch <= "Z") || 
                      (ch >= "0" && ch <= "9") || ch == "_")) {
                    isMatch = regex_match_internal(str, tokens, strPos + 1, tokenPos + 1);
                }
            }
            break;
            
        case REGEX_TOKEN.WHITESPACE:
            if (strPos < strLen) {
                var ch = string_char_at(str, strPos + 1);
                if (ch == " " || ch == "\t" || ch == "\n" || ch == "\r") {
                    isMatch = regex_match_internal(str, tokens, strPos + 1, tokenPos + 1);
                }
            }
            break;
            
        case REGEX_TOKEN.NON_WHITESPACE:
            if (strPos < strLen) {
                var ch = string_char_at(str, strPos + 1);
                if (!(ch == " " || ch == "\t" || ch == "\n" || ch == "\r")) {
                    isMatch = regex_match_internal(str, tokens, strPos + 1, tokenPos + 1);
                }
            }
            break;
            
        case REGEX_TOKEN.QUANTIFIER:
            // Handle quantifiers - this is a simplified implementation
            var prevToken = tokens[tokenPos - 1];
            var minMatches = token.min;
            var maxMatches = token.max;
            
            // Try all possible matching lengths
            for (var i = minMatches; i <= maxMatches || maxMatches == -1; i++) {
                // TODO: Implement proper quantifier logic
                // This is a placeholder that would need expansion
                isMatch = regex_match_internal(str, tokens, strPos + i, tokenPos + 1);
                if (isMatch) break;
                
                // Avoid infinite loops
                if (strPos + i >= strLen) break;
            }
            break;
            
        // Add other token types as needed
    }
    
    return isMatch;
}

/// @function regex_find_all
/// @param {string} str - The string to search in
/// @param {string} pattern - The regex pattern to search for
/// @returns {array} An array of matched substrings
function regex_find_all(str, pattern) {
    var tokens = regex_parse_pattern(pattern);
    var matches = [];
    var strLen = string_length(str);
    
    // Try matching at each position in the string
    for (var i = 0; i < strLen; i++) {
        var match = regex_find_at_position(str, tokens, i);
        if (match != "") {
            array_push(matches, match);
        }
    }
    
    return matches;
}

/// @function regex_find_at_position
/// @param {string} str - The string to search in
/// @param {array} tokens - The parsed regex pattern tokens
/// @param {real} startPos - The position to start searching from
/// @returns {string} The matched substring or an empty string if no match
function regex_find_at_position(str, tokens, startPos) {
    // This is a placeholder - a full implementation would need to
    // actually match the pattern and return the matched string
    return "";
}

/// @function regex_replace_all
/// @param {string} str - The string to perform replacements on
/// @param {string} pattern - The regex pattern to search for
/// @param {string} replacement - The replacement string
/// @returns {string} The string with all matches replaced
function regex_replace_all(str, pattern, replacement) {
    var matches = regex_find_all(str, pattern);
    
    // This is a simplified implementation - a full version would need
    // to handle replacement patterns, capture groups, etc.
    for (var i = 0; i < array_length(matches); i++) {
        str = string_replace_all(str, matches[i], replacement);
    }
    
    return str;
}
