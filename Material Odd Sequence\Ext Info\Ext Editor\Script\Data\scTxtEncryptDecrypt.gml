// Tutup dan enkripsi file yang dibuka
function file_text_encrypt(file_name) {
	//ini_close();
	var encrypt_str = ini_close();
	var layer1_enc = base64_encode(encrypt_str);
	var layer2_enc = base64_encode(layer1_enc);
	
	if (string_copy(file_name, string_length(file_name) - 3, 4) != ".osq") {
		file_name = string_copy(file_name, 1, string_length(file_name) - 4) + ".osq";
	}
	var file = file_text_open_write(file_name);
	file_text_write_string(file, layer2_enc);
	file_text_close(file);
	if (file_exists(string_copy(file_name, 1, string_length(file_name) - 4) + ".ini")) {
		file_delete(string_copy(file_name, 1, string_length(file_name) - 4) + ".ini");
	}
}

// Buka dan dekripsi file
function file_text_decrypt(file_name) {
	//ini_open(string_copy(file_name, 1, string_length(file_name) - 4) + ".ini");
	if ((string_copy(file_name, string_length(file_name) - 3, 4) == ".osq") ? file_exists(file_name) : file_exists(string_copy(file_name, 1, string_length(file_name) - 4) + ".osq")) {
		if (string_copy(file_name, string_length(file_name) - 3, 4) != ".osq") {
			file_name = string_copy(file_name, 1, string_length(file_name) - 4) + ".osq";
		}
		var file = file_text_open_read(file_name);
		
		var encrypted_str = file_text_read_string(file);
		var layer1_dec = base64_decode(encrypted_str);
		var layer2_dec = base64_decode(layer1_dec);
		file_text_close(file);
	
		ini_open_from_string(layer2_dec);
	}
	else {
		ini_open(string_copy(file_name, 1, string_length(file_name) - 4) + ".ini");
	}
}

// Tarik dan dekripsi file cloud
function file_text_pull_cloud(cloud_type /*, file_name = "all"*/) {
	//cloud_type = 1-steam
	if (cloud_type == 1) {
		/*if (file_name != "all") {
			var raw_data = steam_file_read(file_name);
			var layer1_dec = base64_decode(raw_data);
			var layer2_dec = base64_decode(layer1_dec);
			return layer2_dec;
		}*/
		
		/*if (rewrite) {
			if (file_name != "all") {
				ini_open_from_string(layer2_dec);
				file_text_encrypt(file_name);
			}
			else {*/
				var cloud_files = steam_file_get_list();
				for (var i = 0; i < array_length(cloud_files); i++) {
					if (string_copy(cloud_files[i].file_name, string_length(cloud_files[i].file_name) - 3, 4) == ".osq") {
						var data = steam_file_read(cloud_files[i].file_name);
						var file = file_text_open_write(cloud_files[i].file_name);
						data = base64_encode(data);
						data = base64_encode(data);
						file_text_write_string(file, data);
						file_text_close(file);
					}
				}
			/*}
		}*/
	}
}

// Dorong file cloud
function file_text_push_cloud(cloud_type, file_name = "all", backup = false) {
	//cloud_type = 1-steam
	if (cloud_type == 1) {
		var files = [];
		if (file_name == "all") {
			var current_file = file_find_first("*.osq", fa_none);
			while (current_file != "") {
				array_push(files, current_file);
				current_file = file_find_next();
			}
			file_find_close();
			//show_debug_message(files)
		}
		else {
			array_push(files, file_name);
		}
		
		if (!backup) {
			/*var cloud_files = steam_file_get_list();
			for (var i = 0; i < array_length(cloud_files); i++) {
				if (string_copy(cloud_files[i].file_name, string_length(cloud_files[i].file_name) - 3, 4) == ".txt") {
					if (cloud_files[i].file_name != "steam_appid.txt") {
						steam_file_delete(cloud_files[i].file_name);
					}
				}
			}*/

			for (var i = 0; i < array_length(files); i++) {
				var file_open = file_text_open_read(files[i]);
				var data = file_text_read_string(file_open);
				data = base64_decode(data);
				data = base64_decode(data);
				file_text_close(file_open);
				steam_file_write(files[i], data, string_length(data));
			}
			//show_debug_message(steam_file_get_list())
		}
		/*else {
			var cloud_files = steam_file_get_list();
			for (var i = 0; i < array_length(cloud_files); i++) {
				if (string_copy(cloud_files[i].file_name, string_length(cloud_files[i].file_name) - 3, 4) == ".txt") {
					if (cloud_files[i].file_name != "steam_appid.txt") {
						var data = steam_file_read(cloud_files[i].file_name);
						steam_file_write(cloud_files[i].file_name + ".bak", data, string_length(data));
					}
				}
			}
			for (var j = 0; j < array_length(files); j++) {
				var file_open = file_text_open_read(files[j]);
				var data = file_text_read_string(file_open);
				file_text_close(file_open);
				steam_file_write(files[j], data, string_length(data));
			}
		}*/
	}
}
