// Database
global.db_items = db_create_items();

if (os_type == os_windows || os_type == os_linux || os_type == os_macosx) {
	draw_set_circle_precision(64);
}

if (file_exists("general.osq")) {
	file_text_decrypt("general.osq");

	//Profile
	start_date = ini_read_string("player", "start_date", "0");
	if (start_date == "0") {
		ini_write_string("player", "start_date", string(current_year)+string(current_month)+string(current_day));
	};
	playtime = ini_read_real("player", "playtime", 0);

	//Shop
	daily_reset = 0;
	weekly_reset = 0;
	monthly_reset = 0;
	var to_monthly_avl = ini_read_real("shop", "monthly", 0);
	last_date = ini_read_string("player", "last_date", string(current_year)+string(current_month)+string(current_day));
	last_day_shop = ini_read_real("shop", "last_day", 17);
	last_month_shop = ini_read_real("shop", "last_month", 11);
	last_year_shop = ini_read_real("shop", "last_year", 2020);
	last_date_shop = date_create_datetime(last_year_shop, last_month_shop, last_day_shop, 0, 0, 0);
	last_week_shop = date_get_week(last_date_shop);
	last_weekday_shop = date_get_weekday(last_date_shop);
	if (last_weekday_shop == 0) {
		last_weekday_shop = 7;
	}
	last_week_shop = date_get_week(date_create_datetime(last_year_shop, last_month_shop, last_day_shop-last_weekday_shop+1, 0, 0, 0));
	
	current_date_shop = date_create_datetime(current_year, current_month, current_day, 0, 0, 0);
	current_week_shop = date_get_week(current_date_shop);
	current_year_shop = date_get_year(current_date_shop);
	if (last_year_shop == current_year_shop) {
		week_diff_shop = current_week_shop - last_week_shop;
	}
	else {
		week_diff_shop = 52 - last_week_shop + current_week_shop;
	}
	
	if (date_compare_date(current_date_shop, last_date_shop) == 1 || (last_year_shop < current_year && current_month < last_month_shop)) {
		daily_reset = 1;
		if (week_diff_shop >= 1) {
			weekly_reset = 1;
		}
		if (current_month != to_monthly_avl) {
			monthly_reset = 1;
			ini_write_real("shop", "monthly", current_month);
		}
		ini_write_real("shop", "last_day", current_day);
		ini_write_real("shop", "last_month", current_month);
		ini_write_real("shop", "last_year", current_year);
	};
	/*if (week_diff_shop >= 1) {
		weekly_reset = 1;
		daily_reset = 1;
		ini_write_real("shop", "last_day", current_day);
		ini_write_real("shop", "last_month", current_month);
		ini_write_real("shop", "last_year", current_year);
	};
	if ((current_month != last_month_shop && current_year == current_year_shop) || (current_month == last_month_shop && current_year != current_year_shop)) {
		monthly_reset = 1;
	};*/

	if (daily_reset == 1) {
		var purchaseable = function(row) {
			var valid = false;
			if (is_struct(db_get_value(global.db_items, 13, row))) {
				var attr = db_get_value(global.db_items, 13, row);
				valid = attr.limit.type == 1;
				delete attr;
			}
			return valid;
		}

		var item_num = [];
		for (var i = 3; i <= 4; i++) {
			item_num = array_concat(item_num, db_get_value_group(global.db_items, 2, i, 1, purchaseable));
		}
		for (var i = 0; i < array_length(item_num); i++) {
			if (ini_key_exists("shop", "item"+string(item_num[i]))) {
				ini_write_real("shop", "item"+string(item_num[i]), 0);
			}
		}
	
		//Daily Task
		ini_write_real("task", "daily_point", 0);
		var task_clear = ["daily_login", "daily_stage", "daily_challenge", "daily_enemy", "daily_shop", "daily_craft", "daily_mix", "daily_consumable"];
		var reward_claimed = ["daily_login_claim", "daily_stage_claim1", "daily_stage_claim2", "daily_stage_claim3", "daily_challenge_claim1", "daily_challenge_claim2", "daily_enemy_claim1", "daily_enemy_claim2", "daily_enemy_claim3", "daily_enemy_claim4", "daily_enemy_claim5", 
							  "daily_shop_claim", "daily_craft_claim", "daily_mix_claim", "daily_consumable_claim"];
		var bonus_claimed = ["daily_reward1", "daily_reward2", "daily_reward3", "daily_reward4", "daily_reward5"];
		var task_daily_reset = array_concat(task_clear, reward_claimed, bonus_claimed);
		for (var j = 0; j < array_length(task_daily_reset); j++) {
			ini_write_real("task", task_daily_reset[j], 0);
		} 
		ini_write_real("task", "daily_login", 1);
	}
	if (weekly_reset == 1) {
		var purchaseable = function(row) {
			var valid = false;
			if (is_struct(db_get_value(global.db_items, 13, row))) {
				var attr = db_get_value(global.db_items, 13, row);
				valid = attr.limit.type == 2;
				delete attr;
			}
			return valid;
		}

		var item_num = [];
		for (var i = 3; i <= 4; i++) {
			item_num = array_concat(item_num, db_get_value_group(global.db_items, 2, i, 1, purchaseable));
		}
		for (var i = 0; i < array_length(item_num); i++) {
			if (ini_key_exists("shop", "item"+string(item_num[i]))) {
				ini_write_real("shop", "item"+string(item_num[i]), 0);
			}
		}
		
		var diff_change = ini_read_real("main", "diff_change", -1);
		if (diff_change == 0) {
			ini_write_real("main", "diff_change", 1);
		}
		
		ini_write_real("player", "demo_playtime", 0);
	}
	if (monthly_reset) {
		var purchaseable = function(row) {
			var valid = false;
			if (is_struct(db_get_value(global.db_items, 13, row))) {
				var attr = db_get_value(global.db_items, 13, row);
				valid = attr.limit.type == 3;
				delete attr;
			}
			return valid;
		}

		var item_num = [];
		for (var i = 3; i <= 4; i++) {
			item_num = array_concat(item_num, db_get_value_group(global.db_items, 2, i, 1, purchaseable));
		}
		for (var i = 0; i < array_length(item_num); i++) {
			if (ini_key_exists("shop", "item"+string(item_num[i]))) {
				ini_write_real("shop", "item"+string(item_num[i]), 0);
			}
		}
	}
	
	//Challenge
	last_day_cg = ini_read_real("main", "last_day", 11);
	last_month_cg = ini_read_real("main", "last_month", 17);
	last_year_cg = ini_read_real("main", "last_year", 2020);
	last_week_cg = date_get_week(date_create_datetime(last_year_cg, last_month_cg, last_day_cg, 0, 0, 0));
	last_weekday_cg = date_get_weekday(date_create_datetime(last_year_cg, last_month_cg, last_day_cg, 0, 0, 0));
	if (last_weekday_cg == 0) {
		last_weekday_cg = 7;
	}
	last_week_cg = date_get_week(date_create_datetime(last_year_cg, last_month_cg, last_day_cg-last_weekday_cg+1, 0, 0, 0));

	current_week_cg = date_get_week(date_create_datetime(current_year, current_month, current_day, 0, 0, 0));
	current_year_cg = date_get_year(date_create_datetime(current_year, current_month, current_day, 0, 0, 0));
	if (last_year_cg == current_year_cg) {
		week_diff = current_week_cg - last_week_cg;
	}
	else {
		week_diff = 52 - last_week_cg + current_week_cg;
	}

	if (current_weekday == 1) {
		if (current_day != last_day_cg) {
			ini_write_real("main", "last_day", current_day);
			ini_write_real("main", "last_month", current_month);
			ini_write_real("main", "last_year", current_year);
			ini_write_real("main", "cp_limit", 0);
			ini_write_real("main", "oe_limit", 0);
			ini_write_real("main", "mm_limit", 0);
			ini_write_real("main", "md_limit", 0);
			ini_write_real("main", "cc_limit", 0);
			ini_write_real("main", "cp_claimed", 0);
			ini_write_real("main", "oe_claimed", 0);
			ini_write_real("main", "mm_claimed", 0);
			ini_write_real("main", "md_claimed", 0);
			ini_write_real("main", "cc_claimed", 0);
		};
		var cp_claimed = ini_read_real("main", "cp_claimed", 0);
		var cp_limit = ini_read_real("main", "cp_limit", 0);
		var cp_ticket = ini_read_real("main", "cp_ticket", 0);
		if (cp_claimed == 0 && cp_limit < 4) {
			cp_limit += 1;
			cp_ticket += 1;
			ini_write_real("main", "cp_claimed", current_weekday);
			ini_write_real("main", "cp_limit", cp_limit);
			ini_write_real("main", "cp_ticket", cp_ticket);
		};
	};
	else if (current_weekday == 2) {
		if (last_day_cg == 0 || week_diff >= 1) {
			ini_write_real("main", "last_day", current_day);
			ini_write_real("main", "last_month", current_month);
			ini_write_real("main", "last_year", current_year);
			ini_write_real("main", "cp_limit", 0);
			ini_write_real("main", "oe_limit", 0);
			ini_write_real("main", "mm_limit", 0);
			ini_write_real("main", "md_limit", 0);
			ini_write_real("main", "cc_limit", 0);
			ini_write_real("main", "cp_claimed", 0);
			ini_write_real("main", "oe_claimed", 0);
			ini_write_real("main", "mm_claimed", 0);
			ini_write_real("main", "md_claimed", 0);
			ini_write_real("main", "cc_claimed", 0);
		};
		var oe_claimed = ini_read_real("main", "oe_claimed", 0);
		var oe_limit = ini_read_real("main", "oe_limit", 0);
		var oe_ticket = ini_read_real("main", "oe_ticket", 0);
		if (oe_claimed == 0 && oe_limit < 3) {
			oe_limit += 1;
			oe_ticket += 1;
			ini_write_real("main", "oe_claimed", current_weekday);
			ini_write_real("main", "oe_limit", oe_limit);
			ini_write_real("main", "oe_ticket", oe_ticket);
		};
	};
	else if (current_weekday == 3) {
		if (last_day_cg == 0 || week_diff >= 1) {
			ini_write_real("main", "last_day", current_day);
			ini_write_real("main", "last_month", current_month);
			ini_write_real("main", "last_year", current_year);
			ini_write_real("main", "cp_limit", 0);
			ini_write_real("main", "oe_limit", 0);
			ini_write_real("main", "mm_limit", 0);
			ini_write_real("main", "md_limit", 0);
			ini_write_real("main", "cc_limit", 0);
			ini_write_real("main", "cp_claimed", 0);
			ini_write_real("main", "oe_claimed", 0);
			ini_write_real("main", "mm_claimed", 0);
			ini_write_real("main", "md_claimed", 0);
			ini_write_real("main", "cc_claimed", 0);
		};
		var mm_claimed = ini_read_real("main", "mm_claimed", 0);
		var mm_limit = ini_read_real("main", "mm_limit", 0);
		var mm_ticket = ini_read_real("main", "mm_ticket", 0);
		if (mm_claimed == 0 && mm_limit < 3) {
			mm_limit += 1;
			mm_ticket += 1;
			ini_write_real("main", "mm_claimed", current_weekday);
			ini_write_real("main", "mm_limit", mm_limit);
			ini_write_real("main", "mm_ticket", mm_ticket);
		};
	};
	else if (current_weekday == 4) {
		if (last_day_cg == 0 || week_diff >= 1) {
			ini_write_real("main", "last_day", current_day);
			ini_write_real("main", "last_month", current_month);
			ini_write_real("main", "last_year", current_year);
			ini_write_real("main", "cp_limit", 0);
			ini_write_real("main", "oe_limit", 0);
			ini_write_real("main", "mm_limit", 0);
			ini_write_real("main", "md_limit", 0);
			ini_write_real("main", "cc_limit", 0);
			ini_write_real("main", "cp_claimed", 0);
			ini_write_real("main", "oe_claimed", 0);
			ini_write_real("main", "mm_claimed", 0);
			ini_write_real("main", "md_claimed", 0);
			ini_write_real("main", "cc_claimed", 0);
		};
		var cp_claimed = ini_read_real("main", "cp_claimed", 0);
		var cp_limit = ini_read_real("main", "cp_limit", 0);
		var cp_ticket = ini_read_real("main", "cp_ticket", 0);
		if ((cp_claimed == 0 || cp_claimed == 1) && cp_limit < 4) {
			cp_limit += 1;
			cp_ticket += 1;
			ini_write_real("main", "cp_claimed", current_weekday);
			ini_write_real("main", "cp_limit", cp_limit);
			ini_write_real("main", "cp_ticket", cp_ticket);
		};
		var oe_claimed = ini_read_real("main", "oe_claimed", 0);
		var oe_limit = ini_read_real("main", "oe_limit", 0);
		var oe_ticket = ini_read_real("main", "oe_ticket", 0);
		if ((oe_claimed == 0 || oe_claimed == 2) && oe_limit < 3) {
			oe_limit += 1;
			oe_ticket += 1;
			ini_write_real("main", "oe_claimed", current_weekday);
			ini_write_real("main", "oe_limit", oe_limit);
			ini_write_real("main", "oe_ticket", oe_ticket);
		};
	};
	else if (current_weekday == 5) {
		if (last_day_cg == 0 || week_diff >= 1) {
			ini_write_real("main", "last_day", current_day);
			ini_write_real("main", "last_month", current_month);
			ini_write_real("main", "last_year", current_year);
			ini_write_real("main", "cp_limit", 0);
			ini_write_real("main", "oe_limit", 0);
			ini_write_real("main", "mm_limit", 0);
			ini_write_real("main", "md_limit", 0);
			ini_write_real("main", "cc_limit", 0);
			ini_write_real("main", "cp_claimed", 0);
			ini_write_real("main", "oe_claimed", 0);
			ini_write_real("main", "mm_claimed", 0);
			ini_write_real("main", "md_claimed", 0);
			ini_write_real("main", "cc_claimed", 0);
		};
		var mm_claimed = ini_read_real("main", "mm_claimed", 0);
		var mm_limit = ini_read_real("main", "mm_limit", 0);
		var mm_ticket = ini_read_real("main", "mm_ticket", 0);
		if ((mm_claimed == 0 || mm_claimed == 3) && mm_limit < 3) {
			mm_limit += 1;
			mm_ticket += 1;
			ini_write_real("main", "mm_claimed", current_weekday);
			ini_write_real("main", "mm_limit", mm_limit);
			ini_write_real("main", "mm_ticket", mm_ticket);
		};
		var md_claimed = ini_read_real("main", "md_claimed", 0);
		var md_limit = ini_read_real("main", "md_limit", 0);
		var md_ticket = ini_read_real("main", "md_ticket", 0);
		if (md_claimed == 0 && md_limit < 2) {
			md_limit += 1;
			md_ticket += 1;
			ini_write_real("main", "md_claimed", current_weekday);
			ini_write_real("main", "md_limit", md_limit);
			ini_write_real("main", "md_ticket", md_ticket);
		};
	};
	else if (current_weekday == 6) {
		if (last_day_cg == 0 || week_diff >= 1) {
			ini_write_real("main", "last_day", current_day);
			ini_write_real("main", "last_month", current_month);
			ini_write_real("main", "last_year", current_year);
			ini_write_real("main", "cp_limit", 0);
			ini_write_real("main", "oe_limit", 0);
			ini_write_real("main", "mm_limit", 0);
			ini_write_real("main", "md_limit", 0);
			ini_write_real("main", "cc_limit", 0);
			ini_write_real("main", "cp_claimed", 0);
			ini_write_real("main", "oe_claimed", 0);
			ini_write_real("main", "mm_claimed", 0);
			ini_write_real("main", "md_claimed", 0);
			ini_write_real("main", "cc_claimed", 0);
		};
		var cp_claimed = ini_read_real("main", "cp_claimed", 0);
		var cp_limit = ini_read_real("main", "cp_limit", 0);
		var cp_ticket = ini_read_real("main", "cp_ticket", 0);
		if ((cp_claimed == 0 || cp_claimed == 1 || cp_claimed == 4) && cp_limit < 4) {
			cp_limit += 1;
			cp_ticket += 1;
			ini_write_real("main", "cp_claimed", current_weekday);
			ini_write_real("main", "cp_limit", cp_limit);
			ini_write_real("main", "cp_ticket", cp_ticket);
		};
		var cc_claimed = ini_read_real("main", "cc_claimed", 0);
		var cc_limit = ini_read_real("main", "cc_limit", 0);
		var cc_ticket = ini_read_real("main", "cc_ticket", 0);
		if (cc_claimed == 0 && cc_limit < 2) {
			cc_limit += 1;
			cc_ticket += 1;
			ini_write_real("main", "cc_claimed", current_weekday);
			ini_write_real("main", "cc_limit", cc_limit);
			ini_write_real("main", "cc_ticket", cc_ticket);
		};
	};
	else if (current_weekday == 0) {
		/*if (last_day_cg == 0 || week_diff >= 1) {
			ini_write_real("main", "last_day", current_day);
			ini_write_real("main", "last_month", current_month);
			ini_write_real("main", "last_year", current_year);
			ini_write_real("main", "cp_limit", 0);
			ini_write_real("main", "oe_limit", 0);
			ini_write_real("main", "mm_limit", 0);
			ini_write_real("main", "md_limit", 0);
			ini_write_real("main", "cc_limit", 0);
			ini_write_real("main", "cp_claimed", 0);
			ini_write_real("main", "oe_claimed", 0);
			ini_write_real("main", "mm_claimed", 0);
			ini_write_real("main", "md_claimed", 0);
			ini_write_real("main", "cc_claimed", 0);
		};*/
		var cp_claimed = ini_read_real("main", "cp_claimed", 0);
		var cp_limit = ini_read_real("main", "cp_limit", 0);
		var cp_ticket = ini_read_real("main", "cp_ticket", 0);
		if (cp_claimed != -1 && cp_limit < 4) {
			cp_limit = 4 - cp_limit;
			cp_ticket += cp_limit;
			ini_write_real("main", "cp_claimed", -1);
			ini_write_real("main", "cp_limit", 4);
			ini_write_real("main", "cp_ticket", cp_ticket);
		};
		var oe_claimed = ini_read_real("main", "oe_claimed", 0);
		var oe_limit = ini_read_real("main", "oe_limit", 0);
		var oe_ticket = ini_read_real("main", "oe_ticket", 0);
		if (oe_claimed != -1 && oe_limit < 3) {
			oe_limit = 3 - oe_limit;
			oe_ticket += oe_limit;
			ini_write_real("main", "oe_claimed", -1);
			ini_write_real("main", "oe_limit", 3);
			ini_write_real("main", "oe_ticket", oe_ticket);
		};
		var mm_claimed = ini_read_real("main", "mm_claimed", 0);
		var mm_limit = ini_read_real("main", "mm_limit", 0);
		var mm_ticket = ini_read_real("main", "mm_ticket", 0);
		if (mm_claimed != -1 && mm_limit < 3) {
			mm_limit = 3 - mm_limit;
			mm_ticket += mm_limit;
			ini_write_real("main", "mm_claimed", -1);
			ini_write_real("main", "mm_limit", 3);
			ini_write_real("main", "mm_ticket", mm_ticket);
		};
		var md_claimed = ini_read_real("main", "md_claimed", 0);
		var md_limit = ini_read_real("main", "md_limit", 0);
		var md_ticket = ini_read_real("main", "md_ticket", 0);
		if (md_claimed != -1 && md_limit < 2) {
			md_limit = 2 - md_limit;
			md_ticket += md_limit;
			ini_write_real("main", "md_claimed", -1);
			ini_write_real("main", "md_limit", 2);
			ini_write_real("main", "md_ticket", md_ticket);
		};
		var cc_claimed = ini_read_real("main", "cc_claimed", 0);
		var cc_limit = ini_read_real("main", "cc_limit", 0);
		var cc_ticket = ini_read_real("main", "cc_ticket", 0);
		if (cc_claimed != -1 && cc_limit < 2) {
			cc_limit = 2 - cc_limit;
			cc_ticket += cc_limit;
			ini_write_real("main", "cc_claimed", -1);
			ini_write_real("main", "cc_limit", 2);
			ini_write_real("main", "cc_ticket", cc_ticket);
		};
	};
	file_text_encrypt("general.osq");
}

// Ambience Sound
amb_sound = noone;
amb_sound_pause = 0;
amb_play = 0;
amb_current_type = 0;
amb_play_type = 0;
