image_alpha = oArcher.image_alpha;
if (!instance_exists(oCompoundBowQuiver)) {
	var cover = instance_create_depth(x, y, 400, oCompoundBowQuiver);
	cover.owner = owner;
};
if (oArcher.defeated == 0 && oStageComplete.stage_completed > -2) {
	if (oArcher.draw == 0) {
		if (draw_anim == 0) {
			if (owner.bot_dir == "right") {
				x = oArcher.x + 5;
				y = oArcher.y + 25;
				if (global.aim_mode == 0) {
					image_angle = point_direction(x, y, oArcher.x + 100, oArcher.y + 25) - 75;
				};
				else {
					image_angle = point_direction(x, y, oArcherAttack.x, oArcherAttack.y) - 75;
				};
				if (image_xscale != 1) {
					image_xscale = 1;
				};
				if (depth != 200) {
					depth = 200;
				};
			};
			else if (owner.bot_dir == "left") {
				x = oArcher.x - 5;
				y = oArcher.y + 25;
				if (global.aim_mode == 0) {
					image_angle = point_direction(oArcher.x - 100, oArcher.y + 25, x, y) + 75;
				};
				else {
					image_angle = point_direction(oArcherAttack.x, oArcherAttack.y, x, y) + 75;
				};
				if (image_xscale != -1) {
					image_xscale = -1;
				};
				if (depth != -100) {
					depth = -100;
				};
			};
		};
		else {
			if (draw_anim == 1) {
				draw_anim = -1;
				image_speed = -1 * (draw_spd/100);
			};
			if (owner.bot_dir == "right") {
				x = oArcher.x + 5;
				y = oArcher.y + 25;
				if (global.aim_mode == 0) {
					image_angle = point_direction(x, y, oArcher.x + 100, oArcher.y + 25);
				};
				else {
					image_angle = point_direction(x, y, oArcherAttack.x, oArcherAttack.y);
				};
				if (sprite_index = sCompoundBowDrawR) {
					if (image_index <= 1 * (draw_spd/100) && image_speed != 0) {
						image_index = 0;
						image_speed = 0;
						depth = 200;
						draw_anim = 0;
						sprite_index = sCompoundBow;
						if (global.aim_mode == 0) {
							image_angle = point_direction(x, y, oArcher.x + 100, oArcher.y + 25) - 75;
						};
						else {
							image_angle = point_direction(x, y, oArcherAttack.x, oArcherAttack.y) - 75;
						};
						image_xscale = 1;
					};
				};
				else {
					var animate = 0;
					if (sprite_index != sCompoundBowDrawL) {
						animate = 1;
					};
					sprite_index = sCompoundBowDrawR;
					if (animate == 1) {
						image_index = image_number - 1;
					};
				};
			};
			else if (owner.bot_dir == "left") {
				x = oArcher.x - 5;
				y = oArcher.y + 25;
				if (global.aim_mode == 0) {
					image_angle = point_direction(oArcher.x - 100, oArcher.y + 25, x, y);
				};
				else {
					image_angle = point_direction(oArcherAttack.x, oArcherAttack.y, x, y);
				};
				if (sprite_index = sCompoundBowDrawL) {
					if (image_index <= 1 * (draw_spd/100) && image_speed != 0) {
						image_index = 0;
						image_speed = 0;
						depth = -100;
						draw_anim = 0;
						sprite_index = sCompoundBow;
						if (global.aim_mode == 0) {
							image_angle = point_direction(oArcher.x - 100, oArcher.y + 25, x, y) + 75;
						};
						else {
							image_angle = point_direction(oArcherAttack.x, oArcherAttack.y, x, y) + 75;
						};
						image_xscale = -1;
					};
				};
				else {
					var animate = 0;
					if (sprite_index != sCompoundBowDrawR) {
						animate = 1;
					};
					sprite_index = sCompoundBowDrawL;
					if (animate == 1) {
						image_index = image_number - 1;
					};
				};
			};
		};
	};
	else if (oArcher.draw == 1) {
		if (oArcher.attacking == 0) {
			if (anim != 0) {
				anim = 0;
			};
			if (draw_anim == 0) {
				draw_anim = 1;
				image_speed = 1 * (draw_spd/100);
				image_xscale = 1;
				if (owner.bot_dir == "right") {
					sprite_index = sCompoundBowDrawR;
				};
				else {
					sprite_index = sCompoundBowDrawL;
				};
			};
			if (owner.bot_dir == "right") {
				x = oArcher.x + 5;
				y = oArcher.y + 25;
				if (global.aim_mode == 0) {
					image_angle = point_direction(x, y, oArcher.x + 100, oArcher.y + 25);
				};
				else {
					image_angle = point_direction(x, y, oArcherAttack.x, oArcherAttack.y);
				};
				if (sprite_index = sCompoundBowDrawR) {
					if (image_index >= image_number - 1 * (draw_spd/100) && image_speed != 0) {
						image_index = image_number - 1;
						image_speed = 0;
						depth = 200;
					};
					if (depth != 200) {
						depth = 200;
					};
				};
				else if (sprite_index = sCompoundBowDrawL) {
					sprite_index = sCompoundBowDrawR;
					if (image_index >= image_number - 1 * (draw_spd/100) && image_speed != 0) {
						image_speed = 0;
						depth = 200;
					};
				};
				else {
					//Recovery
					if (sprite_index == sCompoundBowODAShot1R) {
						sprite_index = sCompoundBowODAShot1RecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowODAShot2R) {
						sprite_index = sCompoundBowODAShot2RecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowODAShot3R) {
						sprite_index = sCompoundBowODAShot3RecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowODAShot4R) {
						sprite_index = sCompoundBowODAShot4RecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowODAShot5R) {
						sprite_index = sCompoundBowODAShot5RecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowODAShot6R) {
						sprite_index = sCompoundBowODAShot6RecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowODAShot6RecR) {
						if (image_index >= 12 - 1 * (oArcher.atk_spd/100) && depth != 200) {
							depth = 200;
						};
					};
					else if (sprite_index == sCompoundBowHeavyShotR) {
						sprite_index = sCompoundBowHeavyShotRecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowMultiShotAR || sprite_index == sCompoundBowMultiShotBR) {
						sprite_index = sCompoundBowMultiShotRecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowParallelShotR) {
						sprite_index = sCompoundBowHeavyShotRecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowChargedShotAR) {
						sprite_index = sCompoundBowChargedShotARecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowChargedShotBR) {
						sprite_index = sCompoundBowChargedShotBRecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowExplosiveShotR) {
						sprite_index = sCompoundBowExplosiveShotRecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowFlickShotR) {
						sprite_index = sCompoundBowFlickShotRecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowFlickShotRecR) {
						if (image_index >= 10 - 1 * (oArcher.atk_spd/100) && depth != 200) {
							depth = 200;
						};
					};
					else if (sprite_index == sCompoundBowElectricShotR) {
						sprite_index = sCompoundBowElectricShotRecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowCloudburstVolleyR) {
						sprite_index = sCompoundBowCloudburstVolleyRecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowPinpointShootingAR) {
						sprite_index = sCompoundBowPinpointShootingARecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowPinpointShootingBR) {
						sprite_index = sCompoundBowPinpointShootingBRecR;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowPinpointShootingBRecR) {
						if (image_index >= 17 - 1 * (oArcher.atk_spd/100) && depth != 200) {
							depth = 200;
						};
					};
					
					//Ganti arah
					else if (sprite_index == sCompoundBowODAShot1RecL) {
						sprite_index = sCompoundBowODAShot1RecR;
						depth = 200;
					};
					else if (sprite_index == sCompoundBowODAShot2RecL) {
						sprite_index = sCompoundBowODAShot2RecR;
						depth = 200;
					};
					else if (sprite_index == sCompoundBowODAShot3RecL) {
						sprite_index = sCompoundBowODAShot3RecR;
						depth = 200;
					};
					else if (sprite_index == sCompoundBowODAShot4RecL) {
						sprite_index = sCompoundBowODAShot4RecR;
						depth = 200;
					};
					else if (sprite_index == sCompoundBowODAShot5RecL) {
						sprite_index = sCompoundBowODAShot5RecR;
						depth = 200;
					};
					else if (sprite_index == sCompoundBowODAShot6RecL) {
						sprite_index = sCompoundBowODAShot6RecR;
						if (image_index >= 12 - 1 * (oArcher.atk_spd/100) && depth != 200) {
							depth = 200;
						};
						else {
							depth = -100;
						};
					};
					else if (sprite_index == sCompoundBowHeavyShotRecL) {
						sprite_index = sCompoundBowHeavyShotRecR;
						depth = 200;
					};
					else if (sprite_index == sCompoundBowMultiShotRecL) {
						sprite_index = sCompoundBowMultiShotRecR;
						depth = 200;
					};
					else if (sprite_index == sCompoundBowChargedShotARecL) {
						sprite_index = sCompoundBowChargedShotARecR;
						depth = 200;
					};
					else if (sprite_index == sCompoundBowChargedShotBRecL) {
						sprite_index = sCompoundBowChargedShotBRecR;
						depth = 200;
					};
					else if (sprite_index == sCompoundBowExplosiveShotRecL) {
						sprite_index = sCompoundBowExplosiveShotRecR;
						depth = 200;
					};
					else if (sprite_index == sCompoundBowFlickShotRecL) {
						sprite_index = sCompoundBowFlickShotRecR;
						if (image_index >= 10 - 1 * (oArcher.atk_spd/100) && depth != 200) {
							depth = 200;
						};
						else {
							depth = -100;
						};
					};
					else if (sprite_index == sCompoundBowElectricShotRecL) {
						sprite_index = sCompoundBowElectricShotRecR;
						depth = 200;
					};
					else if (sprite_index == sCompoundBowCloudburstVolleyRecL) {
						sprite_index = sCompoundBowCloudburstVolleyRecR;
						depth = 200;
					};
					else if (sprite_index == sCompoundBowPinpointShootingARecL) {
						sprite_index = sCompoundBowPinpointShootingARecR;
						depth = 200;
					};
					else if (sprite_index == sCompoundBowPinpointShootingBRecL) {
						sprite_index = sCompoundBowPinpointShootingBRecR;
						if (image_index >= 17 - 1 * (oArcher.atk_spd/100) && depth != 200) {
							depth = 200;
						};
						else {
							depth = -100;
						};
					};
					
					//Stop anim kalo dah selesai
					if (image_index >= image_number - 1 * (oArcher.atk_spd/100)) {
						image_index = image_number - 1;
						image_speed = 0;
					};
					if (global.class == 2 && oBattleCam.zoom_to != 0) {
						oBattleCam.zoom_to = 0;
					};
				};
			};
			else if (owner.bot_dir == "left") {
				x = oArcher.x - 5;
				y = oArcher.y + 25;
				if (global.aim_mode == 0) {
					image_angle = point_direction(oArcher.x - 100, oArcher.y + 25, x, y);
				};
				else {
					image_angle = point_direction(oArcherAttack.x, oArcherAttack.y, x, y);
				};
				if (sprite_index = sCompoundBowDrawL) {
					if (image_index >= image_number - 1 * (draw_spd/100) && image_speed != 0) {
						image_index = image_number - 1;
						image_speed = 0;
						depth = -100;
					};
					if (depth != -100) {
						depth = -100;
					};
				};
				else if (sprite_index = sCompoundBowDrawR) {
					sprite_index = sCompoundBowDrawL;
					if (image_index >= image_number - 1 * (draw_spd/100) && image_speed != 0) {
						image_index = image_number - 1;
						image_speed = 0;
						depth = -100;
					};
				};
				else {
					//Recovery
					if (sprite_index == sCompoundBowODAShot1L) {
						sprite_index = sCompoundBowODAShot1RecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowODAShot2L) {
						sprite_index = sCompoundBowODAShot2RecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowODAShot3L) {
						sprite_index = sCompoundBowODAShot3RecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowODAShot4L) {
						sprite_index = sCompoundBowODAShot4RecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowODAShot5L) {
						sprite_index = sCompoundBowODAShot5RecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowODAShot6L) {
						sprite_index = sCompoundBowODAShot6RecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowODAShot6RecL) {
						if (image_index >= 12 - 1 * (oArcher.atk_spd/100) && depth != -100) {
							depth = -100;
						};
					};
					else if (sprite_index == sCompoundBowHeavyShotL) {
						sprite_index = sCompoundBowHeavyShotRecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowMultiShotAL || sprite_index == sCompoundBowMultiShotBL) {
						sprite_index = sCompoundBowMultiShotRecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowParallelShotL) {
						sprite_index = sCompoundBowHeavyShotRecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowChargedShotAL) {
						sprite_index = sCompoundBowChargedShotARecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowChargedShotBL) {
						sprite_index = sCompoundBowChargedShotBRecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowExplosiveShotL) {
						sprite_index = sCompoundBowExplosiveShotRecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowFlickShotL) {
						sprite_index = sCompoundBowFlickShotRecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowFlickShotRecL) {
						if (image_index >= 10 - 1 * (oArcher.atk_spd/100) && depth != -100) {
							depth = -100;
						};
					};
					else if (sprite_index == sCompoundBowElectricShotL) {
						sprite_index = sCompoundBowElectricShotRecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowCloudburstVolleyL) {
						sprite_index = sCompoundBowCloudburstVolleyRecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowPinpointShootingAL) {
						sprite_index = sCompoundBowPinpointShootingARecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = -100;
					};
					else if (sprite_index == sCompoundBowPinpointShootingBL) {
						sprite_index = sCompoundBowPinpointShootingBRecL;
						image_index = 0;
						image_speed = 1 * (oArcher.atk_spd/100);
						depth = 200;
					};
					else if (sprite_index == sCompoundBowPinpointShootingBRecL) {
						if (image_index >= 17 - 1 * (oArcher.atk_spd/100) && depth != -100) {
							depth = -100;
						};
					};
					
					//Ganti arah
					else if (sprite_index == sCompoundBowODAShot1RecR) {
						sprite_index = sCompoundBowODAShot1RecL;
						depth = -100;
					};
					else if (sprite_index == sCompoundBowODAShot2RecR) {
						sprite_index = sCompoundBowODAShot2RecL;
						depth = -100;
					};
					else if (sprite_index == sCompoundBowODAShot3RecR) {
						sprite_index = sCompoundBowODAShot3RecL;
						depth = -100;
					};
					else if (sprite_index == sCompoundBowODAShot4RecR) {
						sprite_index = sCompoundBowODAShot4RecL;
						depth = -100;
					};
					else if (sprite_index == sCompoundBowODAShot5RecR) {
						sprite_index = sCompoundBowODAShot5RecL;
						depth = -100;
					};
					else if (sprite_index == sCompoundBowODAShot6RecR) {
						sprite_index = sCompoundBowODAShot6RecL;
						if (image_index >= 12 - 1 * (oArcher.atk_spd/100) && depth != -100) {
							depth = -100;
						};
						else {
							depth = 200;
						};
					};
					else if (sprite_index == sCompoundBowHeavyShotRecR) {
						sprite_index = sCompoundBowHeavyShotRecL;
						depth = -100;
					};
					else if (sprite_index == sCompoundBowMultiShotRecR) {
						sprite_index = sCompoundBowMultiShotRecL;
						depth = -100;
					};
					else if (sprite_index == sCompoundBowChargedShotARecR) {
						sprite_index = sCompoundBowChargedShotARecL;
						depth = -100;
					};
					else if (sprite_index == sCompoundBowChargedShotBRecR) {
						sprite_index = sCompoundBowChargedShotBRecL;
						depth = -100;
					};
					else if (sprite_index == sCompoundBowExplosiveShotRecR) {
						sprite_index = sCompoundBowExplosiveShotRecL;
						depth = -100;
					};
					else if (sprite_index == sCompoundBowFlickShotRecR) {
						sprite_index = sCompoundBowFlickShotRecL;
						if (image_index >= 10 - 1 * (oArcher.atk_spd/100) && depth != -100) {
							depth = -100;
						};
						else {
							depth = 200;
						};
					};
					else if (sprite_index == sCompoundBowElectricShotRecR) {
						sprite_index = sCompoundBowElectricShotRecL;
						depth = -100;
					};
					else if (sprite_index == sCompoundBowCloudburstVolleyRecR) {
						sprite_index = sCompoundBowCloudburstVolleyRecL;
						depth = -100;
					};
					else if (sprite_index == sCompoundBowPinpointShootingARecR) {
						sprite_index = sCompoundBowPinpointShootingARecL;
						depth = -100;
					};
					else if (sprite_index == sCompoundBowPinpointShootingBRecR) {
						sprite_index = sCompoundBowPinpointShootingBRecL;
						if (image_index >= 17 - 1 * (oArcher.atk_spd/100) && depth != -100) {
							depth = -100;
						};
						else {
							depth = 200;
						};
					};
					
					//Stop anim kalo dah selesai
					if (image_index >= image_number - 1 * (oArcher.atk_spd/100)) {
						image_index = image_number - 1;
						image_speed = 0;
					};
					if (global.class == 2 && oBattleCam.zoom_to != 0) {
						oBattleCam.zoom_to = 0;
					};
				};
			};
		};
		else if (oArcher.attacking == 1) {
			var acc_arc = oArcherAttack.crosshair_acc;
			if (owner.bot_dir == "right") {
				x = oArcher.x + 5;
				y = oArcher.y + 25;
				if (global.aim_mode == 0) {
					image_angle = point_direction(oArcher.x, oArcher.y, mouse_x, oArcher.y);
				};
				else {
					image_angle = point_direction(oArcher.x, oArcher.y, oArcherAttack.x, oArcherAttack.y);
				};
				if (oArcherAttack.current_seq_type == 1 || oArcherAttack.current_seq_type == 4 || oArcherAttack.current_seq_type == 6) {
					if (oArcherAttack.chain == 0 || oArcherAttack.chain == 1) {
						var shot_at = 15;
						if (sprite_index != sCompoundBowODAShot1R) {
							sprite_index = sCompoundBowODAShot1R;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = 200;
						};
						if (re_anim == 1) {
							re_anim = -1;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = 200;
						};
						if (sprite_index == sCompoundBowODAShot1R && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
							shot = 1; 
							var xx = oArcher.x + lengthdir_x(90, image_angle);
							var yy = oArcher.y + lengthdir_y(90, image_angle);
							var arrow = instance_create_depth(xx, yy, 150, oArcherODAArrow);
							arrow.image_angle = image_angle + random_range(-acc_arc, acc_arc);
							arrow.direction = arrow.image_angle;
							arrow.speed = 40 * (oArcher.projectile_speed/100);
							arrow.atk_dir = 1;
							oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
							audio_play_sound(soBowShot1, 90, false);
						}; 
						if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
							shot_no = 2;
							anim = 1;
							re_anim = 1;
							shot = 0;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							with (oArcherAttack) {
								alarm_set(0, room_speed * 2);
							};
							oArcherAttack.batk_ii_bonus = 10;
							oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
						};
					};
					else if (oArcherAttack.chain == 2) {
						var shot_at = 11;
						if (sprite_index != sCompoundBowODAShot2R) {
							sprite_index = sCompoundBowODAShot2R;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = 200;
						};
						if (re_anim == 1) {
							re_anim = -1;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = 200;
						};
						if (sprite_index == sCompoundBowODAShot2R && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
							shot = 1;
							var xx = oArcher.x + lengthdir_x(80, image_angle);
							var yy = oArcher.y + lengthdir_y(80, image_angle);
							var arrow = instance_create_depth(xx, yy, 150, oArcherODAArrow);
							arrow.image_angle = image_angle + random_range(-acc_arc, acc_arc);
							arrow.direction = arrow.image_angle;
							arrow.speed = 40 * (oArcher.projectile_speed/100);
							arrow.atk_dir = 1;
							oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
							audio_play_sound(soBowShot1, 90, false);
						};
						if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
							shot_no = 2;
							anim = 1;
							re_anim = 1;
							shot = 0;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							with (oArcherAttack) {
								alarm_set(0, room_speed * 2);
							};
							oArcherAttack.batk_ii_bonus = 10;
							oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
						};
					};
					else if (oArcherAttack.chain == 3) {
						var shot_at1 = 8;
						var shot_at2 = 16;
						if (sprite_index != sCompoundBowODAShot3R) {
							sprite_index = sCompoundBowODAShot3R;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = 200;
						};
						if (re_anim == 1) {
							re_anim = -1;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = 200;
						};
						if (sprite_index == sCompoundBowODAShot3R && ((image_index >= shot_at1 - 1 * (oArcher.atk_spd/100) && shot == 0) || (image_index >= shot_at2 - 1 * (oArcher.atk_spd/100) && shot == 1)) && (anim == -1 || re_anim == -1)) {
							if (shot == 1) {
								shot = 2;
							};
							else if (shot == 0) {
								shot = 1;
							};
							var xx = oArcher.x + lengthdir_x(80, image_angle);
							var yy = oArcher.y + lengthdir_y(80, image_angle);
							var arrow = instance_create_depth(xx, yy, 150, oArcherODAArrow);
							arrow.image_angle = image_angle + random_range(-acc_arc, acc_arc);
							arrow.direction = arrow.image_angle;
							arrow.speed = 40 * (oArcher.projectile_speed/100);
							arrow.atk_dir = 1;
							oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
							audio_play_sound(soBowShot1, 90, false);
						};
						if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
							shot_no = 2;
							anim = 1;
							re_anim = 1;
							shot = 0;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							with (oArcherAttack) {
								alarm_set(0, room_speed * 2);
							};
							oArcherAttack.batk_ii_bonus = 10;
							oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
						};
					};
					else if (oArcherAttack.chain == 4) {
						var shot_at = 17;
						if (sprite_index != sCompoundBowODAShot4R) {
							sprite_index = sCompoundBowODAShot4R;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = 200;
						};
						if (re_anim == 1) {
							re_anim = -1;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = 200;
						};
						if (sprite_index == sCompoundBowODAShot4R && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
							shot = 1;
							var xx = oArcher.x + lengthdir_x(80, image_angle);
							var yy = oArcher.y + lengthdir_y(80, image_angle);
							var arrow = instance_create_depth(xx, yy, 150, oArcherODAArrow);
							arrow.image_angle = image_angle + random_range(-acc_arc, acc_arc);
							arrow.direction = arrow.image_angle;
							arrow.speed = 40 * (oArcher.projectile_speed/100);
							arrow.atk_dir = 1;
							oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
							audio_play_sound(soBowShot1, 90, false);
						};
						if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
							shot_no = 2;
							anim = 1;
							re_anim = 1;
							shot = 0;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							with (oArcherAttack) {
								alarm_set(0, room_speed * 2);
							};
							oArcherAttack.batk_ii_bonus = 10;
							oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
						};
					};
					else if (oArcherAttack.chain == 5) {
						if (((oArcherAttack.current_seq_type == 1 || oArcherAttack.current_seq_type == 4) && oArcherAttack.os5_unlocked == 1) || (oArcherAttack.current_seq_type == 6 && oArcherAttack.as5_unlocked == 1)) {
							var shot_at = 14;
							if (sprite_index != sCompoundBowODAShot5R) {
								sprite_index = sCompoundBowODAShot5R;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = 200;
							};
							if (re_anim == 1) {
								re_anim = -1;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = 200;
							};
							if (sprite_index == sCompoundBowODAShot5R && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
								shot = 1;
								var spread_cnt = 3;
								var base_angle = 15 * ((100-oArcher.mprj_angle_reduction)/100);
								var xx = oArcher.x + lengthdir_x(80, image_angle);
								var yy = oArcher.y + lengthdir_y(80, image_angle);
								for (var i = 0; i < spread_cnt; i++) {
									var arrow = instance_create_depth(xx, yy, 150, oArcherODAArrow);
									arrow.image_angle = (image_angle+(base_angle*(i-floor(spread_cnt/2)))) + random_range(-acc_arc, acc_arc);
									arrow.direction = arrow.image_angle;
									arrow.speed = 40 * (oArcher.projectile_speed/100);
									arrow.atk_dir = 1;
								};
								oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
								audio_play_sound(soBowShot1, 90, false);
							};
							if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
								shot_no = 2;
								anim = 1;
								re_anim = 1;
								shot = 0;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								with (oArcherAttack) {
									alarm_set(0, room_speed * 2);
								};
								oArcherAttack.batk_ii_bonus = 10;
								oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
							};
						};
						else {
							var shot_at = 17;
							if (sprite_index != sCompoundBowODAShot4R) {
								sprite_index = sCompoundBowODAShot4R;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = 200;
							};
							if (re_anim == 1) {
								re_anim = -1;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = 200;
							};
							if (sprite_index == sCompoundBowODAShot4R && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
								shot = 1;
								var xx = oArcher.x + lengthdir_x(80, image_angle);
								var yy = oArcher.y + lengthdir_y(80, image_angle);
								var arrow = instance_create_depth(xx, yy, 150, oArcherODAArrow);
								arrow.image_angle = image_angle + random_range(-acc_arc, acc_arc);
								arrow.direction = arrow.image_angle;
								arrow.speed = 40 * (oArcher.projectile_speed/100);
								arrow.atk_dir = 1;
								oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
								audio_play_sound(soBowShot1, 90, false);
							};
							if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
								shot_no = 2;
								anim = 1;
								re_anim = 1;
								shot = 0;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								with (oArcherAttack) {
									alarm_set(0, room_speed * 2);
								};
								oArcherAttack.batk_ii_bonus = 10;
								oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
							};
						};
					};
					else if (oArcherAttack.chain >= 6) {
						if (((oArcherAttack.current_seq_type == 1 || oArcherAttack.current_seq_type == 4) && oArcherAttack.os6_unlocked == 1) || (oArcherAttack.current_seq_type == 6 && oArcherAttack.as6_unlocked == 1)) {
							var shot_at = 16;
							if (sprite_index != sCompoundBowODAShot6R) {
								sprite_index = sCompoundBowODAShot6R;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = 200;
							};
							if (re_anim == 1) {
								re_anim = -1;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = 200;
							};
							if (sprite_index == sCompoundBowODAShot6R) {
								if (image_index >= 10 - 1 * (oArcher.atk_spd/100) && depth != -100) {
									depth = -100;
								};
							};
							if (sprite_index == sCompoundBowODAShot6R && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
								shot = 1;
								var xx = oArcher.x + lengthdir_x(80, image_angle);
								var yy = oArcher.y + lengthdir_y(80, image_angle);
								var arrow = instance_create_depth(xx, yy, 150, oArcherODAArrow);
								arrow.image_angle = image_angle + random_range(-acc_arc, acc_arc);
								arrow.direction = arrow.image_angle;
								arrow.speed = 40 * (oArcher.projectile_speed/100);
								arrow.atk_dir = 1;
								oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
								audio_play_sound(soBowShot1, 90, false);
							};
							if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
								shot_no = 2;
								anim = 1;
								re_anim = 1;
								shot = 0;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								with (oArcherAttack) {
									alarm_set(0, room_speed * 2);
								};
								oArcherAttack.batk_ii_bonus = 10;
								oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
							};
						};
						else if (((oArcherAttack.current_seq_type == 1 || oArcherAttack.current_seq_type == 4) && oArcherAttack.os5_unlocked == 1) || (oArcherAttack.current_seq_type == 6 && oArcherAttack.as5_unlocked == 1)) {
							var shot_at = 14;
							if (sprite_index != sCompoundBowODAShot5R) {
								sprite_index = sCompoundBowODAShot5R;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = 200;
							};
							if (re_anim == 1) {
								re_anim = -1;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = 200;
							};
							if (sprite_index == sCompoundBowODAShot5R && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
								shot = 1;
								var spread_cnt = 3;
								var base_angle = 15 * ((100-oArcher.mprj_angle_reduction)/100);
								var xx = oArcher.x + lengthdir_x(80, image_angle);
								var yy = oArcher.y + lengthdir_y(80, image_angle);
								for (var i = 0; i < spread_cnt; i++) {
									var arrow = instance_create_depth(xx, yy, 150, oArcherODAArrow);
									arrow.image_angle = (image_angle+(base_angle*(i-floor(spread_cnt/2)))) + random_range(-acc_arc, acc_arc);
									arrow.direction = arrow.image_angle;
									arrow.speed = 40 * (oArcher.projectile_speed/100);
									arrow.atk_dir = 1;
								};
								oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
								audio_play_sound(soBowShot1, 90, false);
							};
							if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
								shot_no = 2;
								anim = 1;
								re_anim = 1;
								shot = 0;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								with (oArcherAttack) {
									alarm_set(0, room_speed * 2);
								};
								oArcherAttack.batk_ii_bonus = 10;
								oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
							};
						};
						else {
							var shot_at = 17;
							if (sprite_index != sCompoundBowODAShot4R) {
								sprite_index = sCompoundBowODAShot4R;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = 200;
							};
							if (re_anim == 1) {
								re_anim = -1;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = 200;
							};
							if (sprite_index == sCompoundBowODAShot4R && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
								shot = 1;
								var xx = oArcher.x + lengthdir_x(80, image_angle);
								var yy = oArcher.y + lengthdir_y(80, image_angle);
								var arrow = instance_create_depth(xx, yy, 150, oArcherODAArrow);
								arrow.image_angle = image_angle + random_range(-acc_arc, acc_arc);
								arrow.direction = arrow.image_angle;
								arrow.speed = 40 * (oArcher.projectile_speed/100);
								arrow.atk_dir = 1;
								oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
								audio_play_sound(soBowShot1, 90, false);
							};
							if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
								shot_no = 2;
								anim = 1;
								re_anim = 1;
								shot = 0;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								with (oArcherAttack) {
									alarm_set(0, room_speed * 2);
								};
								oArcherAttack.batk_ii_bonus = 10;
								oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
							};
						};
					};
				};
				else if (oArcherAttack.current_seq_type == 2) {
					var shot_at = 23;
					if (sprite_index != sCompoundBowHeavyShotR) {
						sprite_index = sCompoundBowHeavyShotR;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (sprite_index == sCompoundBowHeavyShotR && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						var xx = oArcher.x + lengthdir_x(70, image_angle);
						var yy = oArcher.y + lengthdir_y(70, image_angle);
						var arrow = instance_create_depth(xx, yy, 150, oArcherHeavyArrow);
						arrow.image_angle = image_angle + random_range(-acc_arc, acc_arc);
						arrow.direction = arrow.image_angle;
						arrow.speed = 35 * (oArcher.projectile_speed/100);
						arrow.atk_dir = 1;
						oArcherAttack.recoil += 25*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot1, 90, false);
					};
				};
				else if (oArcherAttack.current_seq_type == 3) {
					var shot_at = 10;
					var max_shot = round((((100+oArcher.basic_atk_eff)/100) * 2 + 1*floor(oArcherController.type3_level/5))) + ceil(oArcherAttack.last_chain/2);
					if (oArcherAttack.talent_e2 == 1 && oArcherAttack.talent_e2_pt == 0 && image_speed != 0) {
						oArcherAttack.talent_e2 = -1;
						oArcher.atk_spd += max_shot * 3;
						oArcherAttack.talent_e2_pt = max_shot * 3;
					};
					if (sprite_index != sCompoundBowMultiShotAR && phase == 1) {
						sprite_index = sCompoundBowMultiShotAR;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (sprite_index == sCompoundBowMultiShotAR && phase == 0) {
						sprite_index = sCompoundBowMultiShotBR;
						anim = 1;
						re_anim = 1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						phase = 2;
						alarm_set(0, -1);
					};
					if (sprite_index == sCompoundBowMultiShotBR && phase == 0) {
						if (shot_no < max_shot - 1) {
							anim = 1;
							re_anim = 1;
							shot_no += 1;
							phase = 2;
							shot = 0;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							alarm_set(0, -1);
							oArcherAttack.batk_ii_bonus = 15;
							oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
						};
						else {
							phase = -1;
							shot_no = 0;
							oArcherAttack.last_chain = 0;
							with (oArcherAttack) {
								alarm_set(0, room_speed * 2);
							};
							if (oArcher.pinpoint_mode == 1 && oArcherAttack.pinpoint_buff == 1) {
								oArcherAttack.pinpoint_buff = 0;
								oArcher.atk_spd -= 20;
							};
							if (oArcherAttack.talent_e2 == -1 && oArcherAttack.talent_e2_pt != 0) {
								oArcherAttack.talent_e2 = 1;
								oArcher.atk_spd -= oArcherAttack.talent_e2_pt;
								oArcherAttack.talent_e2_pt = 0;
							};
						};
					};
					if (sprite_index == sCompoundBowMultiShotBR && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						var xx = oArcher.x + lengthdir_x(70, image_angle);
						var yy = oArcher.y + lengthdir_y(70, image_angle);
						var arrow = instance_create_depth(xx, yy, 150, oArcherMPArrow);
						arrow.image_angle = image_angle + random_range(-acc_arc, acc_arc);
						arrow.direction = arrow.image_angle;
						arrow.speed = 40 * (oArcher.projectile_speed/100);
						arrow.atk_dir = 1;
						oArcherAttack.recoil += 15*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot1, 90, false);
						oArcher.hspeed -= oArcher.spd/3;
						oArcher.last_spd = oArcher.hspeed;
						oArcher.stop = 1;
						if (oArcher.pinpoint_mode == 1) {
							oArcher.last_spd = 0;
							oArcher.stop = 0;
							oArcher.hspeed = 0;
							oArcher.vspeed = 0;
						};
					};
					if (sprite_index == sCompoundBowMultiShotAL) {
						sprite_index = sCompoundBowMultiShotAR;
						depth = 200;
					};
					if (sprite_index == sCompoundBowMultiShotBL) {
						sprite_index = sCompoundBowMultiShotBR;
						depth = 200;
					};
				};
				else if (oArcherAttack.current_seq_type == 5) {
					var shot_at = 16;
					if (oArcher.talent_c4 == 1) {
						var spread_cnt = round((((100+oArcher.basic_atk_eff)/100) * 3 + 1*floor(oArcherController.type5_level/5))) + ceil(oArcherAttack.last_chain/2) + (oArcher.jumping * 2);
					};
					else {
						var spread_cnt = round((((100+oArcher.basic_atk_eff)/100) * 3 + 1*floor(oArcherController.type5_level/5))) + ceil(oArcherAttack.last_chain/2) + oArcher.jumping;
					};
					if (sprite_index != sCompoundBowParallelShotR) {
						sprite_index = sCompoundBowParallelShotR;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (sprite_index == sCompoundBowParallelShotR && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						if (oArcher.pinpoint_mode == 1) {
							oArcher.mprj_angle_reduction += 35;
						};
						var base_angle = 12 * ((100-oArcher.mprj_angle_reduction)/100);
						var xx = oArcher.x + lengthdir_x(55, image_angle);
						var yy = oArcher.y + lengthdir_y(55, image_angle);
						for (var i = 0; i < spread_cnt; i++) {
							var arrow = instance_create_depth(xx, yy, 150, oArcherMPArrow);
							arrow.image_angle = (image_angle+(base_angle*(i-floor(spread_cnt/2)))) + random_range(-acc_arc, acc_arc);
							arrow.direction = arrow.image_angle;
							arrow.speed = 40 * (oArcher.projectile_speed/100);
							arrow.atk_dir = 1;
						};
						oArcherAttack.recoil += 25*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot1, 90, false);
						oArcherAttack.last_chain = 0;
						if (oArcher.pinpoint_mode == 1) {
							oArcher.mprj_angle_reduction -= 35;
						};
					};
				};
				else if (oArcherAttack.current_seq_type == 7) {
					if (oArcherAttack.charging == 1) {
						if (sprite_index != sCompoundBowChargedShotAR) {
							sprite_index = sCompoundBowChargedShotAR;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = 200;
						};
						if (re_anim == 1) {
							re_anim = -1;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = 200;
						};
					};
					else if (oArcherAttack.charging == 0) {
						var shot_at = 2;
						if (sprite_index != sCompoundBowChargedShotBR) {
							sprite_index = sCompoundBowChargedShotBR;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = 200;
						};
						if (re_anim == 1) {
							re_anim = -1;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = 200;
							oArcherAttack.batk_ii_bonus = 45;
							oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
						};
						if (sprite_index == sCompoundBowChargedShotBR && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
							shot = 1;
							var xx = oArcher.x + lengthdir_x(70, image_angle);
							var yy = oArcher.y + lengthdir_y(70, image_angle);
							var arrow = instance_create_depth(xx, yy, 150, oArcherChargedArrow);
							arrow.image_angle = image_angle + random_range(-acc_arc, acc_arc);
							arrow.direction = arrow.image_angle;
							arrow.speed = 60 * (oArcher.projectile_speed/100);
							arrow.atk_dir = 1;
							oArcherAttack.recoil += 50*((100-oArcher.recoil_reduction)/100);
							audio_play_sound(soBowShot2, 90, false);
						};
					};
					if (sprite_index == sCompoundBowChargedShotAL) {
						sprite_index = sCompoundBowChargedShotAR;
						depth = 200;
					};
					if (sprite_index == sCompoundBowChargedShotBL) {
						sprite_index = sCompoundBowChargedShotBR;
						depth = 200;
					};
				};
				else if (oArcherAttack.current_seq_type == 8) {
					var shot_at = 15;
					if (sprite_index != sCompoundBowExplosiveShotR) {
						sprite_index = sCompoundBowExplosiveShotR;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (sprite_index == sCompoundBowExplosiveShotR && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						var xx = oArcher.x + lengthdir_x(45, image_angle);
						var yy = oArcher.y + lengthdir_y(45, image_angle);
						var arrow = instance_create_depth(xx, yy, 150, oArcherExplosiveArrow);
						arrow.image_angle = image_angle + random_range(-acc_arc, acc_arc);
						arrow.direction = arrow.image_angle;
						arrow.speed = 35 * (oArcher.projectile_speed/100);
						oArcherAttack.recoil += 35*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot1, 90, false);
					};
				};
			};
			else if (owner.bot_dir == "left") {
				x = oArcher.x - 5;
				y = oArcher.y + 25;
				if (global.aim_mode == 0) {
					image_angle = point_direction(oArcher.x, oArcher.y, mouse_x, oArcher.y);
				};
				else {
					image_angle = point_direction(oArcherAttack.x, oArcherAttack.y, oArcher.x, oArcher.y);
				};
				if (oArcherAttack.current_seq_type == 1 || oArcherAttack.current_seq_type == 4 || oArcherAttack.current_seq_type == 6) {
					if (oArcherAttack.chain == 0 || oArcherAttack.chain == 1) {
						var shot_at = 15;
						if (sprite_index != sCompoundBowODAShot1L) {
							sprite_index = sCompoundBowODAShot1L;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = -100;
						};
						if (re_anim == 1) {
							re_anim = -1;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = -100;
						};
						if (sprite_index == sCompoundBowODAShot1L && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
							shot = 1;
							var xx = oArcher.x + lengthdir_x(-90, image_angle);
							var yy = oArcher.y + lengthdir_y(-90, image_angle);
							var arrow = instance_create_depth(xx, yy, -150, oArcherODAArrow);
							arrow.image_angle = (image_angle + 180) + random_range(-acc_arc, acc_arc);
							arrow.direction = arrow.image_angle;
							arrow.speed = 40 * (oArcher.projectile_speed/100);
							arrow.atk_dir = 2;
							oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
							audio_play_sound(soBowShot1, 90, false);
						};
						if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
							shot_no = 2;
							anim = 1;
							re_anim = 1;
							shot = 0;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							with (oArcherAttack) {
								alarm_set(0, room_speed * 2);
							};
							oArcherAttack.batk_ii_bonus = 10;
							oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
						};
					};
					else if (oArcherAttack.chain == 2) {
						var shot_at = 11;
						if (sprite_index != sCompoundBowODAShot2L) {
							sprite_index = sCompoundBowODAShot2L;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = -100;
						};
						if (re_anim == 1) {
							re_anim = -1;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = -100;
						};
						if (sprite_index == sCompoundBowODAShot2L && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
							shot = 1;
							var xx = oArcher.x + lengthdir_x(-80, image_angle);
							var yy = oArcher.y + lengthdir_y(-80, image_angle);
							var arrow = instance_create_depth(xx, yy, -150, oArcherODAArrow);
							arrow.image_angle = (image_angle + 180) + random_range(-acc_arc, acc_arc);
							arrow.direction = arrow.image_angle;
							arrow.speed = 40 * (oArcher.projectile_speed/100);
							arrow.atk_dir = 2;
							oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
							audio_play_sound(soBowShot1, 90, false);
						};
						if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
							shot_no = 2;
							anim = 1;
							re_anim = 1;
							shot = 0;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							with (oArcherAttack) {
								alarm_set(0, room_speed * 2);
							};
							oArcherAttack.batk_ii_bonus = 10;
							oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
						};
					};
					else if (oArcherAttack.chain == 3) {
						var shot_at1 = 8;
						var shot_at2 = 16;
						if (sprite_index != sCompoundBowODAShot3L) {
							sprite_index = sCompoundBowODAShot3L;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = -100;
						};
						if (re_anim == 1) {
							re_anim = -1;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = -100;
						};
						if (sprite_index == sCompoundBowODAShot3L && ((image_index >= shot_at1 - 1 * (oArcher.atk_spd/100) && shot == 0) || (image_index >= shot_at2 - 1 * (oArcher.atk_spd/100) && shot == 1)) && (anim == -1 || re_anim == -1)) {
							if (shot == 1) {
								shot = 2;
							};
							else if (shot == 0) {
								shot = 1;
							};
							var xx = oArcher.x + lengthdir_x(-80, image_angle);
							var yy = oArcher.y + lengthdir_y(-80, image_angle);
							var arrow = instance_create_depth(xx, yy, -150, oArcherODAArrow);
							arrow.image_angle = (image_angle + 180) + random_range(-acc_arc, acc_arc);
							arrow.direction = arrow.image_angle;
							arrow.speed = 40 * (oArcher.projectile_speed/100);
							arrow.atk_dir = 2;
							oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
							audio_play_sound(soBowShot1, 90, false);
						};
						if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
							shot_no = 2;
							anim = 1;
							re_anim = 1;
							shot = 0;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							with (oArcherAttack) {
								alarm_set(0, room_speed * 2);
							};
							oArcherAttack.batk_ii_bonus = 10;
							oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
						};
					};
					else if (oArcherAttack.chain == 4) {
						var shot_at = 17;
						if (sprite_index != sCompoundBowODAShot4L) {
							sprite_index = sCompoundBowODAShot4L;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = -100;
						};
						if (re_anim == 1) {
							re_anim = -1;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = -100;
						};
						if (sprite_index == sCompoundBowODAShot4L && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
							shot = 1;
							var xx = oArcher.x + lengthdir_x(-80, image_angle);
							var yy = oArcher.y + lengthdir_y(-80, image_angle);
							var arrow = instance_create_depth(xx, yy, -150, oArcherODAArrow);
							arrow.image_angle = (image_angle + 180) + random_range(-acc_arc, acc_arc);
							arrow.direction = arrow.image_angle;
							arrow.speed = 40 * (oArcher.projectile_speed/100);
							arrow.atk_dir = 2;
							oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
							audio_play_sound(soBowShot1, 90, false);
						};
						if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
							shot_no = 2;
							anim = 1;
							re_anim = 1;
							shot = 0;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							with (oArcherAttack) {
								alarm_set(0, room_speed * 2);
							};
							oArcherAttack.batk_ii_bonus = 10;
							oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
						};
					};
					else if (oArcherAttack.chain == 5) {
						if (((oArcherAttack.current_seq_type == 1 || oArcherAttack.current_seq_type == 4) && oArcherAttack.os5_unlocked == 1) || (oArcherAttack.current_seq_type == 6 && oArcherAttack.as5_unlocked == 1)) {
							var shot_at = 14;
							if (sprite_index != sCompoundBowODAShot5L) {
								sprite_index = sCompoundBowODAShot5L;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = -100;
							};
							if (re_anim == 1) {
								re_anim = -1;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = -100;
							};
							if (sprite_index == sCompoundBowODAShot5L && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot = 0) {
								shot = 1;
								var spread_cnt = 3;
								var base_angle = 15 * ((100-oArcher.mprj_angle_reduction)/100);
								var xx = oArcher.x + lengthdir_x(-80, image_angle);
								var yy = oArcher.y + lengthdir_y(-80, image_angle);
								for (var i = 0; i < spread_cnt; i++) {
									var arrow = instance_create_depth(xx, yy, -150, oArcherODAArrow);
									arrow.image_angle = ((image_angle + 180)+(base_angle*(i-floor(spread_cnt/2)))) + random_range(-acc_arc, acc_arc);
									arrow.direction = arrow.image_angle;
									arrow.speed = 40 * (oArcher.projectile_speed/100);
									arrow.atk_dir = 2;
								};
								oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
								audio_play_sound(soBowShot1, 90, false);
							};
							if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
								shot_no = 2;
								anim = 1;
								re_anim = 1;
								shot = 0;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								with (oArcherAttack) {
									alarm_set(0, room_speed * 2);
								};
								oArcherAttack.batk_ii_bonus = 10;
								oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
							};
						};
						else {
							var shot_at = 17;
							if (sprite_index != sCompoundBowODAShot4L) {
								sprite_index = sCompoundBowODAShot4L;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = -100;
							};
							if (re_anim == 1) {
								re_anim = -1;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = -100;
							};
							if (sprite_index == sCompoundBowODAShot4L && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
								shot = 1;
								var xx = oArcher.x + lengthdir_x(-80, image_angle);
								var yy = oArcher.y + lengthdir_y(-80, image_angle);
								var arrow = instance_create_depth(xx, yy, -150, oArcherODAArrow);
								arrow.image_angle = (image_angle + 180) + random_range(-acc_arc, acc_arc);
								arrow.direction = arrow.image_angle;
								arrow.speed = 40 * (oArcher.projectile_speed/100);
								arrow.atk_dir = 2;
								oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
								audio_play_sound(soBowShot1, 90, false);
							};
							if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
								shot_no = 2;
								anim = 1;
								re_anim = 1;
								shot = 0;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								with (oArcherAttack) {
									alarm_set(0, room_speed * 2);
								};
								oArcherAttack.batk_ii_bonus = 10;
								oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
							};
						};
					};
					else if (oArcherAttack.chain >= 6) {
						if (((oArcherAttack.current_seq_type == 1 || oArcherAttack.current_seq_type == 4) && oArcherAttack.os6_unlocked == 1) || (oArcherAttack.current_seq_type == 6 && oArcherAttack.as6_unlocked == 1)) {
							var shot_at = 16;
							if (sprite_index != sCompoundBowODAShot6L) {
								sprite_index = sCompoundBowODAShot6L;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = -100;
							};
							if (re_anim == 1) {
								re_anim = -1;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = -100;
							};
							if (sprite_index == sCompoundBowODAShot6L) {
								if (image_index >= 10 - 1 * (oArcher.atk_spd/100) && depth != 200) {
									depth = 200;
								};
							};
							if (sprite_index == sCompoundBowODAShot6L && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
								shot = 1;
								var xx = oArcher.x + lengthdir_x(-80, image_angle);
								var yy = oArcher.y + lengthdir_y(-80, image_angle);
								var arrow = instance_create_depth(xx, yy, -150, oArcherODAArrow);
								arrow.image_angle = (image_angle + 180) + random_range(-acc_arc, acc_arc);
								arrow.direction = arrow.image_angle;
								arrow.speed = 40 * (oArcher.projectile_speed/100);
								arrow.atk_dir = 2;
								oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
								audio_play_sound(soBowShot1, 90, false);
							};
							if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
								shot_no = 2;
								anim = 1;
								re_anim = 1;
								shot = 0;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								with (oArcherAttack) {
									alarm_set(0, room_speed * 2);
								};
								oArcherAttack.batk_ii_bonus = 10;
								oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
							};
						};
						else if (((oArcherAttack.current_seq_type == 1 || oArcherAttack.current_seq_type == 4) && oArcherAttack.os5_unlocked == 1) || (oArcherAttack.current_seq_type == 6 && oArcherAttack.as5_unlocked == 1)) {
							var shot_at = 14;
							if (sprite_index != sCompoundBowODAShot5L) {
								sprite_index = sCompoundBowODAShot5L;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = -100;
							};
							if (re_anim == 1) {
								re_anim = -1;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = -100;
							};
							if (sprite_index == sCompoundBowODAShot5L && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot = 0) {
								shot = 1;
								var spread_cnt = 3;
								var base_angle = 15 * ((100-oArcher.mprj_angle_reduction)/100);
								var xx = oArcher.x + lengthdir_x(-80, image_angle);
								var yy = oArcher.y + lengthdir_y(-80, image_angle);
								for (var i = 0; i < spread_cnt; i++) {
									var arrow = instance_create_depth(xx, yy, -150, oArcherODAArrow);
									arrow.image_angle = ((image_angle + 180)+(base_angle*(i-floor(spread_cnt/2)))) + random_range(-acc_arc, acc_arc);
									arrow.direction = arrow.image_angle;
									arrow.speed = 40 * (oArcher.projectile_speed/100);
									arrow.atk_dir = 2;
								};
								oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
								audio_play_sound(soBowShot1, 90, false);
							};
							if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
								shot_no = 2;
								anim = 1;
								re_anim = 1;
								shot = 0;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								with (oArcherAttack) {
									alarm_set(0, room_speed * 2);
								};
								oArcherAttack.batk_ii_bonus = 10;
								oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
							};
						};
						else {
							var shot_at = 17;
							if (sprite_index != sCompoundBowODAShot4L) {
								sprite_index = sCompoundBowODAShot4L;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = -100;
							};
							if (re_anim == 1) {
								re_anim = -1;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								depth = -100;
							};
							if (sprite_index == sCompoundBowODAShot4L && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
								shot = 1;
								var xx = oArcher.x + lengthdir_x(-80, image_angle);
								var yy = oArcher.y + lengthdir_y(-80, image_angle);
								var arrow = instance_create_depth(xx, yy, -150, oArcherODAArrow);
								arrow.image_angle = (image_angle + 180) + random_range(-acc_arc, acc_arc);
								arrow.direction = arrow.image_angle;
								arrow.speed = 40 * (oArcher.projectile_speed/100);
								arrow.atk_dir = 2;
								oArcherAttack.recoil += 10*((100-oArcher.recoil_reduction)/100);
								audio_play_sound(soBowShot1, 90, false);
							};
							if (oArcherAttack.current_seq_type == 4 && shot_no == 1 && image_speed == 0) {
								shot_no = 2;
								anim = 1;
								re_anim = 1;
								shot = 0;
								if (anim == 1) {
									image_speed = 1 * (oArcher.atk_spd/100);
									image_index = 0;
								};
								with (oArcherAttack) {
									alarm_set(0, room_speed * 2);
								};
								oArcherAttack.batk_ii_bonus = 10;
								oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
							};
						};
					};
				};
				else if (oArcherAttack.current_seq_type == 2) {
					var shot_at = 23;
					if (sprite_index != sCompoundBowHeavyShotL) {
						sprite_index = sCompoundBowHeavyShotL;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (sprite_index == sCompoundBowHeavyShotL && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						var xx = oArcher.x + lengthdir_x(-70, image_angle);
						var yy = oArcher.y + lengthdir_y(-70, image_angle);
						var arrow = instance_create_depth(xx, yy, -150, oArcherHeavyArrow);
						arrow.image_angle = (image_angle + 180) + random_range(-acc_arc, acc_arc);
						arrow.direction = arrow.image_angle;
						arrow.speed = 35 * (oArcher.projectile_speed/100);
						arrow.atk_dir = 2;
						oArcherAttack.recoil += 25*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot1, 90, false);
					};
				};
				else if (oArcherAttack.current_seq_type == 3) {
					var shot_at = 10;
					var max_shot = round((((100+oArcher.basic_atk_eff)/100) * 2 + 1*floor(oArcherController.type3_level/5))) + ceil(oArcherAttack.last_chain/2);
					if (oArcherAttack.talent_e2 == 1 && oArcherAttack.talent_e2_pt == 0 && image_speed != 0) {
						oArcherAttack.talent_e2 = -1;
						oArcher.atk_spd += max_shot * 3;
						oArcherAttack.talent_e2_pt = max_shot * 3;
					};
					if (sprite_index != sCompoundBowMultiShotAL && phase == 1) {
						sprite_index = sCompoundBowMultiShotAL;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (sprite_index == sCompoundBowMultiShotAL && phase == 0) {
						sprite_index = sCompoundBowMultiShotBL;
						anim = 1;
						re_anim = 1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						phase = 2;
						alarm_set(0, -1);
					};
					if (sprite_index == sCompoundBowMultiShotBL && phase == 0) {
						if (shot_no < max_shot - 1) {
							anim = 1;
							re_anim = 1;
							shot_no += 1;
							phase = 2;
							shot = 0;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							alarm_set(0, -1);
							oArcherAttack.batk_ii_bonus = 15;
							oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
						};
						else {
							phase = -1;
							shot_no = 0;
							oArcherAttack.last_chain = 0;
							with (oArcherAttack) {
								alarm_set(0, room_speed * 2);
							};
							if (oArcher.pinpoint_mode == 1 && oArcherAttack.pinpoint_buff == 1) {
								oArcherAttack.pinpoint_buff = 0;
								oArcher.atk_spd -= 20;
							};
							if (oArcherAttack.talent_e2 == -1 && oArcherAttack.talent_e2_pt != 0) {
								oArcherAttack.talent_e2 = 1;
								oArcher.atk_spd -= oArcherAttack.talent_e2_pt;
								oArcherAttack.talent_e2_pt = 0;
							};
						};
					};
					if (sprite_index == sCompoundBowMultiShotBL && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						var xx = oArcher.x + lengthdir_x(-70, image_angle);
						var yy = oArcher.y + lengthdir_y(-70, image_angle);
						var arrow = instance_create_depth(xx, yy, -150, oArcherMPArrow);
						arrow.image_angle = (image_angle + 180) + random_range(-acc_arc, acc_arc);
						arrow.direction = arrow.image_angle;
						arrow.speed = 40 * (oArcher.projectile_speed/100);
						arrow.atk_dir = 2;
						oArcherAttack.recoil += 15*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot1, 90, false);
						oArcher.hspeed += oArcher.spd/3;
						oArcher.last_spd = oArcher.hspeed;
						oArcher.stop = 1;
						if (oArcher.pinpoint_mode == 1) {
							oArcher.last_spd = 0;
							oArcher.stop = 0;
							oArcher.hspeed = 0;
							oArcher.vspeed = 0;
						};
					};
					if (sprite_index == sCompoundBowMultiShotAR) {
						sprite_index = sCompoundBowMultiShotAL;
						depth = -100;
					};
					if (sprite_index == sCompoundBowMultiShotBR) {
						sprite_index = sCompoundBowMultiShotBL;
						depth = -100;
					};
				};
				else if (oArcherAttack.current_seq_type == 5) {
					var shot_at = 16;
					if (oArcher.talent_c4 == 1) {
						var spread_cnt = round((((100+oArcher.basic_atk_eff)/100) * 3 + 1*floor(oArcherController.type5_level/5))) + ceil(oArcherAttack.last_chain/2) + (oArcher.jumping * 2);
					};
					else {
						var spread_cnt = round((((100+oArcher.basic_atk_eff)/100) * 3 + 1*floor(oArcherController.type5_level/5))) + ceil(oArcherAttack.last_chain/2) + oArcher.jumping;
					};
					if (sprite_index != sCompoundBowParallelShotL) {
						sprite_index = sCompoundBowParallelShotL;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (sprite_index == sCompoundBowParallelShotL && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						if (oArcher.pinpoint_mode == 1) {
							oArcher.mprj_angle_reduction += 35;
						};
						var base_angle = 12 * ((100-oArcher.mprj_angle_reduction)/100);
						var xx = oArcher.x + lengthdir_x(-55, image_angle);
						var yy = oArcher.y + lengthdir_y(-55, image_angle);
						for (var i = 0; i < spread_cnt; i++) {
							var arrow = instance_create_depth(xx, yy, -150, oArcherMPArrow);
							arrow.image_angle = ((image_angle + 180)+(base_angle*(i-floor(spread_cnt/2)))) + random_range(-acc_arc, acc_arc);
							arrow.direction = arrow.image_angle;
							arrow.speed = 40 * (oArcher.projectile_speed/100);
							arrow.atk_dir = 2;
						};
						oArcherAttack.recoil += 25*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot1, 90, false);
						oArcherAttack.last_chain = 0;
						if (oArcher.pinpoint_mode == 1) {
							oArcher.mprj_angle_reduction -= 35;
						};
					};
				};
				else if (oArcherAttack.current_seq_type == 7) {
					if (oArcherAttack.charging == 1) {
						if (sprite_index != sCompoundBowChargedShotAL) {
							sprite_index = sCompoundBowChargedShotAL;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = -100;
						};
						if (re_anim == 1) {
							re_anim = -1;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = -100;
						};
					};
					else if (oArcherAttack.charging == 0) {
						var shot_at = 2;
						if (sprite_index != sCompoundBowChargedShotBL) {
							sprite_index = sCompoundBowChargedShotBL;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = -100;
						};
						if (re_anim == 1) {
							re_anim = -1;
							if (anim == 1) {
								image_speed = 1 * (oArcher.atk_spd/100);
								image_index = 0;
							};
							depth = -100;
							oArcherAttack.batk_ii_bonus = 45;
							oArcher.ignore_interruption += oArcherAttack.batk_ii_bonus;
						};
						if (sprite_index == sCompoundBowChargedShotBL && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
							shot = 1;
							var xx = oArcher.x + lengthdir_x(-70, image_angle);
							var yy = oArcher.y + lengthdir_y(-70, image_angle);
							var arrow = instance_create_depth(xx, yy, -150, oArcherChargedArrow);
							arrow.image_angle = (image_angle + 180) + random_range(-acc_arc, acc_arc);
							arrow.direction = arrow.image_angle;
							arrow.speed = 60 * (oArcher.projectile_speed/100);
							arrow.atk_dir = 2;
							oArcherAttack.recoil += 50*((100-oArcher.recoil_reduction)/100);
							audio_play_sound(soBowShot2, 90, false);
						};
					};
					if (sprite_index == sCompoundBowChargedShotAR) {
						sprite_index = sCompoundBowChargedShotAL;
						depth = -100;
					};
					if (sprite_index == sCompoundBowChargedShotBR) {
						sprite_index = sCompoundBowChargedShotBL;
						depth = -100;
					};
				};
				else if (oArcherAttack.current_seq_type == 8) {
					var shot_at = 15;
					if (sprite_index != sCompoundBowExplosiveShotL) {
						sprite_index = sCompoundBowExplosiveShotL;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (sprite_index == sCompoundBowExplosiveShotL && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						var xx = oArcher.x + lengthdir_x(-45, image_angle);
						var yy = oArcher.y + lengthdir_y(-45, image_angle);
						var arrow = instance_create_depth(xx, yy, -150, oArcherExplosiveArrow);
						arrow.image_angle = (image_angle + 180) + random_range(-acc_arc, acc_arc);
						arrow.direction = arrow.image_angle;
						arrow.speed = 35 * (oArcher.projectile_speed/100);
						oArcherAttack.recoil += 35*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot1, 90, false);
					};
				};
			};
			
			if (anim == 1) {
				anim = -1;
				image_index = 0;
			};
			if (image_index >= image_number - 1 * (oArcher.atk_spd/100) && anim == -1) {
				image_index = image_number - 1;
				image_speed = 0;
				anim = 0;
				re_anim = 0;
				phase = 0;
				alarm_set(0, room_speed * 0.2);
				if ((oArcherAttack.current_seq_type == 4 && shot_no == 0) || (oArcherAttack.current_seq_type == 7 && oArcherAttack.charging == 1)) {
					shot_no = 1;
					alarm_set(0, -1);
				};
				oArcher.ignore_interruption -= oArcherAttack.batk_ii_bonus;
				oArcherAttack.batk_ii_bonus = 0;
			};
		};
		else if (oArcher.attacking == 2) {
			var acc_arc = oArcherAttack.crosshair_acc;
			if (owner.bot_dir == "right") {
				x = oArcher.x + 5;
				y = oArcher.y + 25;
				if (global.aim_mode == 0) {
					image_angle = point_direction(oArcher.x, oArcher.y, mouse_x, oArcher.y);
				};
				else {
					image_angle = point_direction(oArcher.x, oArcher.y, oArcherAttack.x, oArcherAttack.y);
				};
				if (oArcherAttack.spmv_type == 1 && oArcher.joint_atk == 0) {
					var shot_at = 16;
					if (sprite_index != sCompoundBowFlickShotR) {
						sprite_index = sCompoundBowFlickShotR;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (sprite_index == sCompoundBowFlickShotR) {
						if (image_index >= 10 - 1 * (oArcher.atk_spd/100) && depth != -100) {
							depth = -100;
						};
					};
					if (sprite_index == sCompoundBowFlickShotR && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						var xx = oArcher.x + lengthdir_x(30, image_angle);
						var yy = oArcher.y + lengthdir_y(30, image_angle);
						var arrow = instance_create_depth(xx, yy, -150, oArcherODAArrow);
						arrow.image_angle = image_angle + random_range(-acc_arc, acc_arc);
						arrow.direction = arrow.image_angle;
						arrow.speed = 40 * (oArcher.projectile_speed/100);
						arrow.atk_dir = 1;
						oArcherAttack.recoil += 15*((100-oArcher.recoil_reduction)/100);
						if (oArcher.perfect_flick == 0) {
							audio_play_sound(soBowShot1, 90, false);
						};
						else {
							audio_play_sound(soBowShot2, 90, false);
							oArcher.perfect_flick = 0;
						};
					};
				};
				else if (oArcherAttack.spmv_type == 2 && oArcherAttack.ec_shot_unlocked == 0 && oArcher.joint_atk == 0) {
					if (global.aim_mode == 0) {
						image_angle = 330;
					};
					var shot_at = 16;
					var spread_cnt = round(((100+oArcher.sp_move_eff)/100) * 3 + ((oArcherController.spmv_adj1-1)*2));
					if (sprite_index != sCompoundBowParallelShotR) {
						sprite_index = sCompoundBowParallelShotR;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (sprite_index == sCompoundBowParallelShotR && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						if (oArcher.pinpoint_mode == 1) {
							oArcher.mprj_angle_reduction += 35;
						};
						var base_angle = 12 * ((100-oArcher.mprj_angle_reduction)/100);
						var xx = oArcher.x + lengthdir_x(55, image_angle);
						var yy = oArcher.y + lengthdir_y(55, image_angle);
						for (var i = 0; i < spread_cnt; i++) {
							var arrow = instance_create_depth(xx, yy, 150, oArcherSpikeArrow);
							arrow.image_angle = (image_angle+(base_angle*(i-floor(spread_cnt/2)))) + random_range(-acc_arc, acc_arc);
							arrow.direction = arrow.image_angle;
							arrow.speed = 40 * (oArcher.projectile_speed/100);
							arrow.atk_dir = 1;
						};
						oArcherAttack.recoil += 25*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot1, 90, false);
						if (oArcher.pinpoint_mode == 1) {
							oArcher.mprj_angle_reduction -= 35;
						};
					};
				};
				else if (oArcherAttack.spmv_type == 2 && oArcherAttack.ec_shot_unlocked == 1 && oArcher.joint_atk == 0) {
					if (global.aim_mode == 0) {
						image_angle = 330;
					};
					var shot_at = 16;
					if (sprite_index != sCompoundBowElectricShotR) {
						sprite_index = sCompoundBowElectricShotR;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (sprite_index == sCompoundBowElectricShotR && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						var xx = oArcher.x + lengthdir_x(60, image_angle);
						var yy = oArcher.y + lengthdir_y(60, image_angle);
						var arrow = instance_create_depth(xx, yy, 150, oArcherElectricArrow);
						arrow.image_angle = image_angle + random_range(-acc_arc, acc_arc);
						arrow.direction = arrow.image_angle;
						arrow.speed = 40 * (oArcher.projectile_speed/100);
						oArcherAttack.recoil += 35*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot3, 90, false);
					};
				};
				else if (oArcher.joint_atk == -1) {
					var shot_at = 23;
					if (sprite_index != sCompoundBowHeavyShotR) {
						sprite_index = sCompoundBowHeavyShotR;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (sprite_index == sCompoundBowHeavyShotR && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						var xx = oArcher.x + lengthdir_x(70, image_angle);
						var yy = oArcher.y + lengthdir_y(70, image_angle);
						var arrow = instance_create_depth(xx, yy, 150, oArcherHeavyArrow);
						arrow.image_angle = image_angle + random_range(-acc_arc, acc_arc);
						arrow.direction = arrow.image_angle;
						arrow.speed = 35 * (oArcher.projectile_speed/100);
						arrow.atk_dir = 1;
						oArcherAttack.recoil += 35*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot1, 90, false);
					};
				};
			};
			else if (owner.bot_dir == "left") {
				x = oArcher.x - 5;
				y = oArcher.y + 25;
				if (global.aim_mode == 0) {
					image_angle = point_direction(oArcher.x, oArcher.y, mouse_x, oArcher.y);
				};
				else {
					image_angle = point_direction(oArcherAttack.x, oArcherAttack.y, oArcher.x, oArcher.y);
				};
				if (oArcherAttack.spmv_type == 1 && oArcher.joint_atk == 0) {
					var shot_at = 16;
					if (sprite_index != sCompoundBowFlickShotL) {
						sprite_index = sCompoundBowFlickShotL;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (sprite_index == sCompoundBowFlickShotL) {
						if (image_index >= 10 - 1 * (oArcher.atk_spd/100) && depth != 200) {
							depth = 200;
						};
					};
					if (sprite_index == sCompoundBowFlickShotL && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						var xx = oArcher.x + lengthdir_x(-30, image_angle);
						var yy = oArcher.y + lengthdir_y(-30, image_angle);
						var arrow = instance_create_depth(xx, yy, 150, oArcherODAArrow);
						arrow.image_angle = (image_angle+180) + random_range(-acc_arc, acc_arc);
						arrow.direction = arrow.image_angle;
						arrow.speed = 40 * (oArcher.projectile_speed/100);
						arrow.atk_dir = 2;
						oArcherAttack.recoil += 15*((100-oArcher.recoil_reduction)/100);
						if (oArcher.perfect_flick == 0) {
							audio_play_sound(soBowShot1, 90, false);
						};
						else {
							audio_play_sound(soBowShot2, 90, false);
							oArcher.perfect_flick = 0;
						};
					};
				};
				else if (oArcherAttack.spmv_type == 2 && oArcherAttack.ec_shot_unlocked == 0 && oArcher.joint_atk == 0) {
					if (global.aim_mode == 0) {
						image_angle = 30;
					};
					var shot_at = 16;
					var spread_cnt = round(((100+oArcher.sp_move_eff)/100) * 3 + ((oArcherController.spmv_adj1-1)*2));
					if (sprite_index != sCompoundBowParallelShotL) {
						sprite_index = sCompoundBowParallelShotL;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (sprite_index == sCompoundBowParallelShotL && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						if (oArcher.pinpoint_mode == 1) {
							oArcher.mprj_angle_reduction += 35;
						};
						var base_angle = 12 * ((100-oArcher.mprj_angle_reduction)/100);
						var xx = oArcher.x + lengthdir_x(-55, image_angle);
						var yy = oArcher.y + lengthdir_y(-55, image_angle);
						for (var i = 0; i < spread_cnt; i++) {
							var arrow = instance_create_depth(xx, yy, 150, oArcherSpikeArrow);
							arrow.image_angle = ((image_angle+180)+(base_angle*(i-floor(spread_cnt/2)))) + random_range(-acc_arc, acc_arc);
							arrow.direction = arrow.image_angle;
							arrow.speed = 40 * (oArcher.projectile_speed/100);
							arrow.atk_dir = 2;
						};
						oArcherAttack.recoil += 25*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot1, 90, false);
						if (oArcher.pinpoint_mode == 1) {
							oArcher.mprj_angle_reduction -= 35;
						};
					};
				};
				else if (oArcherAttack.spmv_type == 2 && oArcherAttack.ec_shot_unlocked == 1 && oArcher.joint_atk == 0) {
					if (global.aim_mode == 0) {
						image_angle = 30;
					};
					var shot_at = 16;
					if (sprite_index != sCompoundBowElectricShotL) {
						sprite_index = sCompoundBowElectricShotL;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (sprite_index == sCompoundBowElectricShotL && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						var xx = oArcher.x + lengthdir_x(-60, image_angle);
						var yy = oArcher.y + lengthdir_y(-60, image_angle);
						var arrow = instance_create_depth(xx, yy, -150, oArcherElectricArrow);
						arrow.image_angle = (image_angle+180) + random_range(-acc_arc, acc_arc);
						arrow.direction = arrow.image_angle;
						arrow.speed = 40 * (oArcher.projectile_speed/100);
						oArcherAttack.recoil += 35*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot3, 90, false);
					};
				};
				else if (oArcher.joint_atk == -1) {
					var shot_at = 23;
					if (sprite_index != sCompoundBowHeavyShotL) {
						sprite_index = sCompoundBowHeavyShotL;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (sprite_index == sCompoundBowHeavyShotL && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						var xx = oArcher.x + lengthdir_x(-70, image_angle);
						var yy = oArcher.y + lengthdir_y(-70, image_angle);
						var arrow = instance_create_depth(xx, yy, -150, oArcherHeavyArrow);
						arrow.image_angle = (image_angle + 180) + random_range(-acc_arc, acc_arc);
						arrow.direction = arrow.image_angle;
						arrow.speed = 35 * (oArcher.projectile_speed/100);
						arrow.atk_dir = 2;
						oArcherAttack.recoil += 35*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot1, 90, false);
					};
				};
			};
			
			if (anim == 1) {
				anim = -1;
				image_index = 0;
			};
			if (image_index >= image_number - 1 * (oArcher.atk_spd/100) && anim == -1) {
				image_index = image_number - 1;
				image_speed = 0;
				anim = 0;
				re_anim = 0;
				phase = 0;
				alarm_set(1, room_speed * 0.2);
				oArcher.ignore_interruption -= oArcherAttack.spmv_ii_bonus;
				oArcherAttack.spmv_ii_bonus = 0;
			};
		};
		else if (oArcher.attacking == 3) {
			var acc_arc = oArcherAttack.crosshair_acc;
			if (owner.bot_dir == "right") {
				x = oArcher.x + 5;
				y = oArcher.y + 25;
				if (global.aim_mode == 0) {
					image_angle = point_direction(oArcher.x, oArcher.y, mouse_x, oArcher.y);
				};
				else {
					image_angle = point_direction(oArcher.x, oArcher.y, oArcherAttack.x, oArcherAttack.y);
				};
				if (oArcherAttack.ultimate_type == 1) {
					if (global.aim_mode == 1) {
						image_angle = 0;
					};
					var shot_at = 25;
					var spread_cnt = 10;
					if (sprite_index != sCompoundBowCloudburstVolleyR) {
						sprite_index = sCompoundBowCloudburstVolleyR;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (sprite_index == sCompoundBowCloudburstVolleyR && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						var base_angle = 6 * ((100-oArcher.mprj_angle_reduction)/100);
						var xx = oArcher.x + lengthdir_x(75, image_angle+90);
						var yy = oArcher.y + lengthdir_y(75, image_angle+90);
						for (var i = 0; i < spread_cnt; i++) {
							var arrow = instance_create_depth(xx, yy, 150, oArcherCVArrow1);
							arrow.image_angle = ((image_angle+90)+(base_angle*(i-floor(spread_cnt/2)))) + random_range(-acc_arc, acc_arc);
							arrow.direction = arrow.image_angle;
							arrow.speed = 40 * (oArcher.projectile_speed/100);
							arrow.atk_dir = 0;
						};
						oArcherAttack.recoil += 40*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot2, 90, false);
						if (global.aim_mode == 0) {
							instance_create_depth(oArcher.x + 500, -100, -150, oBowCloudburstVolleyArea);
						};
						else if (global.aim_mode == 1) {
							instance_create_depth(oArcherAttack.x, -100, -150, oBowCloudburstVolleyArea);
						};
					};
				};
				else if (oArcherAttack.ultimate_type == 2) {
					var shot_at = 16;
					if (sprite_index != sCompoundBowElectricShotR) {
						sprite_index = sCompoundBowElectricShotR;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = 200;
					};
					if (sprite_index == sCompoundBowElectricShotR && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						var xx = oArcher.x + lengthdir_x(10, image_angle);
						var yy = oArcher.y + lengthdir_y(10, image_angle);
						var arrow = instance_create_depth(xx, yy, 150, oArcherHBArrow);
						arrow.image_angle = image_angle + random_range(-acc_arc, acc_arc);
						arrow.direction = arrow.image_angle;
						arrow.speed = 40 * (oArcher.projectile_speed/100);
						arrow.atk_dir = 1;
						oArcherAttack.recoil += 35*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot2, 90, false);
					};
				};
				else if (oArcherAttack.ultimate_type == 3) {
					if (sprite_index != sCompoundBowPinpointShootingAR && phase == 1) {
						sprite_index = sCompoundBowPinpointShootingAR;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
						};
						depth = 200;
					};
					else if (sprite_index != sCompoundBowPinpointShootingBR && phase == 2) {
						sprite_index = sCompoundBowPinpointShootingBR;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
						};
						depth = 200;
					};
					else if (sprite_index == sCompoundBowPinpointShootingAR) {
						if (image_index >= 21 - 1 * (oArcher.atk_spd/100) && sfx == 0) {
							sfx = 1;
							audio_play_sound(soBowDraw, 90, false);
						};
						else if (image_index >= 30 - 1 * (oArcher.atk_spd/100) && sfx == 1) {
							sfx = 2;
							audio_play_sound(soBowSheath, 90, false);
						};
					};
					else if (sprite_index == sCompoundBowPinpointShootingBR) {
						if (anim == 0 && oArcher.attacking != 0) {
							oArcher.attacking = 0;
							oArcher.pinpoint_mode = 0;
						};
						if (image_index >= 12 - 1 * (oArcher.atk_spd/100) && sfx == 0) {
							sfx = 1;
							audio_play_sound(soBowDraw, 90, false);
						};
					};
					else if (sprite_index == sCompoundBowPinpointShootingAL) {
						sprite_index = sCompoundBowPinpointShootingAR;
						depth = 200;
					};
					else if (sprite_index == sCompoundBowPinpointShootingBL) {
						sprite_index = sCompoundBowPinpointShootingBR;
						depth = 200;
					};
				};
			};
			else if (owner.bot_dir == "left") {
				x = oArcher.x - 5;
				y = oArcher.y + 25;
				if (global.aim_mode == 0) {
					image_angle = point_direction(oArcher.x, oArcher.y, mouse_x, oArcher.y);
				};
				else {
					image_angle = point_direction(oArcherAttack.x, oArcherAttack.y, oArcher.x, oArcher.y);
				};
				if (oArcherAttack.ultimate_type == 1) {
					if (global.aim_mode == 1) {
						image_angle = 0;
					};
					var shot_at = 25;
					var spread_cnt = 10;
					if (sprite_index != sCompoundBowCloudburstVolleyL) {
						sprite_index = sCompoundBowCloudburstVolleyL;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (sprite_index == sCompoundBowCloudburstVolleyL && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						var base_angle = 6 * ((100-oArcher.mprj_angle_reduction)/100);
						var xx = oArcher.x + lengthdir_x(-75, image_angle+90);
						var yy = oArcher.y + lengthdir_y(-75, image_angle+90);
						for (var i = 0; i < spread_cnt; i++) {
							var arrow = instance_create_depth(xx, yy, -150, oArcherCVArrow1);
							arrow.image_angle = ((image_angle+90)+(base_angle*(i-floor(spread_cnt/2)))) + random_range(-acc_arc, acc_arc);
							arrow.direction = arrow.image_angle;
							arrow.speed = 40 * (oArcher.projectile_speed/100);
							arrow.atk_dir = 0;
						};
						oArcherAttack.recoil += 40*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot2, 90, false);
						if (global.aim_mode == 0) {
							instance_create_depth(oArcher.x - 500, -100, -150, oBowCloudburstVolleyArea);
						};
						else if (global.aim_mode == 1) {
							instance_create_depth(oArcherAttack.x, -100, -150, oBowCloudburstVolleyArea);
						};
					};
				};
				else if (oArcherAttack.ultimate_type == 2) {
					var shot_at = 16;
					if (sprite_index != sCompoundBowElectricShotL) {
						sprite_index = sCompoundBowElectricShotL;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (re_anim == 1) {
						re_anim = -1;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
							image_index = 0;
						};
						depth = -100;
					};
					if (sprite_index == sCompoundBowElectricShotL && image_index >= shot_at - 1 * (oArcher.atk_spd/100) && (anim == -1 || re_anim == -1) && shot == 0) {
						shot = 1;
						var xx = oArcher.x + lengthdir_x(-10, image_angle);
						var yy = oArcher.y + lengthdir_y(-10, image_angle);
						var arrow = instance_create_depth(xx, yy, -150, oArcherHBArrow);
						arrow.image_angle = (image_angle+180) + random_range(-acc_arc, acc_arc);
						arrow.direction = arrow.image_angle;
						arrow.speed = 40 * (oArcher.projectile_speed/100);
						arrow.atk_dir = 2;
						oArcherAttack.recoil += 35*((100-oArcher.recoil_reduction)/100);
						audio_play_sound(soBowShot2, 90, false);
					};
				};
				else if (oArcherAttack.ultimate_type == 3) {
					if (sprite_index != sCompoundBowPinpointShootingAL && phase == 1) {
						sprite_index = sCompoundBowPinpointShootingAL;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
						};
						depth = -100;
					};
					else if (sprite_index != sCompoundBowPinpointShootingBL && phase == 2) {
						sprite_index = sCompoundBowPinpointShootingBL;
						if (anim == 1) {
							image_speed = 1 * (oArcher.atk_spd/100);
						};
						depth = -100;
					};
					else if (sprite_index == sCompoundBowPinpointShootingAL) {
						if (image_index >= 21 - 1 * (oArcher.atk_spd/100) && sfx == 0) {
							sfx = 1;
							audio_play_sound(soBowDraw, 90, false);
						};
						else if (image_index >= 30 - 1 * (oArcher.atk_spd/100) && sfx == 1) {
							sfx = 2;
							audio_play_sound(soBowSheath, 90, false);
						};
					};
					else if (sprite_index == sCompoundBowPinpointShootingBL) {
						if (anim == 0 && oArcher.attacking != 0) {
							oArcher.attacking = 0;
							oArcher.pinpoint_mode = 0;
						};
						if (image_index >= 12 - 1 * (oArcher.atk_spd/100) && sfx == 0) {
							sfx = 1;
							audio_play_sound(soBowDraw, 90, false);
						};
					};
					else if (sprite_index == sCompoundBowPinpointShootingAR) {
						sprite_index = sCompoundBowPinpointShootingAL;
						depth = -100;
					};
					else if (sprite_index == sCompoundBowPinpointShootingBR) {
						sprite_index = sCompoundBowPinpointShootingBL;
						depth = -100;
					};
				};
			};
			
			if (anim == 1) {
				anim = -1;
				image_index = 0;
			};
			if (image_index >= image_number - 1 * (oArcher.atk_spd/100) && anim == -1) {
				image_index = image_number - 1;
				image_speed = 0;
				anim = 0;
				re_anim = 0;
				phase = 0;
				alarm_set(1, room_speed * 0.2);
				oArcher.fixed_ignore_interruption = 0;
				if (oArcherAttack.ultimate_type == 3) {
					alarm_set(1, 1);
				};
			};
		};
	};
};

if (oArcher.dashing >= 1) {
	if (owner.bot_dir == "right") {
		instance_create_layer (x, y, "EffectLow", oCompoundBowShadow);
	};
	else if (owner.bot_dir == "left") {
		instance_create_layer (x, y, "ObjectHigh", oCompoundBowShadow);
	};
};

if (flash_alpha > 0) {
	flash_alpha -= 0.05;
};
