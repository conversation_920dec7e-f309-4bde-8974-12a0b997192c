global.nexaflux {
	enabled: <PERSON><PERSON><PERSON>,
	auto_destroy: <PERSON><PERSON><PERSON>,
	thread_count: Int,
	ref: Struct.NexaFluxAnim[]
}

enum NEXAFLUX_CURVES {
	LINEAR,
	EASE_IN_QUAD,
	EASE_OUT_QUAD,
	EASE_INOUT_QUAD
}

function nexaflux_init (
	thread_count: Int,
	auto_destroy: Boolean,			// if true, will destroy unused NexaFluxAnim structs when updating their values
) -> Void

constructor NexaFluxPreset (
	_preset_enum: NEXAFLUX_PRESET,	// enum of the preset to use
) -> Struct.NexaFluxPreset

Struct.NexaFluxPreset {
	types: String[],				// 2D array of "translate_x", "translate_y", "rotate", "scale_x", "scale_y", "depth", "alpha"
	values: Real[][][],				// 3D array of real values
	key_times: Real[][],			// 2D array of key times in frames (int)
	curves: NEXAFLUX_CURVES[],		// 1D/2D array of NEXAFLUX_CURVES

	actions: undefined | {			// undefined or an object with the following properties
		func: Function[],
		time: Real[],
		args: Any[][]
	},

	loop_count: undefined | Int		// -1 for infinite loop
}

constructor NexaFluxAction (
	_func: Function,				// function to call
	_time: Real,					// time in frames (int) to call the function
	_args: Any[],					// arguments to pass to the function
) -> Struct.NexaFluxAction

constructor NexaFluxAnim (
	_ref: Id.Instance | Struct,				// instance, struct, or noone
	_is_relative: Boolean,
	_data: NEXAFLUX_PRESET | Struct,		// preset enum or struct of data
) -> Struct.NexaFluxAnim

Struct.NexaFluxAnim {
	ref: Id.Instance | Struct,

	types: String[],				// 1D array of "translate_x", "translate_y", "rotate", "scale_x", "scale_y", "depth", "alpha"
	values: Real[][][],				// 3D array of real values
	key_times: Real[][],			// 2D array of key times in frames (int) 
	curves: NEXAFLUX_CURVES[],		// 1D/2D array of NEXAFLUX_CURVES

	actions: {
		funcs: NEXAFLUX_ACTION[],
		times: Real[],
		args: Any[][],
		done: Boolean[]
	}

	speed: Real,
	time: Real,					// current time in frames
	max_time: Real,				// total time in frames
	value: {					// current value
		[types: String]: Real
	},
	index: {
		[types: String]: Int
	},
	loop_count: Int,
	current_loop: Int,			// current loop count

	debug: Boolean,				// if true, will print debug information to the console
	is_relative: Boolean,
	is_reversed: Boolean,
	is_looping: Boolean,
	skip_update: Boolean,
	key_times_equal: Boolean,

	get_val() -> Real,
	get_x() -> Real,
	get_y() -> Real,
	get_rot() -> Real,
	get_scale_x() -> Real,
	get_scale_y() -> Real,
	get_depth() -> Real,
	get_alpha() -> Real,

	set_values(
		val: { 
			[types: String]: Real 
		}
	) -> Void,

	set_val(
		name: String, 
		val: Real
	) -> Void,

	set_x(
		val: Real
	) -> Void,

	set_y(
		val: Real
	) -> Void,

	set_rot(
		val: Real
	) -> Void,

	set_scale_x(
		val: Real
	) -> Void,

	set_scale_y(
		val: Real
	) -> Void,

	set_depth(
		val: Real
	) -> Void,

	set_alpha(
		val: Real
	) -> Void,

	set_anim_data(
		data_struct: NEXAFLUX_PRESET | Struct.NexaFluxPreset
	) -> Void,

	calc_t(
		current_t: Real, 
		next_t: Real, 
		curve_type: NEXAFLUX_CURVES
	) -> Real,

	calc_cubic_bezier(
		p0: Real, 
		c1: Real, 
		c2: Real, 
		p3: Real, 
		t: Real
	) -> Real,

	play() -> Void,
	pause() -> Void,
	stop() -> Void,

	reset_action() -> Void,
	reset_index() -> Void,
	reset_value() -> Void,
	reset_time() -> Void,

	push_front(
		type: String, 
		value: Real[][], 
		key_times: Real[], 
		curves: NEXAFLUX_CURVES | NEXAFLUX_CURVES[]
	) -> Void,

	push_back(
		type: String, 
		value: Real[][], 
		key_times: Real[], 
		curves: NEXAFLUX_CURVES | NEXAFLUX_CURVES[]
	) -> Void,

	call_action(
		index: Int
	) -> Void,

	morph(
		anim_preset: NEXAFLUX_PRESET | Struct.NexaFluxPreset,
		add_current_anim: Boolean = false,							// if true, will add the current animation values to the new animation values
		morph_time: Real ?? 0,										// time in seconds to morph the animation
		morph_curve: NEXAFLUX_CURVES ?? NEXAFLUX_CURVES.LINEAR		// curve type for the morphing
	)  -> Void,

	update() -> Void
}

function nexaflux_create(
	nexaflux_const: Struct.NexaFluxAnim
) -> Struct.NexaFluxAnim

function nexaflux_destroy(
	nexaflux_const: Struct.NexaFluxAnim
) -> Void

function nexaflux_update() -> Void